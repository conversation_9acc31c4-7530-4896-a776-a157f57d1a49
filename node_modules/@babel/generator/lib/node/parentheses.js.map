{"version": 3, "names": ["_t", "require", "isArrayTypeAnnotation", "isArrowFunctionExpression", "isBinaryExpression", "isCallExpression", "isExportDeclaration", "isForOfStatement", "isIndexedAccessType", "isMemberExpression", "isObjectPattern", "isOptionalMemberExpression", "isYieldExpression", "PRECEDENCE", "Map", "getBinaryPrecedence", "node", "nodeType", "get", "operator", "isTSTypeExpression", "isClassExtendsClause", "parent", "parentType", "type", "superClass", "hasPostfixPart", "object", "callee", "tag", "NullableTypeAnnotation", "FunctionTypeAnnotation", "printStack", "length", "UpdateExpression", "ObjectExpression", "isFirstInContext", "DoExpression", "async", "Binary", "left", "parentPos", "nodePos", "right", "undefined", "UnionTypeAnnotation", "OptionalIndexedAccessType", "objectType", "TSAsExpression", "TSUnionType", "TSInferType", "TSInstantiationExpression", "typeParameters", "BinaryExpression", "stack", "inForStatementInit", "SequenceExpression", "test", "discriminant", "expression", "YieldExpression", "ClassExpression", "UnaryLike", "FunctionExpression", "ArrowFunctionExpression", "ConditionalExpression", "OptionalMemberExpression", "AssignmentExpression", "LogicalExpression", "Identifier", "_node$extra", "extra", "parenthesized", "rightType", "id", "name", "isFollowedByBracket", "computed", "optional", "await", "checkParam", "expressionStatement", "arrowBody", "exportDefault", "forHead", "forInHead", "forOfHead", "i", "declaration", "body", "init", "expressions", "prefix"], "sources": ["../../src/node/parentheses.ts"], "sourcesContent": ["import {\n  isArrayTypeAnnotation,\n  isArrowFunctionExpression,\n  isBinaryExpression,\n  isCallExpression,\n  isExportDeclaration,\n  isForOfStatement,\n  isIndexedAccessType,\n  isMemberExpression,\n  isObjectPattern,\n  isOptionalMemberExpression,\n  isYieldExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nconst PRECEDENCE = new Map([\n  [\"||\", 0],\n  [\"??\", 0],\n  [\"|>\", 0],\n  [\"&&\", 1],\n  [\"|\", 2],\n  [\"^\", 3],\n  [\"&\", 4],\n  [\"==\", 5],\n  [\"===\", 5],\n  [\"!=\", 5],\n  [\"!==\", 5],\n  [\"<\", 6],\n  [\">\", 6],\n  [\"<=\", 6],\n  [\">=\", 6],\n  [\"in\", 6],\n  [\"instanceof\", 6],\n  [\">>\", 7],\n  [\"<<\", 7],\n  [\">>>\", 7],\n  [\"+\", 8],\n  [\"-\", 8],\n  [\"*\", 9],\n  [\"/\", 9],\n  [\"%\", 9],\n  [\"**\", 10],\n]);\n\nfunction getBinaryPrecedence(\n  node: t.Binary | t.TSAsExpression | t.TSSatisfiesExpression,\n  nodeType: string,\n): number;\nfunction getBinaryPrecedence(\n  node: t.Node,\n  nodeType: string,\n): number | undefined;\nfunction getBinaryPrecedence(node: t.Node, nodeType: string) {\n  if (nodeType === \"BinaryExpression\" || nodeType === \"LogicalExpression\") {\n    return PRECEDENCE.get((node as t.Binary).operator);\n  }\n  if (nodeType === \"TSAsExpression\" || nodeType === \"TSSatisfiesExpression\") {\n    return PRECEDENCE.get(\"in\");\n  }\n}\n\nconst enum CheckParam {\n  expressionStatement = 1 << 0,\n  arrowBody = 1 << 1,\n  exportDefault = 1 << 2,\n  forHead = 1 << 3,\n  forInHead = 1 << 4,\n  forOfHead = 1 << 5,\n}\n\nfunction isTSTypeExpression(nodeType: string) {\n  return (\n    nodeType === \"TSAsExpression\" ||\n    nodeType === \"TSSatisfiesExpression\" ||\n    nodeType === \"TSTypeAssertion\"\n  );\n}\n\nconst isClassExtendsClause = (\n  node: t.Node,\n  parent: t.Node,\n): parent is t.Class => {\n  const parentType = parent.type;\n  return (\n    (parentType === \"ClassDeclaration\" || parentType === \"ClassExpression\") &&\n    parent.superClass === node\n  );\n};\n\nconst hasPostfixPart = (node: t.Node, parent: t.Node) => {\n  const parentType = parent.type;\n  return (\n    ((parentType === \"MemberExpression\" ||\n      parentType === \"OptionalMemberExpression\") &&\n      parent.object === node) ||\n    ((parentType === \"CallExpression\" ||\n      parentType === \"OptionalCallExpression\" ||\n      parentType === \"NewExpression\") &&\n      parent.callee === node) ||\n    (parentType === \"TaggedTemplateExpression\" && parent.tag === node) ||\n    parentType === \"TSNonNullExpression\"\n  );\n};\n\nexport function NullableTypeAnnotation(\n  node: t.NullableTypeAnnotation,\n  parent: t.Node,\n): boolean {\n  return isArrayTypeAnnotation(parent);\n}\n\nexport function FunctionTypeAnnotation(\n  node: t.FunctionTypeAnnotation,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  if (printStack.length < 3) return;\n\n  const parentType = parent.type;\n  return (\n    // (() => A) | (() => B)\n    parentType === \"UnionTypeAnnotation\" ||\n    // (() => A) & (() => B)\n    parentType === \"IntersectionTypeAnnotation\" ||\n    // (() => A)[]\n    parentType === \"ArrayTypeAnnotation\" ||\n    // <T>(A: T): (T => T[]) => B => [A, B]\n    (parentType === \"TypeAnnotation\" &&\n      // Check grandparent\n      isArrowFunctionExpression(printStack[printStack.length - 3]))\n  );\n}\n\nexport function UpdateExpression(\n  node: t.UpdateExpression,\n  parent: t.Node,\n): boolean {\n  return hasPostfixPart(node, parent) || isClassExtendsClause(node, parent);\n}\n\nexport function ObjectExpression(\n  node: t.ObjectExpression,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  return isFirstInContext(\n    printStack,\n    CheckParam.expressionStatement | CheckParam.arrowBody,\n  );\n}\n\nexport function DoExpression(\n  node: t.DoExpression,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  // `async do` can start an expression statement\n  return (\n    !node.async && isFirstInContext(printStack, CheckParam.expressionStatement)\n  );\n}\n\nexport function Binary(\n  node: t.Binary | t.TSAsExpression | t.TSSatisfiesExpression,\n  parent: t.Node,\n): boolean | undefined {\n  const parentType = parent.type;\n  if (\n    node.type === \"BinaryExpression\" &&\n    node.operator === \"**\" &&\n    parentType === \"BinaryExpression\" &&\n    parent.operator === \"**\"\n  ) {\n    return parent.left === node;\n  }\n\n  if (isClassExtendsClause(node, parent)) {\n    return true;\n  }\n\n  if (\n    hasPostfixPart(node, parent) ||\n    parentType === \"UnaryExpression\" ||\n    parentType === \"SpreadElement\" ||\n    parentType === \"AwaitExpression\"\n  ) {\n    return true;\n  }\n\n  const parentPos = getBinaryPrecedence(parent, parentType);\n  if (parentPos != null) {\n    const nodePos = getBinaryPrecedence(node, node.type);\n    if (\n      // Logical expressions with the same precedence don't need parens.\n      (parentPos === nodePos &&\n        parentType === \"BinaryExpression\" &&\n        parent.right === node) ||\n      parentPos > nodePos\n    ) {\n      return true;\n    }\n  }\n\n  return undefined;\n}\n\nexport function UnionTypeAnnotation(\n  node: t.UnionTypeAnnotation,\n  parent: t.Node,\n): boolean {\n  const parentType = parent.type;\n  return (\n    parentType === \"ArrayTypeAnnotation\" ||\n    parentType === \"NullableTypeAnnotation\" ||\n    parentType === \"IntersectionTypeAnnotation\" ||\n    parentType === \"UnionTypeAnnotation\"\n  );\n}\n\nexport { UnionTypeAnnotation as IntersectionTypeAnnotation };\n\nexport function OptionalIndexedAccessType(\n  node: t.OptionalIndexedAccessType,\n  parent: t.Node,\n): boolean {\n  return isIndexedAccessType(parent) && parent.objectType === node;\n}\n\nexport function TSAsExpression(\n  node: t.TSAsExpression | t.TSSatisfiesExpression,\n  parent: t.Node,\n): boolean {\n  if (\n    parent.type === \"BinaryExpression\" &&\n    (parent.operator === \"|\" || parent.operator === \"&\") &&\n    node === parent.left\n  ) {\n    return true;\n  }\n  return Binary(node, parent);\n}\n\nexport { TSAsExpression as TSSatisfiesExpression };\n\nexport { UnaryLike as TSTypeAssertion };\n\nexport function TSUnionType(node: t.TSUnionType, parent: t.Node): boolean {\n  const parentType = parent.type;\n  return (\n    parentType === \"TSArrayType\" ||\n    parentType === \"TSOptionalType\" ||\n    parentType === \"TSIntersectionType\" ||\n    parentType === \"TSUnionType\" ||\n    parentType === \"TSRestType\"\n  );\n}\n\nexport { TSUnionType as TSIntersectionType };\n\nexport function TSInferType(node: t.TSInferType, parent: t.Node): boolean {\n  const parentType = parent.type;\n  return parentType === \"TSArrayType\" || parentType === \"TSOptionalType\";\n}\n\nexport function TSInstantiationExpression(\n  node: t.TSInstantiationExpression,\n  parent: t.Node,\n) {\n  const parentType = parent.type;\n  return (\n    (parentType === \"CallExpression\" ||\n      parentType === \"OptionalCallExpression\" ||\n      parentType === \"NewExpression\" ||\n      parentType === \"TSInstantiationExpression\") &&\n    !!parent.typeParameters\n  );\n}\n\nexport function BinaryExpression(\n  node: t.BinaryExpression,\n  parent: t.Node,\n  stack: unknown,\n  inForStatementInit: boolean,\n): boolean {\n  // for ((1 in []);;);\n  // for (var x = (1 in []) in 2);\n  return node.operator === \"in\" && inForStatementInit;\n}\n\nexport function SequenceExpression(\n  node: t.SequenceExpression,\n  parent: t.Node,\n): boolean {\n  const parentType = parent.type;\n  if (\n    // Although parentheses wouldn't hurt around sequence\n    // expressions in the head of for loops, traditional style\n    // dictates that e.g. i++, j++ should not be wrapped with\n    // parentheses.\n    parentType === \"ForStatement\" ||\n    parentType === \"ThrowStatement\" ||\n    parentType === \"ReturnStatement\" ||\n    (parentType === \"IfStatement\" && parent.test === node) ||\n    (parentType === \"WhileStatement\" && parent.test === node) ||\n    (parentType === \"ForInStatement\" && parent.right === node) ||\n    (parentType === \"SwitchStatement\" && parent.discriminant === node) ||\n    (parentType === \"ExpressionStatement\" && parent.expression === node)\n  ) {\n    return false;\n  }\n\n  // Otherwise err on the side of overparenthesization, adding\n  // explicit exceptions above if this proves overzealous.\n  return true;\n}\n\nexport function YieldExpression(\n  node: t.YieldExpression,\n  parent: t.Node,\n): boolean {\n  const parentType = parent.type;\n  return (\n    parentType === \"BinaryExpression\" ||\n    parentType === \"LogicalExpression\" ||\n    parentType === \"UnaryExpression\" ||\n    parentType === \"SpreadElement\" ||\n    hasPostfixPart(node, parent) ||\n    (parentType === \"AwaitExpression\" && isYieldExpression(node)) ||\n    (parentType === \"ConditionalExpression\" && node === parent.test) ||\n    isClassExtendsClause(node, parent) ||\n    isTSTypeExpression(parentType)\n  );\n}\n\nexport { YieldExpression as AwaitExpression };\n\nexport function ClassExpression(\n  node: t.ClassExpression,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  return isFirstInContext(\n    printStack,\n    CheckParam.expressionStatement | CheckParam.exportDefault,\n  );\n}\n\nexport function UnaryLike(\n  node:\n    | t.UnaryLike\n    | t.TSTypeAssertion\n    | t.ArrowFunctionExpression\n    | t.ConditionalExpression\n    | t.AssignmentExpression,\n  parent: t.Node,\n): boolean {\n  return (\n    hasPostfixPart(node, parent) ||\n    (isBinaryExpression(parent) &&\n      parent.operator === \"**\" &&\n      parent.left === node) ||\n    isClassExtendsClause(node, parent)\n  );\n}\n\nexport function FunctionExpression(\n  node: t.FunctionExpression,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  return isFirstInContext(\n    printStack,\n    CheckParam.expressionStatement | CheckParam.exportDefault,\n  );\n}\n\nexport function ArrowFunctionExpression(\n  node: t.ArrowFunctionExpression,\n  parent: t.Node,\n): boolean {\n  return isExportDeclaration(parent) || ConditionalExpression(node, parent);\n}\n\nexport function ConditionalExpression(\n  node:\n    | t.ConditionalExpression\n    | t.ArrowFunctionExpression\n    | t.AssignmentExpression,\n  parent?: t.Node,\n): boolean {\n  const parentType = parent.type;\n  if (\n    parentType === \"UnaryExpression\" ||\n    parentType === \"SpreadElement\" ||\n    parentType === \"BinaryExpression\" ||\n    parentType === \"LogicalExpression\" ||\n    (parentType === \"ConditionalExpression\" && parent.test === node) ||\n    parentType === \"AwaitExpression\" ||\n    isTSTypeExpression(parentType)\n  ) {\n    return true;\n  }\n\n  return UnaryLike(node, parent);\n}\n\nexport function OptionalMemberExpression(\n  node: t.OptionalMemberExpression,\n  parent: t.Node,\n): boolean {\n  return (\n    (isCallExpression(parent) && parent.callee === node) ||\n    (isMemberExpression(parent) && parent.object === node)\n  );\n}\n\nexport { OptionalMemberExpression as OptionalCallExpression };\n\nexport function AssignmentExpression(\n  node: t.AssignmentExpression,\n  parent: t.Node,\n): boolean {\n  if (isObjectPattern(node.left)) {\n    return true;\n  } else {\n    return ConditionalExpression(node, parent);\n  }\n}\n\nexport function LogicalExpression(\n  node: t.LogicalExpression,\n  parent: t.Node,\n): boolean {\n  const parentType = parent.type;\n  if (isTSTypeExpression(parentType)) return true;\n  if (parentType !== \"LogicalExpression\") return false;\n  switch (node.operator) {\n    case \"||\":\n      return parent.operator === \"??\" || parent.operator === \"&&\";\n    case \"&&\":\n      return parent.operator === \"??\";\n    case \"??\":\n      return parent.operator !== \"??\";\n  }\n}\n\nexport function Identifier(\n  node: t.Identifier,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  const parentType = parent.type;\n  // 13.15.2 AssignmentExpression RS: Evaluation\n  // (fn) = function () {};\n  if (\n    node.extra?.parenthesized &&\n    parentType === \"AssignmentExpression\" &&\n    parent.left === node\n  ) {\n    const rightType = parent.right.type;\n    if (\n      (rightType === \"FunctionExpression\" || rightType === \"ClassExpression\") &&\n      parent.right.id == null\n    ) {\n      return true;\n    }\n  }\n  // Non-strict code allows the identifier `let`, but it cannot occur as-is in\n  // certain contexts to avoid ambiguity with contextual keyword `let`.\n  if (node.name === \"let\") {\n    // Some contexts only forbid `let [`, so check if the next token would\n    // be the left bracket of a computed member expression.\n    const isFollowedByBracket =\n      isMemberExpression(parent, {\n        object: node,\n        computed: true,\n      }) ||\n      isOptionalMemberExpression(parent, {\n        object: node,\n        computed: true,\n        optional: false,\n      });\n    return isFirstInContext(\n      printStack,\n      isFollowedByBracket\n        ? CheckParam.expressionStatement |\n            CheckParam.forHead |\n            CheckParam.forInHead |\n            CheckParam.forOfHead\n        : CheckParam.forOfHead,\n    );\n  }\n\n  // ECMAScript specifically forbids a for-of loop from starting with the\n  // token sequence `for (async of`, because it would be ambiguous with\n  // `for (async of => {};;)`, so we need to add extra parentheses.\n  return (\n    node.name === \"async\" &&\n    isForOfStatement(parent, { left: node, await: false })\n  );\n}\n\n// Walk up the print stack to determine if our node can come first\n// in a particular context.\nfunction isFirstInContext(\n  printStack: Array<t.Node>,\n  checkParam: CheckParam,\n): boolean {\n  const expressionStatement = checkParam & CheckParam.expressionStatement;\n  const arrowBody = checkParam & CheckParam.arrowBody;\n  const exportDefault = checkParam & CheckParam.exportDefault;\n  const forHead = checkParam & CheckParam.forHead;\n  const forInHead = checkParam & CheckParam.forInHead;\n  const forOfHead = checkParam & CheckParam.forOfHead;\n\n  let i = printStack.length - 1;\n  if (i <= 0) return;\n  let node = printStack[i];\n  i--;\n  let parent = printStack[i];\n  while (i >= 0) {\n    const parentType = parent.type;\n    if (\n      (expressionStatement &&\n        parentType === \"ExpressionStatement\" &&\n        parent.expression === node) ||\n      (exportDefault &&\n        parentType === \"ExportDefaultDeclaration\" &&\n        node === parent.declaration) ||\n      (arrowBody &&\n        parentType === \"ArrowFunctionExpression\" &&\n        parent.body === node) ||\n      (forHead && parentType === \"ForStatement\" && parent.init === node) ||\n      (forInHead && parentType === \"ForInStatement\" && parent.left === node) ||\n      (forOfHead && parentType === \"ForOfStatement\" && parent.left === node)\n    ) {\n      return true;\n    }\n\n    if (\n      i > 0 &&\n      ((hasPostfixPart(node, parent) && parentType !== \"NewExpression\") ||\n        (parentType === \"SequenceExpression\" &&\n          parent.expressions[0] === node) ||\n        (parentType === \"UpdateExpression\" && !parent.prefix) ||\n        (parentType === \"ConditionalExpression\" && parent.test === node) ||\n        ((parentType === \"BinaryExpression\" ||\n          parentType === \"LogicalExpression\") &&\n          parent.left === node) ||\n        (parentType === \"AssignmentExpression\" && parent.left === node) ||\n        ((parentType === \"TSAsExpression\" ||\n          parentType === \"TSSatisfiesExpression\") &&\n          parent.expression === node))\n    ) {\n      node = parent;\n      i--;\n      parent = printStack[i];\n    } else {\n      return false;\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAYsB;EAXpBC,qBAAqB;EACrBC,yBAAyB;EACzBC,kBAAkB;EAClBC,gBAAgB;EAChBC,mBAAmB;EACnBC,gBAAgB;EAChBC,mBAAmB;EACnBC,kBAAkB;EAClBC,eAAe;EACfC,0BAA0B;EAC1BC;AAAiB,IAAAZ,EAAA;AAGnB,MAAMa,UAAU,GAAG,IAAIC,GAAG,CAAC,CACzB,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,KAAK,EAAE,CAAC,CAAC,EACV,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,KAAK,EAAE,CAAC,CAAC,EACV,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,YAAY,EAAE,CAAC,CAAC,EACjB,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,KAAK,EAAE,CAAC,CAAC,EACV,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,IAAI,EAAE,EAAE,CAAC,CACX,CAAC;AAUF,SAASC,mBAAmBA,CAACC,IAAY,EAAEC,QAAgB,EAAE;EAC3D,IAAIA,QAAQ,KAAK,kBAAkB,IAAIA,QAAQ,KAAK,mBAAmB,EAAE;IACvE,OAAOJ,UAAU,CAACK,GAAG,CAAEF,IAAI,CAAcG,QAAQ,CAAC;EACpD;EACA,IAAIF,QAAQ,KAAK,gBAAgB,IAAIA,QAAQ,KAAK,uBAAuB,EAAE;IACzE,OAAOJ,UAAU,CAACK,GAAG,CAAC,IAAI,CAAC;EAC7B;AACF;AAWA,SAASE,kBAAkBA,CAACH,QAAgB,EAAE;EAC5C,OACEA,QAAQ,KAAK,gBAAgB,IAC7BA,QAAQ,KAAK,uBAAuB,IACpCA,QAAQ,KAAK,iBAAiB;AAElC;AAEA,MAAMI,oBAAoB,GAAGA,CAC3BL,IAAY,EACZM,MAAc,KACQ;EACtB,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACE,CAACD,UAAU,KAAK,kBAAkB,IAAIA,UAAU,KAAK,iBAAiB,KACtED,MAAM,CAACG,UAAU,KAAKT,IAAI;AAE9B,CAAC;AAED,MAAMU,cAAc,GAAGA,CAACV,IAAY,EAAEM,MAAc,KAAK;EACvD,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACG,CAACD,UAAU,KAAK,kBAAkB,IACjCA,UAAU,KAAK,0BAA0B,KACzCD,MAAM,CAACK,MAAM,KAAKX,IAAI,IACvB,CAACO,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,wBAAwB,IACvCA,UAAU,KAAK,eAAe,KAC9BD,MAAM,CAACM,MAAM,KAAKZ,IAAK,IACxBO,UAAU,KAAK,0BAA0B,IAAID,MAAM,CAACO,GAAG,KAAKb,IAAK,IAClEO,UAAU,KAAK,qBAAqB;AAExC,CAAC;AAEM,SAASO,sBAAsBA,CACpCd,IAA8B,EAC9BM,MAAc,EACL;EACT,OAAOpB,qBAAqB,CAACoB,MAAM,CAAC;AACtC;AAEO,SAASS,sBAAsBA,CACpCf,IAA8B,EAC9BM,MAAc,EACdU,UAAyB,EAChB;EACT,IAAIA,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;EAE3B,MAAMV,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OAEED,UAAU,KAAK,qBAAqB,IAEpCA,UAAU,KAAK,4BAA4B,IAE3CA,UAAU,KAAK,qBAAqB,IAEnCA,UAAU,KAAK,gBAAgB,IAE9BpB,yBAAyB,CAAC6B,UAAU,CAACA,UAAU,CAACC,MAAM,GAAG,CAAC,CAAC,CAAE;AAEnE;AAEO,SAASC,gBAAgBA,CAC9BlB,IAAwB,EACxBM,MAAc,EACL;EACT,OAAOI,cAAc,CAACV,IAAI,EAAEM,MAAM,CAAC,IAAID,oBAAoB,CAACL,IAAI,EAAEM,MAAM,CAAC;AAC3E;AAEO,SAASa,gBAAgBA,CAC9BnB,IAAwB,EACxBM,MAAc,EACdU,UAAyB,EAChB;EACT,OAAOI,gBAAgB,CACrBJ,UAAU,EACV,KACF,CAAC;AACH;AAEO,SAASK,YAAYA,CAC1BrB,IAAoB,EACpBM,MAAc,EACdU,UAAyB,EAChB;EAET,OACE,CAAChB,IAAI,CAACsB,KAAK,IAAIF,gBAAgB,CAACJ,UAAU,GAAgC,CAAC;AAE/E;AAEO,SAASO,MAAMA,CACpBvB,IAA2D,EAC3DM,MAAc,EACO;EACrB,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,IACER,IAAI,CAACQ,IAAI,KAAK,kBAAkB,IAChCR,IAAI,CAACG,QAAQ,KAAK,IAAI,IACtBI,UAAU,KAAK,kBAAkB,IACjCD,MAAM,CAACH,QAAQ,KAAK,IAAI,EACxB;IACA,OAAOG,MAAM,CAACkB,IAAI,KAAKxB,IAAI;EAC7B;EAEA,IAAIK,oBAAoB,CAACL,IAAI,EAAEM,MAAM,CAAC,EAAE;IACtC,OAAO,IAAI;EACb;EAEA,IACEI,cAAc,CAACV,IAAI,EAAEM,MAAM,CAAC,IAC5BC,UAAU,KAAK,iBAAiB,IAChCA,UAAU,KAAK,eAAe,IAC9BA,UAAU,KAAK,iBAAiB,EAChC;IACA,OAAO,IAAI;EACb;EAEA,MAAMkB,SAAS,GAAG1B,mBAAmB,CAACO,MAAM,EAAEC,UAAU,CAAC;EACzD,IAAIkB,SAAS,IAAI,IAAI,EAAE;IACrB,MAAMC,OAAO,GAAG3B,mBAAmB,CAACC,IAAI,EAAEA,IAAI,CAACQ,IAAI,CAAC;IACpD,IAEGiB,SAAS,KAAKC,OAAO,IACpBnB,UAAU,KAAK,kBAAkB,IACjCD,MAAM,CAACqB,KAAK,KAAK3B,IAAI,IACvByB,SAAS,GAAGC,OAAO,EACnB;MACA,OAAO,IAAI;IACb;EACF;EAEA,OAAOE,SAAS;AAClB;AAEO,SAASC,mBAAmBA,CACjC7B,IAA2B,EAC3BM,MAAc,EACL;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACED,UAAU,KAAK,qBAAqB,IACpCA,UAAU,KAAK,wBAAwB,IACvCA,UAAU,KAAK,4BAA4B,IAC3CA,UAAU,KAAK,qBAAqB;AAExC;AAIO,SAASuB,yBAAyBA,CACvC9B,IAAiC,EACjCM,MAAc,EACL;EACT,OAAOd,mBAAmB,CAACc,MAAM,CAAC,IAAIA,MAAM,CAACyB,UAAU,KAAK/B,IAAI;AAClE;AAEO,SAASgC,cAAcA,CAC5BhC,IAAgD,EAChDM,MAAc,EACL;EACT,IACEA,MAAM,CAACE,IAAI,KAAK,kBAAkB,KACjCF,MAAM,CAACH,QAAQ,KAAK,GAAG,IAAIG,MAAM,CAACH,QAAQ,KAAK,GAAG,CAAC,IACpDH,IAAI,KAAKM,MAAM,CAACkB,IAAI,EACpB;IACA,OAAO,IAAI;EACb;EACA,OAAOD,MAAM,CAACvB,IAAI,EAAEM,MAAM,CAAC;AAC7B;AAMO,SAAS2B,WAAWA,CAACjC,IAAmB,EAAEM,MAAc,EAAW;EACxE,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACED,UAAU,KAAK,aAAa,IAC5BA,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,oBAAoB,IACnCA,UAAU,KAAK,aAAa,IAC5BA,UAAU,KAAK,YAAY;AAE/B;AAIO,SAAS2B,WAAWA,CAAClC,IAAmB,EAAEM,MAAc,EAAW;EACxE,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OAAOD,UAAU,KAAK,aAAa,IAAIA,UAAU,KAAK,gBAAgB;AACxE;AAEO,SAAS4B,yBAAyBA,CACvCnC,IAAiC,EACjCM,MAAc,EACd;EACA,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACE,CAACD,UAAU,KAAK,gBAAgB,IAC9BA,UAAU,KAAK,wBAAwB,IACvCA,UAAU,KAAK,eAAe,IAC9BA,UAAU,KAAK,2BAA2B,KAC5C,CAAC,CAACD,MAAM,CAAC8B,cAAc;AAE3B;AAEO,SAASC,gBAAgBA,CAC9BrC,IAAwB,EACxBM,MAAc,EACdgC,KAAc,EACdC,kBAA2B,EAClB;EAGT,OAAOvC,IAAI,CAACG,QAAQ,KAAK,IAAI,IAAIoC,kBAAkB;AACrD;AAEO,SAASC,kBAAkBA,CAChCxC,IAA0B,EAC1BM,MAAc,EACL;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,IAKED,UAAU,KAAK,cAAc,IAC7BA,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,iBAAiB,IAC/BA,UAAU,KAAK,aAAa,IAAID,MAAM,CAACmC,IAAI,KAAKzC,IAAK,IACrDO,UAAU,KAAK,gBAAgB,IAAID,MAAM,CAACmC,IAAI,KAAKzC,IAAK,IACxDO,UAAU,KAAK,gBAAgB,IAAID,MAAM,CAACqB,KAAK,KAAK3B,IAAK,IACzDO,UAAU,KAAK,iBAAiB,IAAID,MAAM,CAACoC,YAAY,KAAK1C,IAAK,IACjEO,UAAU,KAAK,qBAAqB,IAAID,MAAM,CAACqC,UAAU,KAAK3C,IAAK,EACpE;IACA,OAAO,KAAK;EACd;EAIA,OAAO,IAAI;AACb;AAEO,SAAS4C,eAAeA,CAC7B5C,IAAuB,EACvBM,MAAc,EACL;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACED,UAAU,KAAK,kBAAkB,IACjCA,UAAU,KAAK,mBAAmB,IAClCA,UAAU,KAAK,iBAAiB,IAChCA,UAAU,KAAK,eAAe,IAC9BG,cAAc,CAACV,IAAI,EAAEM,MAAM,CAAC,IAC3BC,UAAU,KAAK,iBAAiB,IAAIX,iBAAiB,CAACI,IAAI,CAAE,IAC5DO,UAAU,KAAK,uBAAuB,IAAIP,IAAI,KAAKM,MAAM,CAACmC,IAAK,IAChEpC,oBAAoB,CAACL,IAAI,EAAEM,MAAM,CAAC,IAClCF,kBAAkB,CAACG,UAAU,CAAC;AAElC;AAIO,SAASsC,eAAeA,CAC7B7C,IAAuB,EACvBM,MAAc,EACdU,UAAyB,EAChB;EACT,OAAOI,gBAAgB,CACrBJ,UAAU,EACV,KACF,CAAC;AACH;AAEO,SAAS8B,SAASA,CACvB9C,IAK0B,EAC1BM,MAAc,EACL;EACT,OACEI,cAAc,CAACV,IAAI,EAAEM,MAAM,CAAC,IAC3BlB,kBAAkB,CAACkB,MAAM,CAAC,IACzBA,MAAM,CAACH,QAAQ,KAAK,IAAI,IACxBG,MAAM,CAACkB,IAAI,KAAKxB,IAAK,IACvBK,oBAAoB,CAACL,IAAI,EAAEM,MAAM,CAAC;AAEtC;AAEO,SAASyC,kBAAkBA,CAChC/C,IAA0B,EAC1BM,MAAc,EACdU,UAAyB,EAChB;EACT,OAAOI,gBAAgB,CACrBJ,UAAU,EACV,KACF,CAAC;AACH;AAEO,SAASgC,uBAAuBA,CACrChD,IAA+B,EAC/BM,MAAc,EACL;EACT,OAAOhB,mBAAmB,CAACgB,MAAM,CAAC,IAAI2C,qBAAqB,CAACjD,IAAI,EAAEM,MAAM,CAAC;AAC3E;AAEO,SAAS2C,qBAAqBA,CACnCjD,IAG0B,EAC1BM,MAAe,EACN;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,IACED,UAAU,KAAK,iBAAiB,IAChCA,UAAU,KAAK,eAAe,IAC9BA,UAAU,KAAK,kBAAkB,IACjCA,UAAU,KAAK,mBAAmB,IACjCA,UAAU,KAAK,uBAAuB,IAAID,MAAM,CAACmC,IAAI,KAAKzC,IAAK,IAChEO,UAAU,KAAK,iBAAiB,IAChCH,kBAAkB,CAACG,UAAU,CAAC,EAC9B;IACA,OAAO,IAAI;EACb;EAEA,OAAOuC,SAAS,CAAC9C,IAAI,EAAEM,MAAM,CAAC;AAChC;AAEO,SAAS4C,wBAAwBA,CACtClD,IAAgC,EAChCM,MAAc,EACL;EACT,OACGjB,gBAAgB,CAACiB,MAAM,CAAC,IAAIA,MAAM,CAACM,MAAM,KAAKZ,IAAI,IAClDP,kBAAkB,CAACa,MAAM,CAAC,IAAIA,MAAM,CAACK,MAAM,KAAKX,IAAK;AAE1D;AAIO,SAASmD,oBAAoBA,CAClCnD,IAA4B,EAC5BM,MAAc,EACL;EACT,IAAIZ,eAAe,CAACM,IAAI,CAACwB,IAAI,CAAC,EAAE;IAC9B,OAAO,IAAI;EACb,CAAC,MAAM;IACL,OAAOyB,qBAAqB,CAACjD,IAAI,EAAEM,MAAM,CAAC;EAC5C;AACF;AAEO,SAAS8C,iBAAiBA,CAC/BpD,IAAyB,EACzBM,MAAc,EACL;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,IAAIJ,kBAAkB,CAACG,UAAU,CAAC,EAAE,OAAO,IAAI;EAC/C,IAAIA,UAAU,KAAK,mBAAmB,EAAE,OAAO,KAAK;EACpD,QAAQP,IAAI,CAACG,QAAQ;IACnB,KAAK,IAAI;MACP,OAAOG,MAAM,CAACH,QAAQ,KAAK,IAAI,IAAIG,MAAM,CAACH,QAAQ,KAAK,IAAI;IAC7D,KAAK,IAAI;MACP,OAAOG,MAAM,CAACH,QAAQ,KAAK,IAAI;IACjC,KAAK,IAAI;MACP,OAAOG,MAAM,CAACH,QAAQ,KAAK,IAAI;EACnC;AACF;AAEO,SAASkD,UAAUA,CACxBrD,IAAkB,EAClBM,MAAc,EACdU,UAAyB,EAChB;EAAA,IAAAsC,WAAA;EACT,MAAM/C,UAAU,GAAGD,MAAM,CAACE,IAAI;EAG9B,IACE,CAAA8C,WAAA,GAAAtD,IAAI,CAACuD,KAAK,aAAVD,WAAA,CAAYE,aAAa,IACzBjD,UAAU,KAAK,sBAAsB,IACrCD,MAAM,CAACkB,IAAI,KAAKxB,IAAI,EACpB;IACA,MAAMyD,SAAS,GAAGnD,MAAM,CAACqB,KAAK,CAACnB,IAAI;IACnC,IACE,CAACiD,SAAS,KAAK,oBAAoB,IAAIA,SAAS,KAAK,iBAAiB,KACtEnD,MAAM,CAACqB,KAAK,CAAC+B,EAAE,IAAI,IAAI,EACvB;MACA,OAAO,IAAI;IACb;EACF;EAGA,IAAI1D,IAAI,CAAC2D,IAAI,KAAK,KAAK,EAAE;IAGvB,MAAMC,mBAAmB,GACvBnE,kBAAkB,CAACa,MAAM,EAAE;MACzBK,MAAM,EAAEX,IAAI;MACZ6D,QAAQ,EAAE;IACZ,CAAC,CAAC,IACFlE,0BAA0B,CAACW,MAAM,EAAE;MACjCK,MAAM,EAAEX,IAAI;MACZ6D,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACJ,OAAO1C,gBAAgB,CACrBJ,UAAU,EACV4C,mBAAmB,GACf,KACoB,KACE,KACA,KAE5B,CAAC;EACH;EAKA,OACE5D,IAAI,CAAC2D,IAAI,KAAK,OAAO,IACrBpE,gBAAgB,CAACe,MAAM,EAAE;IAAEkB,IAAI,EAAExB,IAAI;IAAE+D,KAAK,EAAE;EAAM,CAAC,CAAC;AAE1D;AAIA,SAAS3C,gBAAgBA,CACvBJ,UAAyB,EACzBgD,UAAsB,EACb;EACT,MAAMC,mBAAmB,GAAGD,UAAU,IAAiC;EACvE,MAAME,SAAS,GAAGF,UAAU,IAAuB;EACnD,MAAMG,aAAa,GAAGH,UAAU,IAA2B;EAC3D,MAAMI,OAAO,GAAGJ,UAAU,IAAqB;EAC/C,MAAMK,SAAS,GAAGL,UAAU,KAAuB;EACnD,MAAMM,SAAS,GAAGN,UAAU,KAAuB;EAEnD,IAAIO,CAAC,GAAGvD,UAAU,CAACC,MAAM,GAAG,CAAC;EAC7B,IAAIsD,CAAC,IAAI,CAAC,EAAE;EACZ,IAAIvE,IAAI,GAAGgB,UAAU,CAACuD,CAAC,CAAC;EACxBA,CAAC,EAAE;EACH,IAAIjE,MAAM,GAAGU,UAAU,CAACuD,CAAC,CAAC;EAC1B,OAAOA,CAAC,IAAI,CAAC,EAAE;IACb,MAAMhE,UAAU,GAAGD,MAAM,CAACE,IAAI;IAC9B,IACGyD,mBAAmB,IAClB1D,UAAU,KAAK,qBAAqB,IACpCD,MAAM,CAACqC,UAAU,KAAK3C,IAAI,IAC3BmE,aAAa,IACZ5D,UAAU,KAAK,0BAA0B,IACzCP,IAAI,KAAKM,MAAM,CAACkE,WAAY,IAC7BN,SAAS,IACR3D,UAAU,KAAK,yBAAyB,IACxCD,MAAM,CAACmE,IAAI,KAAKzE,IAAK,IACtBoE,OAAO,IAAI7D,UAAU,KAAK,cAAc,IAAID,MAAM,CAACoE,IAAI,KAAK1E,IAAK,IACjEqE,SAAS,IAAI9D,UAAU,KAAK,gBAAgB,IAAID,MAAM,CAACkB,IAAI,KAAKxB,IAAK,IACrEsE,SAAS,IAAI/D,UAAU,KAAK,gBAAgB,IAAID,MAAM,CAACkB,IAAI,KAAKxB,IAAK,EACtE;MACA,OAAO,IAAI;IACb;IAEA,IACEuE,CAAC,GAAG,CAAC,KACH7D,cAAc,CAACV,IAAI,EAAEM,MAAM,CAAC,IAAIC,UAAU,KAAK,eAAe,IAC7DA,UAAU,KAAK,oBAAoB,IAClCD,MAAM,CAACqE,WAAW,CAAC,CAAC,CAAC,KAAK3E,IAAK,IAChCO,UAAU,KAAK,kBAAkB,IAAI,CAACD,MAAM,CAACsE,MAAO,IACpDrE,UAAU,KAAK,uBAAuB,IAAID,MAAM,CAACmC,IAAI,KAAKzC,IAAK,IAC/D,CAACO,UAAU,KAAK,kBAAkB,IACjCA,UAAU,KAAK,mBAAmB,KAClCD,MAAM,CAACkB,IAAI,KAAKxB,IAAK,IACtBO,UAAU,KAAK,sBAAsB,IAAID,MAAM,CAACkB,IAAI,KAAKxB,IAAK,IAC9D,CAACO,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,uBAAuB,KACtCD,MAAM,CAACqC,UAAU,KAAK3C,IAAK,CAAC,EAChC;MACAA,IAAI,GAAGM,MAAM;MACbiE,CAAC,EAAE;MACHjE,MAAM,GAAGU,UAAU,CAACuD,CAAC,CAAC;IACxB,CAAC,MAAM;MACL,OAAO,KAAK;IACd;EACF;EAEA,OAAO,KAAK;AACd", "ignoreList": []}