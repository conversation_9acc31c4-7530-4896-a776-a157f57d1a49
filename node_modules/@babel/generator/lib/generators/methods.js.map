{"version": 3, "names": ["_t", "require", "isIdentifier", "_params", "node", "idNode", "parentNode", "print", "typeParameters", "nameInfo", "_getFuncIdName", "call", "sourceIdentifierName", "name", "pos", "token", "_parameters", "params", "noLineTerminator", "type", "returnType", "_noLineTerminator", "parameters", "parent", "exit", "enterForStatementInit", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "i", "_param", "space", "parameter", "printJoin", "decorators", "optional", "typeAnnotation", "_methodHead", "kind", "key", "word", "async", "generator", "computed", "undefined", "_predicate", "noLineTerminatorAfter", "predicate", "_functionHead", "_endsWithInnerRaw", "id", "FunctionExpression", "body", "ArrowFunctionExpression", "firstParam", "format", "retainLines", "hasTypesOrComments", "printInnerComments", "param", "_param$leadingComment", "_param$trailingCommen", "leadingComments", "trailingComments", "parentType", "left", "_id$loc", "_id$loc2", "loc", "start", "identifierName", "_id$loc3", "_id$loc4", "value"], "sources": ["../../src/generators/methods.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport type * as t from \"@babel/types\";\nimport { isIdentifier, type ParentMaps } from \"@babel/types\";\n\ntype ParentsOf<T extends t.Node> = ParentMaps[T[\"type\"]];\n\nexport function _params(\n  this: Printer,\n  node: t.Function | t.TSDeclareMethod | t.TSDeclareFunction,\n  idNode: t.Expression | t.PrivateName,\n  parentNode: ParentsOf<typeof node>,\n) {\n  this.print(node.typeParameters, node);\n\n  const nameInfo = _getFuncIdName.call(this, idNode, parentNode);\n  if (nameInfo) {\n    this.sourceIdentifierName(nameInfo.name, nameInfo.pos);\n  }\n\n  this.token(\"(\");\n  this._parameters(node.params, node);\n  this.token(\")\");\n\n  const noLineTerminator = node.type === \"ArrowFunctionExpression\";\n  this.print(node.returnType, node, noLineTerminator);\n\n  this._noLineTerminator = noLineTerminator;\n}\n\nexport function _parameters(\n  this: Printer,\n  parameters: t.Function[\"params\"],\n  parent:\n    | t.Function\n    | t.TSIndexSignature\n    | t.TSDeclareMethod\n    | t.TSDeclareFunction\n    | t.TSFunctionType\n    | t.TSConstructorType,\n) {\n  const exit = this.enterForStatementInit(false);\n\n  const paramLength = parameters.length;\n  for (let i = 0; i < paramLength; i++) {\n    this._param(parameters[i], parent);\n\n    if (i < parameters.length - 1) {\n      this.token(\",\");\n      this.space();\n    }\n  }\n\n  exit();\n}\n\nexport function _param(\n  this: Printer,\n  parameter: t.Identifier | t.RestElement | t.Pattern | t.TSParameterProperty,\n  parent?:\n    | t.Function\n    | t.TSIndexSignature\n    | t.TSDeclareMethod\n    | t.TSDeclareFunction\n    | t.TSFunctionType\n    | t.TSConstructorType,\n) {\n  this.printJoin(parameter.decorators, parameter);\n  this.print(parameter, parent);\n  if (\n    // @ts-expect-error optional is not in TSParameterProperty\n    parameter.optional\n  ) {\n    this.token(\"?\"); // TS / flow\n  }\n\n  this.print(\n    // @ts-expect-error typeAnnotation is not in TSParameterProperty\n    parameter.typeAnnotation,\n    parameter,\n  ); // TS / flow\n}\n\nexport function _methodHead(this: Printer, node: t.Method | t.TSDeclareMethod) {\n  const kind = node.kind;\n  const key = node.key;\n\n  if (kind === \"get\" || kind === \"set\") {\n    this.word(kind);\n    this.space();\n  }\n\n  if (node.async) {\n    this.word(\"async\", true);\n    this.space();\n  }\n\n  if (\n    kind === \"method\" ||\n    // @ts-expect-error Fixme: kind: \"init\" is not defined\n    kind === \"init\"\n  ) {\n    if (node.generator) {\n      this.token(\"*\");\n    }\n  }\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(key, node);\n    this.token(\"]\");\n  } else {\n    this.print(key, node);\n  }\n\n  if (\n    // @ts-expect-error optional is not in ObjectMethod\n    node.optional\n  ) {\n    // TS\n    this.token(\"?\");\n  }\n\n  this._params(\n    node,\n    node.computed && node.key.type !== \"StringLiteral\" ? undefined : node.key,\n    undefined,\n  );\n}\n\nexport function _predicate(\n  this: Printer,\n  node:\n    | t.FunctionDeclaration\n    | t.FunctionExpression\n    | t.ArrowFunctionExpression,\n  noLineTerminatorAfter?: boolean,\n) {\n  if (node.predicate) {\n    if (!node.returnType) {\n      this.token(\":\");\n    }\n    this.space();\n    this.print(node.predicate, node, noLineTerminatorAfter);\n  }\n}\n\nexport function _functionHead(\n  this: Printer,\n  node: t.FunctionDeclaration | t.FunctionExpression | t.TSDeclareFunction,\n  parent: ParentsOf<typeof node>,\n) {\n  if (node.async) {\n    this.word(\"async\");\n    // We prevent inner comments from being printed here,\n    // so that they are always consistently printed in the\n    // same place regardless of the function type.\n    this._endsWithInnerRaw = false;\n    this.space();\n  }\n  this.word(\"function\");\n  if (node.generator) {\n    // We prevent inner comments from being printed here,\n    // so that they are always consistently printed in the\n    // same place regardless of the function type.\n    this._endsWithInnerRaw = false;\n    this.token(\"*\");\n  }\n\n  this.space();\n  if (node.id) {\n    this.print(node.id, node);\n  }\n\n  this._params(node, node.id, parent);\n  if (node.type !== \"TSDeclareFunction\") {\n    this._predicate(node);\n  }\n}\n\nexport function FunctionExpression(\n  this: Printer,\n  node: t.FunctionExpression,\n  parent: ParentsOf<typeof node>,\n) {\n  this._functionHead(node, parent);\n  this.space();\n  this.print(node.body, node);\n}\n\nexport { FunctionExpression as FunctionDeclaration };\n\nexport function ArrowFunctionExpression(\n  this: Printer,\n  node: t.ArrowFunctionExpression,\n  parent: ParentsOf<typeof node>,\n) {\n  if (node.async) {\n    this.word(\"async\", true);\n    this.space();\n  }\n\n  // Try to avoid printing parens in simple cases, but only if we're pretty\n  // sure that they aren't needed by type annotations or potential newlines.\n  let firstParam;\n  if (\n    !this.format.retainLines &&\n    node.params.length === 1 &&\n    isIdentifier((firstParam = node.params[0])) &&\n    !hasTypesOrComments(node, firstParam)\n  ) {\n    this.print(firstParam, node, true);\n  } else {\n    this._params(node, undefined, parent);\n  }\n\n  this._predicate(node, true);\n  this.space();\n  // When printing (x)/*1*/=>{}, we remove the parentheses\n  // and thus there aren't two contiguous inner tokens.\n  // We forcefully print inner comments here.\n  this.printInnerComments();\n  this.token(\"=>\");\n\n  this.space();\n\n  this.print(node.body, node);\n}\n\nfunction hasTypesOrComments(\n  node: t.ArrowFunctionExpression,\n  param: t.Identifier,\n): boolean {\n  return !!(\n    node.typeParameters ||\n    node.returnType ||\n    node.predicate ||\n    param.typeAnnotation ||\n    param.optional ||\n    // Flow does not support `foo /*: string*/ => {};`\n    param.leadingComments?.length ||\n    param.trailingComments?.length\n  );\n}\n\nfunction _getFuncIdName(\n  this: Printer,\n  idNode: t.Expression | t.PrivateName,\n  parent: ParentsOf<t.Function | t.TSDeclareMethod | t.TSDeclareFunction>,\n) {\n  let id: t.Expression | t.PrivateName | t.LVal = idNode;\n\n  if (!id && parent) {\n    const parentType = parent.type;\n\n    if (parentType === \"VariableDeclarator\") {\n      id = parent.id;\n    } else if (\n      parentType === \"AssignmentExpression\" ||\n      parentType === \"AssignmentPattern\"\n    ) {\n      id = parent.left;\n    } else if (\n      parentType === \"ObjectProperty\" ||\n      parentType === \"ClassProperty\"\n    ) {\n      if (!parent.computed || parent.key.type === \"StringLiteral\") {\n        id = parent.key;\n      }\n    } else if (\n      parentType === \"ClassPrivateProperty\" ||\n      parentType === \"ClassAccessorProperty\"\n    ) {\n      id = parent.key;\n    }\n  }\n\n  if (!id) return;\n\n  let nameInfo;\n\n  if (id.type === \"Identifier\") {\n    nameInfo = {\n      pos: id.loc?.start,\n      name: id.loc?.identifierName || id.name,\n    };\n  } else if (id.type === \"PrivateName\") {\n    nameInfo = {\n      pos: id.loc?.start,\n      name: \"#\" + id.id.name,\n    };\n  } else if (id.type === \"StringLiteral\") {\n    nameInfo = {\n      pos: id.loc?.start,\n      name: id.value,\n    };\n  }\n\n  return nameInfo;\n}\n"], "mappings": ";;;;;;;;;;;;;AAEA,IAAAA,EAAA,GAAAC,OAAA;AAA6D;EAApDC;AAAY,IAAAF,EAAA;AAId,SAASG,OAAOA,CAErBC,IAA0D,EAC1DC,MAAoC,EACpCC,UAAkC,EAClC;EACA,IAAI,CAACC,KAAK,CAACH,IAAI,CAACI,cAAc,EAAEJ,IAAI,CAAC;EAErC,MAAMK,QAAQ,GAAGC,cAAc,CAACC,IAAI,CAAC,IAAI,EAAEN,MAAM,EAAEC,UAAU,CAAC;EAC9D,IAAIG,QAAQ,EAAE;IACZ,IAAI,CAACG,oBAAoB,CAACH,QAAQ,CAACI,IAAI,EAAEJ,QAAQ,CAACK,GAAG,CAAC;EACxD;EAEA,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,WAAW,CAACZ,IAAI,CAACa,MAAM,EAAEb,IAAI,CAAC;EACnC,IAAI,CAACW,SAAK,GAAI,CAAC;EAEf,MAAMG,gBAAgB,GAAGd,IAAI,CAACe,IAAI,KAAK,yBAAyB;EAChE,IAAI,CAACZ,KAAK,CAACH,IAAI,CAACgB,UAAU,EAAEhB,IAAI,EAAEc,gBAAgB,CAAC;EAEnD,IAAI,CAACG,iBAAiB,GAAGH,gBAAgB;AAC3C;AAEO,SAASF,WAAWA,CAEzBM,UAAgC,EAChCC,MAMuB,EACvB;EACA,MAAMC,IAAI,GAAG,IAAI,CAACC,qBAAqB,CAAC,KAAK,CAAC;EAE9C,MAAMC,WAAW,GAAGJ,UAAU,CAACK,MAAM;EACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,EAAEE,CAAC,EAAE,EAAE;IACpC,IAAI,CAACC,MAAM,CAACP,UAAU,CAACM,CAAC,CAAC,EAAEL,MAAM,CAAC;IAElC,IAAIK,CAAC,GAAGN,UAAU,CAACK,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACZ,SAAK,GAAI,CAAC;MACf,IAAI,CAACe,KAAK,CAAC,CAAC;IACd;EACF;EAEAN,IAAI,CAAC,CAAC;AACR;AAEO,SAASK,MAAMA,CAEpBE,SAA2E,EAC3ER,MAMuB,EACvB;EACA,IAAI,CAACS,SAAS,CAACD,SAAS,CAACE,UAAU,EAAEF,SAAS,CAAC;EAC/C,IAAI,CAACxB,KAAK,CAACwB,SAAS,EAAER,MAAM,CAAC;EAC7B,IAEEQ,SAAS,CAACG,QAAQ,EAClB;IACA,IAAI,CAACnB,SAAK,GAAI,CAAC;EACjB;EAEA,IAAI,CAACR,KAAK,CAERwB,SAAS,CAACI,cAAc,EACxBJ,SACF,CAAC;AACH;AAEO,SAASK,WAAWA,CAAgBhC,IAAkC,EAAE;EAC7E,MAAMiC,IAAI,GAAGjC,IAAI,CAACiC,IAAI;EACtB,MAAMC,GAAG,GAAGlC,IAAI,CAACkC,GAAG;EAEpB,IAAID,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,EAAE;IACpC,IAAI,CAACE,IAAI,CAACF,IAAI,CAAC;IACf,IAAI,CAACP,KAAK,CAAC,CAAC;EACd;EAEA,IAAI1B,IAAI,CAACoC,KAAK,EAAE;IACd,IAAI,CAACD,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;IACxB,IAAI,CAACT,KAAK,CAAC,CAAC;EACd;EAEA,IACEO,IAAI,KAAK,QAAQ,IAEjBA,IAAI,KAAK,MAAM,EACf;IACA,IAAIjC,IAAI,CAACqC,SAAS,EAAE;MAClB,IAAI,CAAC1B,SAAK,GAAI,CAAC;IACjB;EACF;EAEA,IAAIX,IAAI,CAACsC,QAAQ,EAAE;IACjB,IAAI,CAAC3B,SAAK,GAAI,CAAC;IACf,IAAI,CAACR,KAAK,CAAC+B,GAAG,EAAElC,IAAI,CAAC;IACrB,IAAI,CAACW,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACR,KAAK,CAAC+B,GAAG,EAAElC,IAAI,CAAC;EACvB;EAEA,IAEEA,IAAI,CAAC8B,QAAQ,EACb;IAEA,IAAI,CAACnB,SAAK,GAAI,CAAC;EACjB;EAEA,IAAI,CAACZ,OAAO,CACVC,IAAI,EACJA,IAAI,CAACsC,QAAQ,IAAItC,IAAI,CAACkC,GAAG,CAACnB,IAAI,KAAK,eAAe,GAAGwB,SAAS,GAAGvC,IAAI,CAACkC,GAAG,EACzEK,SACF,CAAC;AACH;AAEO,SAASC,UAAUA,CAExBxC,IAG6B,EAC7ByC,qBAA+B,EAC/B;EACA,IAAIzC,IAAI,CAAC0C,SAAS,EAAE;IAClB,IAAI,CAAC1C,IAAI,CAACgB,UAAU,EAAE;MACpB,IAAI,CAACL,SAAK,GAAI,CAAC;IACjB;IACA,IAAI,CAACe,KAAK,CAAC,CAAC;IACZ,IAAI,CAACvB,KAAK,CAACH,IAAI,CAAC0C,SAAS,EAAE1C,IAAI,EAAEyC,qBAAqB,CAAC;EACzD;AACF;AAEO,SAASE,aAAaA,CAE3B3C,IAAwE,EACxEmB,MAA8B,EAC9B;EACA,IAAInB,IAAI,CAACoC,KAAK,EAAE;IACd,IAAI,CAACD,IAAI,CAAC,OAAO,CAAC;IAIlB,IAAI,CAACS,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAAClB,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACS,IAAI,CAAC,UAAU,CAAC;EACrB,IAAInC,IAAI,CAACqC,SAAS,EAAE;IAIlB,IAAI,CAACO,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACjC,SAAK,GAAI,CAAC;EACjB;EAEA,IAAI,CAACe,KAAK,CAAC,CAAC;EACZ,IAAI1B,IAAI,CAAC6C,EAAE,EAAE;IACX,IAAI,CAAC1C,KAAK,CAACH,IAAI,CAAC6C,EAAE,EAAE7C,IAAI,CAAC;EAC3B;EAEA,IAAI,CAACD,OAAO,CAACC,IAAI,EAAEA,IAAI,CAAC6C,EAAE,EAAE1B,MAAM,CAAC;EACnC,IAAInB,IAAI,CAACe,IAAI,KAAK,mBAAmB,EAAE;IACrC,IAAI,CAACyB,UAAU,CAACxC,IAAI,CAAC;EACvB;AACF;AAEO,SAAS8C,kBAAkBA,CAEhC9C,IAA0B,EAC1BmB,MAA8B,EAC9B;EACA,IAAI,CAACwB,aAAa,CAAC3C,IAAI,EAAEmB,MAAM,CAAC;EAChC,IAAI,CAACO,KAAK,CAAC,CAAC;EACZ,IAAI,CAACvB,KAAK,CAACH,IAAI,CAAC+C,IAAI,EAAE/C,IAAI,CAAC;AAC7B;AAIO,SAASgD,uBAAuBA,CAErChD,IAA+B,EAC/BmB,MAA8B,EAC9B;EACA,IAAInB,IAAI,CAACoC,KAAK,EAAE;IACd,IAAI,CAACD,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;IACxB,IAAI,CAACT,KAAK,CAAC,CAAC;EACd;EAIA,IAAIuB,UAAU;EACd,IACE,CAAC,IAAI,CAACC,MAAM,CAACC,WAAW,IACxBnD,IAAI,CAACa,MAAM,CAACU,MAAM,KAAK,CAAC,IACxBzB,YAAY,CAAEmD,UAAU,GAAGjD,IAAI,CAACa,MAAM,CAAC,CAAC,CAAE,CAAC,IAC3C,CAACuC,kBAAkB,CAACpD,IAAI,EAAEiD,UAAU,CAAC,EACrC;IACA,IAAI,CAAC9C,KAAK,CAAC8C,UAAU,EAAEjD,IAAI,EAAE,IAAI,CAAC;EACpC,CAAC,MAAM;IACL,IAAI,CAACD,OAAO,CAACC,IAAI,EAAEuC,SAAS,EAAEpB,MAAM,CAAC;EACvC;EAEA,IAAI,CAACqB,UAAU,CAACxC,IAAI,EAAE,IAAI,CAAC;EAC3B,IAAI,CAAC0B,KAAK,CAAC,CAAC;EAIZ,IAAI,CAAC2B,kBAAkB,CAAC,CAAC;EACzB,IAAI,CAAC1C,KAAK,CAAC,IAAI,CAAC;EAEhB,IAAI,CAACe,KAAK,CAAC,CAAC;EAEZ,IAAI,CAACvB,KAAK,CAACH,IAAI,CAAC+C,IAAI,EAAE/C,IAAI,CAAC;AAC7B;AAEA,SAASoD,kBAAkBA,CACzBpD,IAA+B,EAC/BsD,KAAmB,EACV;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EACT,OAAO,CAAC,EACNxD,IAAI,CAACI,cAAc,IACnBJ,IAAI,CAACgB,UAAU,IACfhB,IAAI,CAAC0C,SAAS,IACdY,KAAK,CAACvB,cAAc,IACpBuB,KAAK,CAACxB,QAAQ,KAAAyB,qBAAA,GAEdD,KAAK,CAACG,eAAe,aAArBF,qBAAA,CAAuBhC,MAAM,KAAAiC,qBAAA,GAC7BF,KAAK,CAACI,gBAAgB,aAAtBF,qBAAA,CAAwBjC,MAAM,CAC/B;AACH;AAEA,SAASjB,cAAcA,CAErBL,MAAoC,EACpCkB,MAAuE,EACvE;EACA,IAAI0B,EAAyC,GAAG5C,MAAM;EAEtD,IAAI,CAAC4C,EAAE,IAAI1B,MAAM,EAAE;IACjB,MAAMwC,UAAU,GAAGxC,MAAM,CAACJ,IAAI;IAE9B,IAAI4C,UAAU,KAAK,oBAAoB,EAAE;MACvCd,EAAE,GAAG1B,MAAM,CAAC0B,EAAE;IAChB,CAAC,MAAM,IACLc,UAAU,KAAK,sBAAsB,IACrCA,UAAU,KAAK,mBAAmB,EAClC;MACAd,EAAE,GAAG1B,MAAM,CAACyC,IAAI;IAClB,CAAC,MAAM,IACLD,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,eAAe,EAC9B;MACA,IAAI,CAACxC,MAAM,CAACmB,QAAQ,IAAInB,MAAM,CAACe,GAAG,CAACnB,IAAI,KAAK,eAAe,EAAE;QAC3D8B,EAAE,GAAG1B,MAAM,CAACe,GAAG;MACjB;IACF,CAAC,MAAM,IACLyB,UAAU,KAAK,sBAAsB,IACrCA,UAAU,KAAK,uBAAuB,EACtC;MACAd,EAAE,GAAG1B,MAAM,CAACe,GAAG;IACjB;EACF;EAEA,IAAI,CAACW,EAAE,EAAE;EAET,IAAIxC,QAAQ;EAEZ,IAAIwC,EAAE,CAAC9B,IAAI,KAAK,YAAY,EAAE;IAAA,IAAA8C,OAAA,EAAAC,QAAA;IAC5BzD,QAAQ,GAAG;MACTK,GAAG,GAAAmD,OAAA,GAAEhB,EAAE,CAACkB,GAAG,qBAANF,OAAA,CAAQG,KAAK;MAClBvD,IAAI,EAAE,EAAAqD,QAAA,GAAAjB,EAAE,CAACkB,GAAG,qBAAND,QAAA,CAAQG,cAAc,KAAIpB,EAAE,CAACpC;IACrC,CAAC;EACH,CAAC,MAAM,IAAIoC,EAAE,CAAC9B,IAAI,KAAK,aAAa,EAAE;IAAA,IAAAmD,QAAA;IACpC7D,QAAQ,GAAG;MACTK,GAAG,GAAAwD,QAAA,GAAErB,EAAE,CAACkB,GAAG,qBAANG,QAAA,CAAQF,KAAK;MAClBvD,IAAI,EAAE,GAAG,GAAGoC,EAAE,CAACA,EAAE,CAACpC;IACpB,CAAC;EACH,CAAC,MAAM,IAAIoC,EAAE,CAAC9B,IAAI,KAAK,eAAe,EAAE;IAAA,IAAAoD,QAAA;IACtC9D,QAAQ,GAAG;MACTK,GAAG,GAAAyD,QAAA,GAAEtB,EAAE,CAACkB,GAAG,qBAANI,QAAA,CAAQH,KAAK;MAClBvD,IAAI,EAAEoC,EAAE,CAACuB;IACX,CAAC;EACH;EAEA,OAAO/D,QAAQ;AACjB", "ignoreList": []}