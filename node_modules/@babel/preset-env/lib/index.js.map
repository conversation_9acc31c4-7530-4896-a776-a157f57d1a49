{"version": 3, "names": ["_semver", "require", "_debug", "_filterItems", "_moduleTransformations", "_normalizeOptions", "_shippedProposals", "_pluginsCompatData", "_babelPluginPolyfillCorejs", "_babel7Plugins", "_helperCompilationTargets", "_availablePlugins", "_helper<PERSON>lugin<PERSON><PERSON>s", "pluginCoreJS3", "_pluginCoreJS3", "default", "isPluginRequired", "targets", "support", "isRequired", "compatData", "filterStageFromList", "list", "stageList", "Object", "keys", "reduce", "result", "item", "has", "pluginLists", "withProposals", "withoutBugfixes", "pluginsList", "withBugfixes", "assign", "pluginsBugfixesList", "withoutProposals", "proposalPlugins", "getPluginList", "proposals", "bugfixes", "getPlugin", "pluginName", "plugin", "availablePlugins", "Error", "transformIncludesAndExcludes", "opts", "opt", "target", "exec", "add", "all", "plugins", "Set", "builtIns", "exports", "getSpecialModulesPluginNames", "modules", "shouldTransformDynamicImport", "babelVersion", "modulesPluginNames", "push", "moduleTransformations", "console", "warn", "getCoreJSOptions", "useBuiltIns", "corejs", "polyfillTargets", "include", "exclude", "shippedProposals", "debug", "method", "version", "toString", "undefined", "noRuntimeName", "getPolyfillPlugins", "regenerator", "polyfillPlugins", "pluginOptions", "major", "babel7", "pluginCoreJS2", "legacyBabelPolyfillPlugin", "usage", "deprecated", "pluginRegenerator", "removeRegeneratorEntryPlugin", "getLocalTargets", "optionsTargets", "ignoreBrowserslistConfig", "config<PERSON><PERSON>", "browserslistEnv", "<PERSON><PERSON><PERSON><PERSON>", "browsers", "getTargets", "supportsStaticESM", "caller", "supportsDynamicImport", "supportsExportNamespaceFrom", "_default", "declarePreset", "api", "assertVersion", "babelTargets", "optionsExclude", "forceAllTransforms", "optionsInclude", "optionsModules", "normalizeOptions", "loose", "spec", "semver", "lt", "hasUglifyTarget", "uglify", "transformTargets", "pluginNames", "filterItems", "pluginSyntaxMap", "addProposalSyntaxPlugins", "proposalSyntaxPlugins", "removeUnsupportedItems", "removeUnnecessaryItems", "overlappingPlugins", "pluginUseBuiltIns", "Array", "from", "map", "deprecatedAssertSyntax", "concat", "log", "JSON", "stringify", "prettifyTargets", "for<PERSON>ach", "logPlugin", "getModulesPluginNames", "transformations", "shouldTransformESM", "shouldTransformExportNamespaceFrom"], "sources": ["../src/index.ts"], "sourcesContent": ["import semver, { type SemVer } from \"semver\";\nimport { logPlugin } from \"./debug.ts\";\nimport {\n  addProposalSyntaxPlugins,\n  removeUnnecessaryItems,\n  removeUnsupportedItems,\n} from \"./filter-items.ts\";\nimport moduleTransformations from \"./module-transformations.ts\";\nimport normalizeOptions from \"./normalize-options.ts\";\nimport {\n  pluginSyntaxMap,\n  proposalPlugins,\n  proposalSyntaxPlugins,\n} from \"./shipped-proposals.ts\";\nimport {\n  plugins as pluginsList,\n  pluginsBugfixes as pluginsBugfixesList,\n  overlappingPlugins,\n} from \"./plugins-compat-data.ts\";\n\nimport type { CallerMetadata } from \"@babel/core\";\n\nimport _pluginCoreJS3 from \"babel-plugin-polyfill-corejs3\";\n// TODO(Babel 8): Just use the default import\nconst pluginCoreJS3 = _pluginCoreJS3.default || _pluginCoreJS3;\n\n// TODO(Babel 8): Remove this\nimport babel7 from \"./polyfills/babel-7-plugins.cjs\";\n\nimport getTargets, {\n  prettifyTargets,\n  filterItems,\n  isRequired,\n} from \"@babel/helper-compilation-targets\";\nimport type { Targets, InputTargets } from \"@babel/helper-compilation-targets\";\nimport availablePlugins from \"./available-plugins.ts\";\nimport { declarePreset } from \"@babel/helper-plugin-utils\";\n\nimport type { BuiltInsOption, ModuleOption, Options } from \"./types.ts\";\nexport type { Options };\n\n// TODO: Remove in Babel 8\nexport function isPluginRequired(targets: Targets, support: Targets) {\n  return isRequired(\"fake-name\", targets, {\n    compatData: { \"fake-name\": support },\n  });\n}\n\nfunction filterStageFromList(\n  list: { [feature: string]: Targets },\n  stageList: Set<string>,\n) {\n  return Object.keys(list).reduce((result, item) => {\n    if (!stageList.has(item)) {\n      // @ts-expect-error todo: refine result types\n      result[item] = list[item];\n    }\n\n    return result;\n  }, {});\n}\n\nconst pluginLists = {\n  withProposals: {\n    withoutBugfixes: pluginsList,\n    withBugfixes: Object.assign({}, pluginsList, pluginsBugfixesList),\n  },\n  withoutProposals: {\n    withoutBugfixes: filterStageFromList(pluginsList, proposalPlugins),\n    withBugfixes: filterStageFromList(\n      Object.assign({}, pluginsList, pluginsBugfixesList),\n      proposalPlugins,\n    ),\n  },\n};\n\nfunction getPluginList(proposals: boolean, bugfixes: boolean) {\n  if (proposals) {\n    if (bugfixes) return pluginLists.withProposals.withBugfixes;\n    else return pluginLists.withProposals.withoutBugfixes;\n  } else {\n    if (bugfixes) return pluginLists.withoutProposals.withBugfixes;\n    else return pluginLists.withoutProposals.withoutBugfixes;\n  }\n}\n\nconst getPlugin = (pluginName: string) => {\n  const plugin =\n    // @ts-expect-error plugin name is constructed from available plugin list\n    availablePlugins[pluginName]();\n\n  if (!plugin) {\n    throw new Error(\n      `Could not find plugin \"${pluginName}\". Ensure there is an entry in ./available-plugins.js for it.`,\n    );\n  }\n\n  return plugin;\n};\n\nexport const transformIncludesAndExcludes = (opts: Array<string>): any => {\n  return opts.reduce(\n    (result, opt) => {\n      const target = /^(es|es6|es7|esnext|web)\\./.exec(opt)\n        ? \"builtIns\"\n        : \"plugins\";\n      result[target].add(opt);\n      return result;\n    },\n    {\n      all: opts,\n      plugins: new Set(),\n      builtIns: new Set(),\n    },\n  );\n};\n\nfunction getSpecialModulesPluginNames(\n  modules: Exclude<ModuleOption, \"auto\">,\n  shouldTransformDynamicImport: boolean,\n  babelVersion: string,\n) {\n  const modulesPluginNames = [];\n  if (modules) {\n    modulesPluginNames.push(moduleTransformations[modules]);\n  }\n\n  if (shouldTransformDynamicImport) {\n    if (modules && modules !== \"umd\") {\n      modulesPluginNames.push(\"transform-dynamic-import\");\n    } else {\n      console.warn(\n        \"Dynamic import can only be transformed when transforming ES\" +\n          \" modules to AMD, CommonJS or SystemJS.\",\n      );\n    }\n  }\n\n  if (!process.env.BABEL_8_BREAKING && babelVersion[0] !== \"8\") {\n    // Enable module-related syntax plugins for older Babel versions\n    if (!shouldTransformDynamicImport) {\n      modulesPluginNames.push(\"syntax-dynamic-import\");\n    }\n    modulesPluginNames.push(\"syntax-top-level-await\");\n    modulesPluginNames.push(\"syntax-import-meta\");\n  }\n\n  return modulesPluginNames;\n}\n\nconst getCoreJSOptions = ({\n  useBuiltIns,\n  corejs,\n  polyfillTargets,\n  include,\n  exclude,\n  proposals,\n  shippedProposals,\n  debug,\n}: {\n  useBuiltIns: BuiltInsOption;\n  corejs: SemVer | null | false;\n  polyfillTargets: Targets;\n  include: Set<string>;\n  exclude: Set<string>;\n  proposals: boolean;\n  shippedProposals: boolean;\n  debug: boolean;\n}) => ({\n  method: `${useBuiltIns}-global`,\n  version: corejs ? corejs.toString() : undefined,\n  targets: polyfillTargets,\n  include,\n  exclude,\n  proposals,\n  shippedProposals,\n  debug,\n  \"#__secret_key__@babel/preset-env__compatibility\": {\n    noRuntimeName: true,\n  },\n});\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var getPolyfillPlugins = ({\n    useBuiltIns,\n    corejs,\n    polyfillTargets,\n    include,\n    exclude,\n    proposals,\n    shippedProposals,\n    regenerator,\n    debug,\n  }: {\n    useBuiltIns: BuiltInsOption;\n    corejs: SemVer | null | false;\n    polyfillTargets: Targets;\n    include: Set<string>;\n    exclude: Set<string>;\n    proposals: boolean;\n    shippedProposals: boolean;\n    regenerator: boolean;\n    debug: boolean;\n  }) => {\n    const polyfillPlugins = [];\n    if (useBuiltIns === \"usage\" || useBuiltIns === \"entry\") {\n      const pluginOptions = getCoreJSOptions({\n        useBuiltIns,\n        corejs,\n        polyfillTargets,\n        include,\n        exclude,\n        proposals,\n        shippedProposals,\n        debug,\n      });\n\n      if (corejs) {\n        if (process.env.BABEL_8_BREAKING) {\n          polyfillPlugins.push([pluginCoreJS3, pluginOptions]);\n        } else {\n          if (useBuiltIns === \"usage\") {\n            if (corejs.major === 2) {\n              polyfillPlugins.push(\n                [babel7.pluginCoreJS2, pluginOptions],\n                [babel7.legacyBabelPolyfillPlugin, { usage: true }],\n              );\n            } else {\n              polyfillPlugins.push(\n                [pluginCoreJS3, pluginOptions],\n                [\n                  babel7.legacyBabelPolyfillPlugin,\n                  { usage: true, deprecated: true },\n                ],\n              );\n            }\n            if (regenerator) {\n              polyfillPlugins.push([\n                babel7.pluginRegenerator,\n                { method: \"usage-global\", debug },\n              ]);\n            }\n          } else {\n            if (corejs.major === 2) {\n              polyfillPlugins.push(\n                [babel7.legacyBabelPolyfillPlugin, { regenerator }],\n                [babel7.pluginCoreJS2, pluginOptions],\n              );\n            } else {\n              polyfillPlugins.push(\n                [pluginCoreJS3, pluginOptions],\n                [babel7.legacyBabelPolyfillPlugin, { deprecated: true }],\n              );\n              if (!regenerator) {\n                polyfillPlugins.push([\n                  babel7.removeRegeneratorEntryPlugin,\n                  pluginOptions,\n                ]);\n              }\n            }\n          }\n        }\n      }\n    }\n    return polyfillPlugins;\n  };\n\n  if (!USE_ESM) {\n    // eslint-disable-next-line no-restricted-globals\n    exports.getPolyfillPlugins = getPolyfillPlugins;\n  }\n}\n\nfunction getLocalTargets(\n  optionsTargets: Options[\"targets\"],\n  ignoreBrowserslistConfig: boolean,\n  configPath: string,\n  browserslistEnv: string,\n) {\n  if (optionsTargets?.esmodules && optionsTargets.browsers) {\n    console.warn(`\n@babel/preset-env: esmodules and browsers targets have been specified together.\n\\`browsers\\` target, \\`${optionsTargets.browsers.toString()}\\` will be ignored.\n`);\n  }\n\n  return getTargets(optionsTargets as InputTargets, {\n    ignoreBrowserslistConfig,\n    configPath,\n    browserslistEnv,\n  });\n}\n\nfunction supportsStaticESM(caller: CallerMetadata | undefined) {\n  // TODO(Babel 8): Fallback to true\n  // @ts-expect-error supportsStaticESM is not defined in CallerMetadata\n  return !!caller?.supportsStaticESM;\n}\n\nfunction supportsDynamicImport(caller: CallerMetadata | undefined) {\n  // TODO(Babel 8): Fallback to true\n  // @ts-expect-error supportsDynamicImport is not defined in CallerMetadata\n  return !!caller?.supportsDynamicImport;\n}\n\nfunction supportsExportNamespaceFrom(caller: CallerMetadata | undefined) {\n  // TODO(Babel 8): Fallback to null\n  // @ts-expect-error supportsExportNamespaceFrom is not defined in CallerMetadata\n  return !!caller?.supportsExportNamespaceFrom;\n}\n\nexport default declarePreset((api, opts: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const babelTargets = api.targets();\n\n  if (process.env.BABEL_8_BREAKING && (\"loose\" in opts || \"spec\" in opts)) {\n    throw new Error(\n      \"@babel/preset-env: The 'loose' and 'spec' options have been removed, \" +\n        \"and you should configure granular compiler assumptions instead. See \" +\n        \"https://babeljs.io/assumptions for more information.\",\n    );\n  }\n\n  const {\n    bugfixes,\n    configPath,\n    debug,\n    exclude: optionsExclude,\n    forceAllTransforms,\n    ignoreBrowserslistConfig,\n    include: optionsInclude,\n    modules: optionsModules,\n    shippedProposals,\n    targets: optionsTargets,\n    useBuiltIns,\n    corejs: { version: corejs, proposals },\n    browserslistEnv,\n  } = normalizeOptions(opts);\n\n  if (!process.env.BABEL_8_BREAKING) {\n    // eslint-disable-next-line no-var\n    var { loose, spec = false } = opts;\n  }\n\n  let targets = babelTargets;\n\n  if (\n    // @babel/core < 7.13.0 doesn't load targets (api.targets() always\n    // returns {} thanks to @babel/helper-plugin-utils), so we always want\n    // to fallback to the old targets behavior in this case.\n    semver.lt(api.version, \"7.13.0\") ||\n    // If any browserslist-related option is specified, fallback to the old\n    // behavior of not using the targets specified in the top-level options.\n    opts.targets ||\n    opts.configPath ||\n    opts.browserslistEnv ||\n    opts.ignoreBrowserslistConfig\n  ) {\n    if (!process.env.BABEL_8_BREAKING) {\n      // eslint-disable-next-line no-var\n      var hasUglifyTarget = false;\n\n      if (optionsTargets?.uglify) {\n        hasUglifyTarget = true;\n        delete optionsTargets.uglify;\n\n        console.warn(`\nThe uglify target has been deprecated. Set the top level\noption \\`forceAllTransforms: true\\` instead.\n`);\n      }\n    }\n\n    targets = getLocalTargets(\n      optionsTargets,\n      ignoreBrowserslistConfig,\n      configPath,\n      browserslistEnv,\n    );\n  }\n\n  const transformTargets = (\n    process.env.BABEL_8_BREAKING\n      ? forceAllTransforms\n      : forceAllTransforms || hasUglifyTarget\n  )\n    ? ({} as Targets)\n    : targets;\n\n  const include = transformIncludesAndExcludes(optionsInclude);\n  const exclude = transformIncludesAndExcludes(optionsExclude);\n\n  const compatData = getPluginList(shippedProposals, bugfixes);\n  const modules =\n    optionsModules === \"auto\"\n      ? api.caller(supportsStaticESM)\n        ? false\n        : \"commonjs\"\n      : optionsModules;\n  const shouldTransformDynamicImport =\n    optionsModules === \"auto\" ? !api.caller(supportsDynamicImport) : !!modules;\n\n  // If the caller does not support export-namespace-from, we forcefully add\n  // the plugin to `includes`.\n  // TODO(Babel 8): stop doing this, similarly to how we don't do this for any\n  // other plugin. We can consider adding bundlers as targets in the future,\n  // but we should not have a one-off special case for this plugin.\n  if (\n    !exclude.plugins.has(\"transform-export-namespace-from\") &&\n    (optionsModules === \"auto\"\n      ? !api.caller(supportsExportNamespaceFrom)\n      : !!modules)\n  ) {\n    include.plugins.add(\"transform-export-namespace-from\");\n  }\n\n  const pluginNames = filterItems(\n    compatData,\n    include.plugins,\n    exclude.plugins,\n    transformTargets,\n    getSpecialModulesPluginNames(\n      modules,\n      shouldTransformDynamicImport,\n      api.version,\n    ),\n    process.env.BABEL_8_BREAKING || !loose\n      ? undefined\n      : [\"transform-typeof-symbol\"],\n    pluginSyntaxMap,\n  );\n  if (shippedProposals) {\n    addProposalSyntaxPlugins(pluginNames, proposalSyntaxPlugins);\n  }\n  removeUnsupportedItems(pluginNames, api.version);\n  removeUnnecessaryItems(pluginNames, overlappingPlugins);\n\n  const polyfillPlugins = process.env.BABEL_8_BREAKING\n    ? useBuiltIns\n      ? [\n          [\n            pluginCoreJS3,\n            getCoreJSOptions({\n              useBuiltIns,\n              corejs,\n              polyfillTargets: targets,\n              include: include.builtIns,\n              exclude: exclude.builtIns,\n              proposals,\n              shippedProposals,\n              debug,\n            }),\n          ],\n        ]\n      : []\n    : getPolyfillPlugins({\n        useBuiltIns,\n        corejs,\n        polyfillTargets: targets,\n        include: include.builtIns,\n        exclude: exclude.builtIns,\n        proposals,\n        shippedProposals,\n        regenerator: pluginNames.has(\"transform-regenerator\"),\n        debug,\n      });\n\n  const pluginUseBuiltIns = useBuiltIns !== false;\n  const plugins = Array.from(pluginNames)\n    .map(pluginName => {\n      if (\n        !process.env.BABEL_8_BREAKING &&\n        (pluginName === \"transform-class-properties\" ||\n          pluginName === \"transform-private-methods\" ||\n          pluginName === \"transform-private-property-in-object\")\n      ) {\n        return [\n          getPlugin(pluginName),\n          {\n            loose: loose\n              ? \"#__internal__@babel/preset-env__prefer-true-but-false-is-ok-if-it-prevents-an-error\"\n              : \"#__internal__@babel/preset-env__prefer-false-but-true-is-ok-if-it-prevents-an-error\",\n          },\n        ];\n      }\n      if (pluginName === \"syntax-import-attributes\") {\n        // For backward compatibility with the import-assertions plugin, we\n        // allow the deprecated `assert` keyword.\n        // TODO(Babel 8): Revisit this.\n        return [getPlugin(pluginName), { deprecatedAssertSyntax: true }];\n      }\n      return [\n        getPlugin(pluginName),\n        process.env.BABEL_8_BREAKING\n          ? { useBuiltIns: pluginUseBuiltIns }\n          : { spec, loose, useBuiltIns: pluginUseBuiltIns },\n      ];\n    })\n    .concat(polyfillPlugins);\n\n  if (debug) {\n    console.log(\"@babel/preset-env: `DEBUG` option\");\n    console.log(\"\\nUsing targets:\");\n    console.log(JSON.stringify(prettifyTargets(targets), null, 2));\n    console.log(`\\nUsing modules transform: ${optionsModules.toString()}`);\n    console.log(\"\\nUsing plugins:\");\n    pluginNames.forEach(pluginName => {\n      logPlugin(pluginName, targets, compatData);\n    });\n\n    if (!useBuiltIns) {\n      console.log(\n        \"\\nUsing polyfills: No polyfills were added, since the `useBuiltIns` option was not set.\",\n      );\n    }\n  }\n\n  return { plugins };\n});\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  // eslint-disable-next-line no-restricted-globals\n  exports.getModulesPluginNames = ({\n    modules,\n    transformations,\n    shouldTransformESM,\n    shouldTransformDynamicImport,\n    shouldTransformExportNamespaceFrom,\n  }: {\n    modules: ModuleOption;\n    transformations: typeof import(\"./module-transformations\").default;\n    shouldTransformESM: boolean;\n    shouldTransformDynamicImport: boolean;\n    shouldTransformExportNamespaceFrom: boolean;\n  }) => {\n    const modulesPluginNames = [];\n    if (modules !== false && transformations[modules]) {\n      if (shouldTransformESM) {\n        modulesPluginNames.push(transformations[modules]);\n      }\n\n      if (shouldTransformDynamicImport) {\n        if (shouldTransformESM && modules !== \"umd\") {\n          modulesPluginNames.push(\"transform-dynamic-import\");\n        } else {\n          console.warn(\n            \"Dynamic import can only be transformed when transforming ES\" +\n              \" modules to AMD, CommonJS or SystemJS.\",\n          );\n        }\n      }\n    }\n\n    if (shouldTransformExportNamespaceFrom) {\n      modulesPluginNames.push(\"transform-export-namespace-from\");\n    }\n    if (!shouldTransformDynamicImport) {\n      modulesPluginNames.push(\"syntax-dynamic-import\");\n    }\n    if (!shouldTransformExportNamespaceFrom) {\n      modulesPluginNames.push(\"syntax-export-namespace-from\");\n    }\n    modulesPluginNames.push(\"syntax-top-level-await\");\n    modulesPluginNames.push(\"syntax-import-meta\");\n\n    return modulesPluginNames;\n  };\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AAKA,IAAAG,sBAAA,GAAAH,OAAA;AACA,IAAAI,iBAAA,GAAAJ,OAAA;AACA,IAAAK,iBAAA,GAAAL,OAAA;AAKA,IAAAM,kBAAA,GAAAN,OAAA;AAQA,IAAAO,0BAAA,GAAAP,OAAA;AAKA,IAAAQ,cAAA,GAAAR,OAAA;AAEA,IAAAS,yBAAA,GAAAT,OAAA;AAMA,IAAAU,iBAAA,GAAAV,OAAA;AACA,IAAAW,kBAAA,GAAAX,OAAA;AAZA,MAAMY,aAAa,GAAGC,0BAAc,CAACC,OAAO,IAAID,0BAAc;AAkBvD,SAASE,gBAAgBA,CAACC,OAAgB,EAAEC,OAAgB,EAAE;EACnE,OAAO,IAAAC,oCAAU,EAAC,WAAW,EAAEF,OAAO,EAAE;IACtCG,UAAU,EAAE;MAAE,WAAW,EAAEF;IAAQ;EACrC,CAAC,CAAC;AACJ;AAEA,SAASG,mBAAmBA,CAC1BC,IAAoC,EACpCC,SAAsB,EACtB;EACA,OAAOC,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAACI,MAAM,CAAC,CAACC,MAAM,EAAEC,IAAI,KAAK;IAChD,IAAI,CAACL,SAAS,CAACM,GAAG,CAACD,IAAI,CAAC,EAAE;MAExBD,MAAM,CAACC,IAAI,CAAC,GAAGN,IAAI,CAACM,IAAI,CAAC;IAC3B;IAEA,OAAOD,MAAM;EACf,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AAEA,MAAMG,WAAW,GAAG;EAClBC,aAAa,EAAE;IACbC,eAAe,EAAEC,0BAAW;IAC5BC,YAAY,EAAEV,MAAM,CAACW,MAAM,CAAC,CAAC,CAAC,EAAEF,0BAAW,EAAEG,kCAAmB;EAClE,CAAC;EACDC,gBAAgB,EAAE;IAChBL,eAAe,EAAEX,mBAAmB,CAACY,0BAAW,EAAEK,iCAAe,CAAC;IAClEJ,YAAY,EAAEb,mBAAmB,CAC/BG,MAAM,CAACW,MAAM,CAAC,CAAC,CAAC,EAAEF,0BAAW,EAAEG,kCAAmB,CAAC,EACnDE,iCACF;EACF;AACF,CAAC;AAED,SAASC,aAAaA,CAACC,SAAkB,EAAEC,QAAiB,EAAE;EAC5D,IAAID,SAAS,EAAE;IACb,IAAIC,QAAQ,EAAE,OAAOX,WAAW,CAACC,aAAa,CAACG,YAAY,CAAC,KACvD,OAAOJ,WAAW,CAACC,aAAa,CAACC,eAAe;EACvD,CAAC,MAAM;IACL,IAAIS,QAAQ,EAAE,OAAOX,WAAW,CAACO,gBAAgB,CAACH,YAAY,CAAC,KAC1D,OAAOJ,WAAW,CAACO,gBAAgB,CAACL,eAAe;EAC1D;AACF;AAEA,MAAMU,SAAS,GAAIC,UAAkB,IAAK;EACxC,MAAMC,MAAM,GAEVC,yBAAgB,CAACF,UAAU,CAAC,CAAC,CAAC;EAEhC,IAAI,CAACC,MAAM,EAAE;IACX,MAAM,IAAIE,KAAK,CACZ,0BAAyBH,UAAW,+DACvC,CAAC;EACH;EAEA,OAAOC,MAAM;AACf,CAAC;AAEM,MAAMG,4BAA4B,GAAIC,IAAmB,IAAU;EACxE,OAAOA,IAAI,CAACtB,MAAM,CAChB,CAACC,MAAM,EAAEsB,GAAG,KAAK;IACf,MAAMC,MAAM,GAAG,4BAA4B,CAACC,IAAI,CAACF,GAAG,CAAC,GACjD,UAAU,GACV,SAAS;IACbtB,MAAM,CAACuB,MAAM,CAAC,CAACE,GAAG,CAACH,GAAG,CAAC;IACvB,OAAOtB,MAAM;EACf,CAAC,EACD;IACE0B,GAAG,EAAEL,IAAI;IACTM,OAAO,EAAE,IAAIC,GAAG,CAAC,CAAC;IAClBC,QAAQ,EAAE,IAAID,GAAG,CAAC;EACpB,CACF,CAAC;AACH,CAAC;AAACE,OAAA,CAAAV,4BAAA,GAAAA,4BAAA;AAEF,SAASW,4BAA4BA,CACnCC,OAAsC,EACtCC,4BAAqC,EACrCC,YAAoB,EACpB;EACA,MAAMC,kBAAkB,GAAG,EAAE;EAC7B,IAAIH,OAAO,EAAE;IACXG,kBAAkB,CAACC,IAAI,CAACC,8BAAqB,CAACL,OAAO,CAAC,CAAC;EACzD;EAEA,IAAIC,4BAA4B,EAAE;IAChC,IAAID,OAAO,IAAIA,OAAO,KAAK,KAAK,EAAE;MAChCG,kBAAkB,CAACC,IAAI,CAAC,0BAA0B,CAAC;IACrD,CAAC,MAAM;MACLE,OAAO,CAACC,IAAI,CACV,6DAA6D,GAC3D,wCACJ,CAAC;IACH;EACF;EAEA,IAAqCL,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAE5D,IAAI,CAACD,4BAA4B,EAAE;MACjCE,kBAAkB,CAACC,IAAI,CAAC,uBAAuB,CAAC;IAClD;IACAD,kBAAkB,CAACC,IAAI,CAAC,wBAAwB,CAAC;IACjDD,kBAAkB,CAACC,IAAI,CAAC,oBAAoB,CAAC;EAC/C;EAEA,OAAOD,kBAAkB;AAC3B;AAEA,MAAMK,gBAAgB,GAAGA,CAAC;EACxBC,WAAW;EACXC,MAAM;EACNC,eAAe;EACfC,OAAO;EACPC,OAAO;EACPhC,SAAS;EACTiC,gBAAgB;EAChBC;AAUF,CAAC,MAAM;EACLC,MAAM,EAAG,GAAEP,WAAY,SAAQ;EAC/BQ,OAAO,EAAEP,MAAM,GAAGA,MAAM,CAACQ,QAAQ,CAAC,CAAC,GAAGC,SAAS;EAC/C7D,OAAO,EAAEqD,eAAe;EACxBC,OAAO;EACPC,OAAO;EACPhC,SAAS;EACTiC,gBAAgB;EAChBC,KAAK;EACL,iDAAiD,EAAE;IACjDK,aAAa,EAAE;EACjB;AACF,CAAC,CAAC;AAEiC;EAEjC,IAAIC,kBAAkB,GAAGA,CAAC;IACxBZ,WAAW;IACXC,MAAM;IACNC,eAAe;IACfC,OAAO;IACPC,OAAO;IACPhC,SAAS;IACTiC,gBAAgB;IAChBQ,WAAW;IACXP;EAWF,CAAC,KAAK;IACJ,MAAMQ,eAAe,GAAG,EAAE;IAC1B,IAAId,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,OAAO,EAAE;MACtD,MAAMe,aAAa,GAAGhB,gBAAgB,CAAC;QACrCC,WAAW;QACXC,MAAM;QACNC,eAAe;QACfC,OAAO;QACPC,OAAO;QACPhC,SAAS;QACTiC,gBAAgB;QAChBC;MACF,CAAC,CAAC;MAEF,IAAIL,MAAM,EAAE;QAGH;UACL,IAAID,WAAW,KAAK,OAAO,EAAE;YAC3B,IAAIC,MAAM,CAACe,KAAK,KAAK,CAAC,EAAE;cACtBF,eAAe,CAACnB,IAAI,CAClB,CAACsB,cAAM,CAACC,aAAa,EAAEH,aAAa,CAAC,EACrC,CAACE,cAAM,CAACE,yBAAyB,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAC,CACpD,CAAC;YACH,CAAC,MAAM;cACLN,eAAe,CAACnB,IAAI,CAClB,CAAClD,aAAa,EAAEsE,aAAa,CAAC,EAC9B,CACEE,cAAM,CAACE,yBAAyB,EAChC;gBAAEC,KAAK,EAAE,IAAI;gBAAEC,UAAU,EAAE;cAAK,CAAC,CAErC,CAAC;YACH;YACA,IAAIR,WAAW,EAAE;cACfC,eAAe,CAACnB,IAAI,CAAC,CACnBsB,cAAM,CAACK,iBAAiB,EACxB;gBAAEf,MAAM,EAAE,cAAc;gBAAED;cAAM,CAAC,CAClC,CAAC;YACJ;UACF,CAAC,MAAM;YACL,IAAIL,MAAM,CAACe,KAAK,KAAK,CAAC,EAAE;cACtBF,eAAe,CAACnB,IAAI,CAClB,CAACsB,cAAM,CAACE,yBAAyB,EAAE;gBAAEN;cAAY,CAAC,CAAC,EACnD,CAACI,cAAM,CAACC,aAAa,EAAEH,aAAa,CACtC,CAAC;YACH,CAAC,MAAM;cACLD,eAAe,CAACnB,IAAI,CAClB,CAAClD,aAAa,EAAEsE,aAAa,CAAC,EAC9B,CAACE,cAAM,CAACE,yBAAyB,EAAE;gBAAEE,UAAU,EAAE;cAAK,CAAC,CACzD,CAAC;cACD,IAAI,CAACR,WAAW,EAAE;gBAChBC,eAAe,CAACnB,IAAI,CAAC,CACnBsB,cAAM,CAACM,4BAA4B,EACnCR,aAAa,CACd,CAAC;cACJ;YACF;UACF;QACF;MACF;IACF;IACA,OAAOD,eAAe;EACxB,CAAC;EAEa;IAEZzB,OAAO,CAACuB,kBAAkB,GAAGA,kBAAkB;EACjD;AACF;AAEA,SAASY,eAAeA,CACtBC,cAAkC,EAClCC,wBAAiC,EACjCC,UAAkB,EAClBC,eAAuB,EACvB;EACA,IAAIH,cAAc,YAAdA,cAAc,CAAEI,SAAS,IAAIJ,cAAc,CAACK,QAAQ,EAAE;IACxDjC,OAAO,CAACC,IAAI,CAAE;AAClB;AACA,yBAAyB2B,cAAc,CAACK,QAAQ,CAACrB,QAAQ,CAAC,CAAE;AAC5D,CAAC,CAAC;EACA;EAEA,OAAO,IAAAsB,iCAAU,EAACN,cAAc,EAAkB;IAChDC,wBAAwB;IACxBC,UAAU;IACVC;EACF,CAAC,CAAC;AACJ;AAEA,SAASI,iBAAiBA,CAACC,MAAkC,EAAE;EAG7D,OAAO,CAAC,EAACA,MAAM,YAANA,MAAM,CAAED,iBAAiB;AACpC;AAEA,SAASE,qBAAqBA,CAACD,MAAkC,EAAE;EAGjE,OAAO,CAAC,EAACA,MAAM,YAANA,MAAM,CAAEC,qBAAqB;AACxC;AAEA,SAASC,2BAA2BA,CAACF,MAAkC,EAAE;EAGvE,OAAO,CAAC,EAACA,MAAM,YAANA,MAAM,CAAEE,2BAA2B;AAC9C;AAAC,IAAAC,QAAA,GAAA/C,OAAA,CAAA1C,OAAA,GAEc,IAAA0F,gCAAa,EAAC,CAACC,GAAG,EAAE1D,IAAa,KAAK;EACnD0D,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAMC,YAAY,GAAGF,GAAG,CAACzF,OAAO,CAAC,CAAC;EAAC;EAUnC,MAAM;IACJwB,QAAQ;IACRsD,UAAU;IACVrB,KAAK;IACLF,OAAO,EAAEqC,cAAc;IACvBC,kBAAkB;IAClBhB,wBAAwB;IACxBvB,OAAO,EAAEwC,cAAc;IACvBpD,OAAO,EAAEqD,cAAc;IACvBvC,gBAAgB;IAChBxD,OAAO,EAAE4E,cAAc;IACvBzB,WAAW;IACXC,MAAM,EAAE;MAAEO,OAAO,EAAEP,MAAM;MAAE7B;IAAU,CAAC;IACtCwD;EACF,CAAC,GAAG,IAAAiB,yBAAgB,EAACjE,IAAI,CAAC;EAES;IAEjC,IAAI;MAAEkE,KAAK;MAAEC,IAAI,GAAG;IAAM,CAAC,GAAGnE,IAAI;EACpC;EAEA,IAAI/B,OAAO,GAAG2F,YAAY;EAE1B,IAIEQ,OAAM,CAACC,EAAE,CAACX,GAAG,CAAC9B,OAAO,EAAE,QAAQ,CAAC,IAGhC5B,IAAI,CAAC/B,OAAO,IACZ+B,IAAI,CAAC+C,UAAU,IACf/C,IAAI,CAACgD,eAAe,IACpBhD,IAAI,CAAC8C,wBAAwB,EAC7B;IACmC;MAEjC,IAAIwB,eAAe,GAAG,KAAK;MAE3B,IAAIzB,cAAc,YAAdA,cAAc,CAAE0B,MAAM,EAAE;QAC1BD,eAAe,GAAG,IAAI;QACtB,OAAOzB,cAAc,CAAC0B,MAAM;QAE5BtD,OAAO,CAACC,IAAI,CAAE;AACtB;AACA;AACA,CAAC,CAAC;MACI;IACF;IAEAjD,OAAO,GAAG2E,eAAe,CACvBC,cAAc,EACdC,wBAAwB,EACxBC,UAAU,EACVC,eACF,CAAC;EACH;EAEA,MAAMwB,gBAAgB,GAGhBV,kBAAkB,IAAIQ,eAAe,GAEtC,CAAC,CAAC,GACHrG,OAAO;EAEX,MAAMsD,OAAO,GAAGxB,4BAA4B,CAACgE,cAAc,CAAC;EAC5D,MAAMvC,OAAO,GAAGzB,4BAA4B,CAAC8D,cAAc,CAAC;EAE5D,MAAMzF,UAAU,GAAGmB,aAAa,CAACkC,gBAAgB,EAAEhC,QAAQ,CAAC;EAC5D,MAAMkB,OAAO,GACXqD,cAAc,KAAK,MAAM,GACrBN,GAAG,CAACL,MAAM,CAACD,iBAAiB,CAAC,GAC3B,KAAK,GACL,UAAU,GACZY,cAAc;EACpB,MAAMpD,4BAA4B,GAChCoD,cAAc,KAAK,MAAM,GAAG,CAACN,GAAG,CAACL,MAAM,CAACC,qBAAqB,CAAC,GAAG,CAAC,CAAC3C,OAAO;EAO5E,IACE,CAACa,OAAO,CAAClB,OAAO,CAACzB,GAAG,CAAC,iCAAiC,CAAC,KACtDmF,cAAc,KAAK,MAAM,GACtB,CAACN,GAAG,CAACL,MAAM,CAACE,2BAA2B,CAAC,GACxC,CAAC,CAAC5C,OAAO,CAAC,EACd;IACAY,OAAO,CAACjB,OAAO,CAACF,GAAG,CAAC,iCAAiC,CAAC;EACxD;EAEA,MAAMqE,WAAW,GAAG,IAAAC,qCAAW,EAC7BtG,UAAU,EACVmD,OAAO,CAACjB,OAAO,EACfkB,OAAO,CAAClB,OAAO,EACfkE,gBAAgB,EAChB9D,4BAA4B,CAC1BC,OAAO,EACPC,4BAA4B,EAC5B8C,GAAG,CAAC9B,OACN,CAAC,EAC+B,CAACsC,KAAK,GAClCpC,SAAS,GACT,CAAC,yBAAyB,CAAC,EAC/B6C,iCACF,CAAC;EACD,IAAIlD,gBAAgB,EAAE;IACpB,IAAAmD,qCAAwB,EAACH,WAAW,EAAEI,uCAAqB,CAAC;EAC9D;EACA,IAAAC,mCAAsB,EAACL,WAAW,EAAEf,GAAG,CAAC9B,OAAO,CAAC;EAChD,IAAAmD,mCAAsB,EAACN,WAAW,EAAEO,qCAAkB,CAAC;EAEvD,MAAM9C,eAAe,GAkBjBF,kBAAkB,CAAC;IACjBZ,WAAW;IACXC,MAAM;IACNC,eAAe,EAAErD,OAAO;IACxBsD,OAAO,EAAEA,OAAO,CAACf,QAAQ;IACzBgB,OAAO,EAAEA,OAAO,CAAChB,QAAQ;IACzBhB,SAAS;IACTiC,gBAAgB;IAChBQ,WAAW,EAAEwC,WAAW,CAAC5F,GAAG,CAAC,uBAAuB,CAAC;IACrD6C;EACF,CAAC,CAAC;EAEN,MAAMuD,iBAAiB,GAAG7D,WAAW,KAAK,KAAK;EAC/C,MAAMd,OAAO,GAAG4E,KAAK,CAACC,IAAI,CAACV,WAAW,CAAC,CACpCW,GAAG,CAACzF,UAAU,IAAI;IACjB,IAEGA,UAAU,KAAK,4BAA4B,IAC1CA,UAAU,KAAK,2BAA2B,IAC1CA,UAAU,KAAK,sCAAsC,EACvD;MACA,OAAO,CACLD,SAAS,CAACC,UAAU,CAAC,EACrB;QACEuE,KAAK,EAAEA,KAAK,GACR,qFAAqF,GACrF;MACN,CAAC,CACF;IACH;IACA,IAAIvE,UAAU,KAAK,0BAA0B,EAAE;MAI7C,OAAO,CAACD,SAAS,CAACC,UAAU,CAAC,EAAE;QAAE0F,sBAAsB,EAAE;MAAK,CAAC,CAAC;IAClE;IACA,OAAO,CACL3F,SAAS,CAACC,UAAU,CAAC,EAGjB;MAAEwE,IAAI;MAAED,KAAK;MAAE9C,WAAW,EAAE6D;IAAkB,CAAC,CACpD;EACH,CAAC,CAAC,CACDK,MAAM,CAACpD,eAAe,CAAC;EAE1B,IAAIR,KAAK,EAAE;IACTT,OAAO,CAACsE,GAAG,CAAC,mCAAmC,CAAC;IAChDtE,OAAO,CAACsE,GAAG,CAAC,kBAAkB,CAAC;IAC/BtE,OAAO,CAACsE,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,IAAAC,yCAAe,EAACzH,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC9DgD,OAAO,CAACsE,GAAG,CAAE,8BAA6BvB,cAAc,CAACnC,QAAQ,CAAC,CAAE,EAAC,CAAC;IACtEZ,OAAO,CAACsE,GAAG,CAAC,kBAAkB,CAAC;IAC/Bd,WAAW,CAACkB,OAAO,CAAChG,UAAU,IAAI;MAChC,IAAAiG,gBAAS,EAACjG,UAAU,EAAE1B,OAAO,EAAEG,UAAU,CAAC;IAC5C,CAAC,CAAC;IAEF,IAAI,CAACgD,WAAW,EAAE;MAChBH,OAAO,CAACsE,GAAG,CACT,yFACF,CAAC;IACH;EACF;EAEA,OAAO;IAAEjF;EAAQ,CAAC;AACpB,CAAC,CAAC;AAE6C;EAE7CG,OAAO,CAACoF,qBAAqB,GAAG,CAAC;IAC/BlF,OAAO;IACPmF,eAAe;IACfC,kBAAkB;IAClBnF,4BAA4B;IAC5BoF;EAOF,CAAC,KAAK;IACJ,MAAMlF,kBAAkB,GAAG,EAAE;IAC7B,IAAIH,OAAO,KAAK,KAAK,IAAImF,eAAe,CAACnF,OAAO,CAAC,EAAE;MACjD,IAAIoF,kBAAkB,EAAE;QACtBjF,kBAAkB,CAACC,IAAI,CAAC+E,eAAe,CAACnF,OAAO,CAAC,CAAC;MACnD;MAEA,IAAIC,4BAA4B,EAAE;QAChC,IAAImF,kBAAkB,IAAIpF,OAAO,KAAK,KAAK,EAAE;UAC3CG,kBAAkB,CAACC,IAAI,CAAC,0BAA0B,CAAC;QACrD,CAAC,MAAM;UACLE,OAAO,CAACC,IAAI,CACV,6DAA6D,GAC3D,wCACJ,CAAC;QACH;MACF;IACF;IAEA,IAAI8E,kCAAkC,EAAE;MACtClF,kBAAkB,CAACC,IAAI,CAAC,iCAAiC,CAAC;IAC5D;IACA,IAAI,CAACH,4BAA4B,EAAE;MACjCE,kBAAkB,CAACC,IAAI,CAAC,uBAAuB,CAAC;IAClD;IACA,IAAI,CAACiF,kCAAkC,EAAE;MACvClF,kBAAkB,CAACC,IAAI,CAAC,8BAA8B,CAAC;IACzD;IACAD,kBAAkB,CAACC,IAAI,CAAC,wBAAwB,CAAC;IACjDD,kBAAkB,CAACC,IAAI,CAAC,oBAAoB,CAAC;IAE7C,OAAOD,kBAAkB;EAC3B,CAAC;AACH", "ignoreList": []}