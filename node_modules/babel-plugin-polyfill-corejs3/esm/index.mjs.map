{"version": 3, "file": "index.mjs", "sources": ["../src/shipped-proposals.ts", "../src/built-in-definitions.ts", "../src/babel-runtime-corejs3-paths.ts", "../src/usage-filters.ts", "../src/utils.ts", "../src/index.ts"], "sourcesContent": ["// This file is automatically generated by scripts/build-corejs3-shipped-proposals.mjs\n\nexport default new Set<string>([\n  \"esnext.suppressed-error.constructor\",\n  \"esnext.array.from-async\",\n  \"esnext.array.group\",\n  \"esnext.array.group-to-map\",\n  \"esnext.iterator.constructor\",\n  \"esnext.iterator.drop\",\n  \"esnext.iterator.every\",\n  \"esnext.iterator.filter\",\n  \"esnext.iterator.find\",\n  \"esnext.iterator.flat-map\",\n  \"esnext.iterator.for-each\",\n  \"esnext.iterator.from\",\n  \"esnext.iterator.map\",\n  \"esnext.iterator.reduce\",\n  \"esnext.iterator.some\",\n  \"esnext.iterator.take\",\n  \"esnext.iterator.to-array\",\n  \"esnext.json.is-raw-json\",\n  \"esnext.json.parse\",\n  \"esnext.json.raw-json\",\n  \"esnext.set.difference.v2\",\n  \"esnext.set.intersection.v2\",\n  \"esnext.set.is-disjoint-from.v2\",\n  \"esnext.set.is-subset-of.v2\",\n  \"esnext.set.is-superset-of.v2\",\n  \"esnext.set.symmetric-difference.v2\",\n  \"esnext.set.union.v2\",\n  \"esnext.symbol.async-dispose\",\n  \"esnext.symbol.dispose\",\n  \"esnext.symbol.metadata\",\n]);\n", "import corejs3Polyfills from \"../core-js-compat/data.js\";\n\ntype ObjectMap<V> = { [name: string]: V };\ntype ObjectMap2<V> = ObjectMap<ObjectMap<V>>;\n\nexport type CoreJSPolyfillDescriptor = {\n  name: string;\n  pure: string | null;\n  global: string[];\n  exclude: string[] | null;\n};\n\nconst polyfillsOrder = {};\nObject.keys(corejs3Polyfills).forEach((name, index) => {\n  polyfillsOrder[name] = index;\n});\n\nconst define = (\n  pure,\n  global,\n  name = global[0],\n  exclude?,\n): CoreJSPolyfillDescriptor => {\n  return {\n    name,\n    pure,\n    global: global.sort((a, b) => polyfillsOrder[a] - polyfillsOrder[b]),\n    exclude,\n  };\n};\n\nconst typed = (...modules) =>\n  define(null, [...modules, ...TypedArrayDependencies]);\n\nconst ArrayNatureIterators = [\n  \"es.array.iterator\",\n  \"web.dom-collections.iterator\",\n];\n\nexport const CommonIterators = [\"es.string.iterator\", ...ArrayNatureIterators];\n\nconst ArrayNatureIteratorsWithTag = [\n  \"es.object.to-string\",\n  ...ArrayNatureIterators,\n];\n\nconst CommonIteratorsWithTag = [\"es.object.to-string\", ...CommonIterators];\n\nconst ErrorDependencies = [\"es.error.cause\", \"es.error.to-string\"];\n\nconst SuppressedErrorDependencies = [\n  \"esnext.suppressed-error.constructor\",\n  ...ErrorDependencies,\n];\n\nconst TypedArrayDependencies = [\n  \"es.typed-array.at\",\n  \"es.typed-array.copy-within\",\n  \"es.typed-array.every\",\n  \"es.typed-array.fill\",\n  \"es.typed-array.filter\",\n  \"es.typed-array.find\",\n  \"es.typed-array.find-index\",\n  \"es.typed-array.find-last\",\n  \"es.typed-array.find-last-index\",\n  \"es.typed-array.for-each\",\n  \"es.typed-array.includes\",\n  \"es.typed-array.index-of\",\n  \"es.typed-array.iterator\",\n  \"es.typed-array.join\",\n  \"es.typed-array.last-index-of\",\n  \"es.typed-array.map\",\n  \"es.typed-array.reduce\",\n  \"es.typed-array.reduce-right\",\n  \"es.typed-array.reverse\",\n  \"es.typed-array.set\",\n  \"es.typed-array.slice\",\n  \"es.typed-array.some\",\n  \"es.typed-array.sort\",\n  \"es.typed-array.subarray\",\n  \"es.typed-array.to-locale-string\",\n  \"es.typed-array.to-reversed\",\n  \"es.typed-array.to-sorted\",\n  \"es.typed-array.to-string\",\n  \"es.typed-array.with\",\n  \"es.object.to-string\",\n  \"es.array.iterator\",\n  \"es.array-buffer.slice\",\n  \"es.data-view\",\n  \"es.array-buffer.detached\",\n  \"es.array-buffer.transfer\",\n  \"es.array-buffer.transfer-to-fixed-length\",\n  \"esnext.typed-array.filter-reject\",\n  \"esnext.typed-array.group-by\",\n  \"esnext.typed-array.to-spliced\",\n  \"esnext.typed-array.unique-by\",\n];\n\nexport const PromiseDependencies = [\"es.promise\", \"es.object.to-string\"];\n\nexport const PromiseDependenciesWithIterators = [\n  ...PromiseDependencies,\n  ...CommonIterators,\n];\n\nconst SymbolDependencies = [\n  \"es.symbol\",\n  \"es.symbol.description\",\n  \"es.object.to-string\",\n];\n\nconst MapDependencies = [\n  \"es.map\",\n  \"esnext.map.delete-all\",\n  \"esnext.map.emplace\",\n  \"esnext.map.every\",\n  \"esnext.map.filter\",\n  \"esnext.map.find\",\n  \"esnext.map.find-key\",\n  \"esnext.map.includes\",\n  \"esnext.map.key-of\",\n  \"esnext.map.map-keys\",\n  \"esnext.map.map-values\",\n  \"esnext.map.merge\",\n  \"esnext.map.reduce\",\n  \"esnext.map.some\",\n  \"esnext.map.update\",\n  ...CommonIteratorsWithTag,\n];\n\nconst SetDependencies = [\n  \"es.set\",\n  \"esnext.set.add-all\",\n  \"esnext.set.delete-all\",\n  \"esnext.set.difference\",\n  \"esnext.set.difference.v2\",\n  \"esnext.set.every\",\n  \"esnext.set.filter\",\n  \"esnext.set.find\",\n  \"esnext.set.intersection\",\n  \"esnext.set.intersection.v2\",\n  \"esnext.set.is-disjoint-from\",\n  \"esnext.set.is-disjoint-from.v2\",\n  \"esnext.set.is-subset-of\",\n  \"esnext.set.is-subset-of.v2\",\n  \"esnext.set.is-superset-of\",\n  \"esnext.set.is-superset-of.v2\",\n  \"esnext.set.join\",\n  \"esnext.set.map\",\n  \"esnext.set.reduce\",\n  \"esnext.set.some\",\n  \"esnext.set.symmetric-difference\",\n  \"esnext.set.symmetric-difference.v2\",\n  \"esnext.set.union\",\n  \"esnext.set.union.v2\",\n  ...CommonIteratorsWithTag,\n];\n\nconst WeakMapDependencies = [\n  \"es.weak-map\",\n  \"esnext.weak-map.delete-all\",\n  \"esnext.weak-map.emplace\",\n  ...CommonIteratorsWithTag,\n];\n\nconst WeakSetDependencies = [\n  \"es.weak-set\",\n  \"esnext.weak-set.add-all\",\n  \"esnext.weak-set.delete-all\",\n  ...CommonIteratorsWithTag,\n];\n\nconst DOMExceptionDependencies = [\n  \"web.dom-exception.constructor\",\n  \"web.dom-exception.stack\",\n  \"web.dom-exception.to-string-tag\",\n  \"es.error.to-string\",\n];\n\nconst URLSearchParamsDependencies = [\n  \"web.url-search-params\",\n  \"web.url-search-params.delete\",\n  \"web.url-search-params.has\",\n  \"web.url-search-params.size\",\n  ...CommonIteratorsWithTag,\n];\n\nconst AsyncIteratorDependencies = [\n  \"esnext.async-iterator.constructor\",\n  ...PromiseDependencies,\n];\n\nconst AsyncIteratorProblemMethods = [\n  \"esnext.async-iterator.every\",\n  \"esnext.async-iterator.filter\",\n  \"esnext.async-iterator.find\",\n  \"esnext.async-iterator.flat-map\",\n  \"esnext.async-iterator.for-each\",\n  \"esnext.async-iterator.map\",\n  \"esnext.async-iterator.reduce\",\n  \"esnext.async-iterator.some\",\n];\n\nconst IteratorDependencies = [\n  \"esnext.iterator.constructor\",\n  \"es.object.to-string\",\n];\n\nexport const DecoratorMetadataDependencies = [\n  \"esnext.symbol.metadata\",\n  \"esnext.function.metadata\",\n];\n\nconst TypedArrayStaticMethods = (base: string) => ({\n  from: define(null, [\"es.typed-array.from\", base, ...TypedArrayDependencies]),\n  fromAsync: define(null, [\n    \"esnext.typed-array.from-async\",\n    base,\n    ...PromiseDependenciesWithIterators,\n    ...TypedArrayDependencies,\n  ]),\n  of: define(null, [\"es.typed-array.of\", base, ...TypedArrayDependencies]),\n});\n\nconst DataViewDependencies = [\n  \"es.data-view\",\n  \"es.array-buffer.constructor\",\n  \"es.array-buffer.slice\",\n  \"es.array-buffer.detached\",\n  \"es.array-buffer.transfer\",\n  \"es.array-buffer.transfer-to-fixed-length\",\n  \"es.object.to-string\",\n];\n\nexport const BuiltIns: ObjectMap<CoreJSPolyfillDescriptor> = {\n  AsyncDisposableStack: define(\"async-disposable-stack/index\", [\n    \"esnext.async-disposable-stack.constructor\",\n    \"es.object.to-string\",\n    \"esnext.async-iterator.async-dispose\",\n    \"esnext.iterator.dispose\",\n    ...PromiseDependencies,\n    ...SuppressedErrorDependencies,\n  ]),\n  AsyncIterator: define(\"async-iterator/index\", AsyncIteratorDependencies),\n  AggregateError: define(\"aggregate-error\", [\n    \"es.aggregate-error\",\n    ...ErrorDependencies,\n    ...CommonIteratorsWithTag,\n    \"es.aggregate-error.cause\",\n  ]),\n  ArrayBuffer: define(null, [\n    \"es.array-buffer.constructor\",\n    \"es.array-buffer.slice\",\n    \"es.data-view\",\n    \"es.array-buffer.detached\",\n    \"es.array-buffer.transfer\",\n    \"es.array-buffer.transfer-to-fixed-length\",\n    \"es.object.to-string\",\n  ]),\n  DataView: define(null, DataViewDependencies),\n  Date: define(null, [\"es.date.to-string\"]),\n  DOMException: define(\"dom-exception/index\", DOMExceptionDependencies),\n  DisposableStack: define(\"disposable-stack/index\", [\n    \"esnext.disposable-stack.constructor\",\n    \"es.object.to-string\",\n    \"esnext.iterator.dispose\",\n    ...SuppressedErrorDependencies,\n  ]),\n  Error: define(null, ErrorDependencies),\n  EvalError: define(null, ErrorDependencies),\n  Float32Array: typed(\"es.typed-array.float32-array\"),\n  Float64Array: typed(\"es.typed-array.float64-array\"),\n  Int8Array: typed(\"es.typed-array.int8-array\"),\n  Int16Array: typed(\"es.typed-array.int16-array\"),\n  Int32Array: typed(\"es.typed-array.int32-array\"),\n  Iterator: define(\"iterator/index\", IteratorDependencies),\n  Uint8Array: typed(\n    \"es.typed-array.uint8-array\",\n    \"esnext.uint8-array.to-base64\",\n    \"esnext.uint8-array.to-hex\",\n  ),\n  Uint8ClampedArray: typed(\"es.typed-array.uint8-clamped-array\"),\n  Uint16Array: typed(\"es.typed-array.uint16-array\"),\n  Uint32Array: typed(\"es.typed-array.uint32-array\"),\n  Map: define(\"map/index\", MapDependencies),\n  Number: define(null, [\"es.number.constructor\"]),\n  Observable: define(\"observable/index\", [\n    \"esnext.observable\",\n    \"esnext.symbol.observable\",\n    \"es.object.to-string\",\n    ...CommonIteratorsWithTag,\n  ]),\n  Promise: define(\"promise/index\", PromiseDependencies),\n  RangeError: define(null, ErrorDependencies),\n  ReferenceError: define(null, ErrorDependencies),\n  Reflect: define(null, [\"es.reflect.to-string-tag\", \"es.object.to-string\"]),\n  RegExp: define(null, [\n    \"es.regexp.constructor\",\n    \"es.regexp.dot-all\",\n    \"es.regexp.exec\",\n    \"es.regexp.sticky\",\n    \"es.regexp.to-string\",\n  ]),\n  Set: define(\"set/index\", SetDependencies),\n  SuppressedError: define(\"suppressed-error\", SuppressedErrorDependencies),\n  Symbol: define(\"symbol/index\", SymbolDependencies),\n  SyntaxError: define(null, ErrorDependencies),\n  TypeError: define(null, ErrorDependencies),\n  URIError: define(null, ErrorDependencies),\n  URL: define(\"url/index\", [\n    \"web.url\",\n    \"web.url.to-json\",\n    ...URLSearchParamsDependencies,\n  ]),\n  URLSearchParams: define(\n    \"url-search-params/index\",\n    URLSearchParamsDependencies,\n  ),\n  WeakMap: define(\"weak-map/index\", WeakMapDependencies),\n  WeakSet: define(\"weak-set/index\", WeakSetDependencies),\n\n  atob: define(\"atob\", [\"web.atob\", ...DOMExceptionDependencies]),\n  btoa: define(\"btoa\", [\"web.btoa\", ...DOMExceptionDependencies]),\n  clearImmediate: define(\"clear-immediate\", [\"web.immediate\"]),\n  compositeKey: define(\"composite-key\", [\"esnext.composite-key\"]),\n  compositeSymbol: define(\"composite-symbol\", [\"esnext.composite-symbol\"]),\n  escape: define(\"escape\", [\"es.escape\"]),\n  fetch: define(null, PromiseDependencies),\n  globalThis: define(\"global-this\", [\"es.global-this\"]),\n  parseFloat: define(\"parse-float\", [\"es.parse-float\"]),\n  parseInt: define(\"parse-int\", [\"es.parse-int\"]),\n  queueMicrotask: define(\"queue-microtask\", [\"web.queue-microtask\"]),\n  self: define(\"self\", [\"web.self\"]),\n  setImmediate: define(\"set-immediate\", [\"web.immediate\"]),\n  setInterval: define(\"set-interval\", [\"web.timers\"]),\n  setTimeout: define(\"set-timeout\", [\"web.timers\"]),\n  structuredClone: define(\"structured-clone\", [\n    \"web.structured-clone\",\n    ...DOMExceptionDependencies,\n    \"es.array.iterator\",\n    \"es.object.keys\",\n    \"es.object.to-string\",\n    \"es.map\",\n    \"es.set\",\n  ]),\n  unescape: define(\"unescape\", [\"es.unescape\"]),\n};\n\nexport const StaticProperties: ObjectMap2<CoreJSPolyfillDescriptor> = {\n  AsyncIterator: {\n    from: define(\"async-iterator/from\", [\n      \"esnext.async-iterator.from\",\n      ...AsyncIteratorDependencies,\n      ...AsyncIteratorProblemMethods,\n      ...CommonIterators,\n    ]),\n  },\n  Array: {\n    from: define(\"array/from\", [\"es.array.from\", \"es.string.iterator\"]),\n    fromAsync: define(\"array/from-async\", [\n      \"esnext.array.from-async\",\n      ...PromiseDependenciesWithIterators,\n    ]),\n    isArray: define(\"array/is-array\", [\"es.array.is-array\"]),\n    isTemplateObject: define(\"array/is-template-object\", [\n      \"esnext.array.is-template-object\",\n    ]),\n    of: define(\"array/of\", [\"es.array.of\"]),\n  },\n\n  ArrayBuffer: {\n    isView: define(null, [\"es.array-buffer.is-view\"]),\n  },\n\n  BigInt: {\n    range: define(\"bigint/range\", [\n      \"esnext.bigint.range\",\n      \"es.object.to-string\",\n    ]),\n  },\n\n  Date: {\n    now: define(\"date/now\", [\"es.date.now\"]),\n  },\n\n  Function: {\n    isCallable: define(\"function/is-callable\", [\"esnext.function.is-callable\"]),\n    isConstructor: define(\"function/is-constructor\", [\n      \"esnext.function.is-constructor\",\n    ]),\n  },\n\n  Iterator: {\n    from: define(\"iterator/from\", [\n      \"esnext.iterator.from\",\n      ...IteratorDependencies,\n      ...CommonIterators,\n    ]),\n    range: define(\"iterator/range\", [\n      \"esnext.iterator.range\",\n      \"es.object.to-string\",\n    ]),\n  },\n\n  JSON: {\n    isRawJSON: define(\"json/is-raw-json\", [\"esnext.json.is-raw-json\"]),\n    parse: define(\"json/parse\", [\"esnext.json.parse\", \"es.object.keys\"]),\n    rawJSON: define(\"json/raw-json\", [\n      \"esnext.json.raw-json\",\n      \"es.object.create\",\n      \"es.object.freeze\",\n    ]),\n    stringify: define(\n      \"json/stringify\",\n      [\"es.json.stringify\", \"es.date.to-json\"],\n      \"es.symbol\",\n    ),\n  },\n\n  Math: {\n    DEG_PER_RAD: define(\"math/deg-per-rad\", [\"esnext.math.deg-per-rad\"]),\n    RAD_PER_DEG: define(\"math/rad-per-deg\", [\"esnext.math.rad-per-deg\"]),\n    acosh: define(\"math/acosh\", [\"es.math.acosh\"]),\n    asinh: define(\"math/asinh\", [\"es.math.asinh\"]),\n    atanh: define(\"math/atanh\", [\"es.math.atanh\"]),\n    cbrt: define(\"math/cbrt\", [\"es.math.cbrt\"]),\n    clamp: define(\"math/clamp\", [\"esnext.math.clamp\"]),\n    clz32: define(\"math/clz32\", [\"es.math.clz32\"]),\n    cosh: define(\"math/cosh\", [\"es.math.cosh\"]),\n    degrees: define(\"math/degrees\", [\"esnext.math.degrees\"]),\n    expm1: define(\"math/expm1\", [\"es.math.expm1\"]),\n    fround: define(\"math/fround\", [\"es.math.fround\"]),\n    f16round: define(\"math/f16round\", [\"esnext.math.f16round\"]),\n    fscale: define(\"math/fscale\", [\"esnext.math.fscale\"]),\n    hypot: define(\"math/hypot\", [\"es.math.hypot\"]),\n    iaddh: define(\"math/iaddh\", [\"esnext.math.iaddh\"]),\n    imul: define(\"math/imul\", [\"es.math.imul\"]),\n    imulh: define(\"math/imulh\", [\"esnext.math.imulh\"]),\n    isubh: define(\"math/isubh\", [\"esnext.math.isubh\"]),\n    log10: define(\"math/log10\", [\"es.math.log10\"]),\n    log1p: define(\"math/log1p\", [\"es.math.log1p\"]),\n    log2: define(\"math/log2\", [\"es.math.log2\"]),\n    radians: define(\"math/radians\", [\"esnext.math.radians\"]),\n    scale: define(\"math/scale\", [\"esnext.math.scale\"]),\n    seededPRNG: define(\"math/seeded-prng\", [\"esnext.math.seeded-prng\"]),\n    sign: define(\"math/sign\", [\"es.math.sign\"]),\n    signbit: define(\"math/signbit\", [\"esnext.math.signbit\"]),\n    sinh: define(\"math/sinh\", [\"es.math.sinh\"]),\n    tanh: define(\"math/tanh\", [\"es.math.tanh\"]),\n    trunc: define(\"math/trunc\", [\"es.math.trunc\"]),\n    umulh: define(\"math/umulh\", [\"esnext.math.umulh\"]),\n  },\n\n  Map: {\n    from: define(null, [\"esnext.map.from\", ...MapDependencies]),\n    groupBy: define(\"map/group-by\", [\"es.map.group-by\", ...MapDependencies]),\n    keyBy: define(\"map/key-by\", [\"esnext.map.key-by\", ...MapDependencies]),\n    of: define(null, [\"esnext.map.of\", ...MapDependencies]),\n  },\n\n  Number: {\n    EPSILON: define(\"number/epsilon\", [\"es.number.epsilon\"]),\n    MAX_SAFE_INTEGER: define(\"number/max-safe-integer\", [\n      \"es.number.max-safe-integer\",\n    ]),\n    MIN_SAFE_INTEGER: define(\"number/min-safe-integer\", [\n      \"es.number.min-safe-integer\",\n    ]),\n    fromString: define(\"number/from-string\", [\"esnext.number.from-string\"]),\n    isFinite: define(\"number/is-finite\", [\"es.number.is-finite\"]),\n    isInteger: define(\"number/is-integer\", [\"es.number.is-integer\"]),\n    isNaN: define(\"number/is-nan\", [\"es.number.is-nan\"]),\n    isSafeInteger: define(\"number/is-safe-integer\", [\n      \"es.number.is-safe-integer\",\n    ]),\n    parseFloat: define(\"number/parse-float\", [\"es.number.parse-float\"]),\n    parseInt: define(\"number/parse-int\", [\"es.number.parse-int\"]),\n    range: define(\"number/range\", [\n      \"esnext.number.range\",\n      \"es.object.to-string\",\n    ]),\n  },\n\n  Object: {\n    assign: define(\"object/assign\", [\"es.object.assign\"]),\n    create: define(\"object/create\", [\"es.object.create\"]),\n    defineProperties: define(\"object/define-properties\", [\n      \"es.object.define-properties\",\n    ]),\n    defineProperty: define(\"object/define-property\", [\n      \"es.object.define-property\",\n    ]),\n    entries: define(\"object/entries\", [\"es.object.entries\"]),\n    freeze: define(\"object/freeze\", [\"es.object.freeze\"]),\n    fromEntries: define(\"object/from-entries\", [\n      \"es.object.from-entries\",\n      \"es.array.iterator\",\n    ]),\n    getOwnPropertyDescriptor: define(\"object/get-own-property-descriptor\", [\n      \"es.object.get-own-property-descriptor\",\n    ]),\n    getOwnPropertyDescriptors: define(\"object/get-own-property-descriptors\", [\n      \"es.object.get-own-property-descriptors\",\n    ]),\n    getOwnPropertyNames: define(\"object/get-own-property-names\", [\n      \"es.object.get-own-property-names\",\n    ]),\n    getOwnPropertySymbols: define(\"object/get-own-property-symbols\", [\n      \"es.symbol\",\n    ]),\n    getPrototypeOf: define(\"object/get-prototype-of\", [\n      \"es.object.get-prototype-of\",\n    ]),\n    groupBy: define(\"object/group-by\", [\n      \"es.object.group-by\",\n      \"es.object.create\",\n    ]),\n    hasOwn: define(\"object/has-own\", [\"es.object.has-own\"]),\n    is: define(\"object/is\", [\"es.object.is\"]),\n    isExtensible: define(\"object/is-extensible\", [\"es.object.is-extensible\"]),\n    isFrozen: define(\"object/is-frozen\", [\"es.object.is-frozen\"]),\n    isSealed: define(\"object/is-sealed\", [\"es.object.is-sealed\"]),\n    keys: define(\"object/keys\", [\"es.object.keys\"]),\n    preventExtensions: define(\"object/prevent-extensions\", [\n      \"es.object.prevent-extensions\",\n    ]),\n    seal: define(\"object/seal\", [\"es.object.seal\"]),\n    setPrototypeOf: define(\"object/set-prototype-of\", [\n      \"es.object.set-prototype-of\",\n    ]),\n    values: define(\"object/values\", [\"es.object.values\"]),\n  },\n\n  Promise: {\n    all: define(null, PromiseDependenciesWithIterators),\n    allSettled: define(\"promise/all-settled\", [\n      \"es.promise.all-settled\",\n      ...PromiseDependenciesWithIterators,\n    ]),\n    any: define(\"promise/any\", [\n      \"es.promise.any\",\n      \"es.aggregate-error\",\n      ...PromiseDependenciesWithIterators,\n    ]),\n    race: define(null, PromiseDependenciesWithIterators),\n    try: define(\"promise/try\", [\"esnext.promise.try\", ...PromiseDependencies]),\n    withResolvers: define(\"promise/with-resolvers\", [\n      \"es.promise.with-resolvers\",\n      ...PromiseDependencies,\n    ]),\n  },\n\n  Reflect: {\n    apply: define(\"reflect/apply\", [\"es.reflect.apply\"]),\n    construct: define(\"reflect/construct\", [\"es.reflect.construct\"]),\n    defineMetadata: define(\"reflect/define-metadata\", [\n      \"esnext.reflect.define-metadata\",\n    ]),\n    defineProperty: define(\"reflect/define-property\", [\n      \"es.reflect.define-property\",\n    ]),\n    deleteMetadata: define(\"reflect/delete-metadata\", [\n      \"esnext.reflect.delete-metadata\",\n    ]),\n    deleteProperty: define(\"reflect/delete-property\", [\n      \"es.reflect.delete-property\",\n    ]),\n    get: define(\"reflect/get\", [\"es.reflect.get\"]),\n    getMetadata: define(\"reflect/get-metadata\", [\n      \"esnext.reflect.get-metadata\",\n    ]),\n    getMetadataKeys: define(\"reflect/get-metadata-keys\", [\n      \"esnext.reflect.get-metadata-keys\",\n    ]),\n    getOwnMetadata: define(\"reflect/get-own-metadata\", [\n      \"esnext.reflect.get-own-metadata\",\n    ]),\n    getOwnMetadataKeys: define(\"reflect/get-own-metadata-keys\", [\n      \"esnext.reflect.get-own-metadata-keys\",\n    ]),\n    getOwnPropertyDescriptor: define(\"reflect/get-own-property-descriptor\", [\n      \"es.reflect.get-own-property-descriptor\",\n    ]),\n    getPrototypeOf: define(\"reflect/get-prototype-of\", [\n      \"es.reflect.get-prototype-of\",\n    ]),\n    has: define(\"reflect/has\", [\"es.reflect.has\"]),\n    hasMetadata: define(\"reflect/has-metadata\", [\n      \"esnext.reflect.has-metadata\",\n    ]),\n    hasOwnMetadata: define(\"reflect/has-own-metadata\", [\n      \"esnext.reflect.has-own-metadata\",\n    ]),\n    isExtensible: define(\"reflect/is-extensible\", [\"es.reflect.is-extensible\"]),\n    metadata: define(\"reflect/metadata\", [\"esnext.reflect.metadata\"]),\n    ownKeys: define(\"reflect/own-keys\", [\"es.reflect.own-keys\"]),\n    preventExtensions: define(\"reflect/prevent-extensions\", [\n      \"es.reflect.prevent-extensions\",\n    ]),\n    set: define(\"reflect/set\", [\"es.reflect.set\"]),\n    setPrototypeOf: define(\"reflect/set-prototype-of\", [\n      \"es.reflect.set-prototype-of\",\n    ]),\n  },\n\n  RegExp: {\n    escape: define(\"regexp/escape\", [\"esnext.regexp.escape\"]),\n  },\n\n  Set: {\n    from: define(null, [\"esnext.set.from\", ...SetDependencies]),\n    of: define(null, [\"esnext.set.of\", ...SetDependencies]),\n  },\n\n  String: {\n    cooked: define(\"string/cooked\", [\"esnext.string.cooked\"]),\n    dedent: define(\"string/dedent\", [\n      \"esnext.string.dedent\",\n      \"es.string.from-code-point\",\n      \"es.weak-map\",\n    ]),\n    fromCodePoint: define(\"string/from-code-point\", [\n      \"es.string.from-code-point\",\n    ]),\n    raw: define(\"string/raw\", [\"es.string.raw\"]),\n  },\n\n  Symbol: {\n    asyncDispose: define(\"symbol/async-dispose\", [\n      \"esnext.symbol.async-dispose\",\n      \"esnext.async-iterator.async-dispose\",\n    ]),\n    asyncIterator: define(\"symbol/async-iterator\", [\n      \"es.symbol.async-iterator\",\n    ]),\n    dispose: define(\"symbol/dispose\", [\n      \"esnext.symbol.dispose\",\n      \"esnext.iterator.dispose\",\n    ]),\n    for: define(\"symbol/for\", [], \"es.symbol\"),\n    hasInstance: define(\"symbol/has-instance\", [\n      \"es.symbol.has-instance\",\n      \"es.function.has-instance\",\n    ]),\n    isConcatSpreadable: define(\"symbol/is-concat-spreadable\", [\n      \"es.symbol.is-concat-spreadable\",\n      \"es.array.concat\",\n    ]),\n    isRegistered: define(\"symbol/is-registered\", [\n      \"esnext.symbol.is-registered\",\n      \"es.symbol\",\n    ]),\n    isRegisteredSymbol: define(\"symbol/is-registered-symbol\", [\n      \"esnext.symbol.is-registered-symbol\",\n      \"es.symbol\",\n    ]),\n    isWellKnown: define(\"symbol/is-well-known\", [\n      \"esnext.symbol.is-well-known\",\n      \"es.symbol\",\n    ]),\n    isWellKnownSymbol: define(\"symbol/is-well-known-symbol\", [\n      \"esnext.symbol.is-well-known-symbol\",\n      \"es.symbol\",\n    ]),\n    iterator: define(\"symbol/iterator\", [\n      \"es.symbol.iterator\",\n      ...CommonIteratorsWithTag,\n    ]),\n    keyFor: define(\"symbol/key-for\", [], \"es.symbol\"),\n    match: define(\"symbol/match\", [\"es.symbol.match\", \"es.string.match\"]),\n    matcher: define(\"symbol/matcher\", [\"esnext.symbol.matcher\"]),\n    matchAll: define(\"symbol/match-all\", [\n      \"es.symbol.match-all\",\n      \"es.string.match-all\",\n    ]),\n    metadata: define(\"symbol/metadata\", DecoratorMetadataDependencies),\n    metadataKey: define(\"symbol/metadata-key\", [\"esnext.symbol.metadata-key\"]),\n    observable: define(\"symbol/observable\", [\"esnext.symbol.observable\"]),\n    patternMatch: define(\"symbol/pattern-match\", [\n      \"esnext.symbol.pattern-match\",\n    ]),\n    replace: define(\"symbol/replace\", [\n      \"es.symbol.replace\",\n      \"es.string.replace\",\n    ]),\n    search: define(\"symbol/search\", [\"es.symbol.search\", \"es.string.search\"]),\n    species: define(\"symbol/species\", [\n      \"es.symbol.species\",\n      \"es.array.species\",\n    ]),\n    split: define(\"symbol/split\", [\"es.symbol.split\", \"es.string.split\"]),\n    toPrimitive: define(\"symbol/to-primitive\", [\n      \"es.symbol.to-primitive\",\n      \"es.date.to-primitive\",\n    ]),\n    toStringTag: define(\"symbol/to-string-tag\", [\n      \"es.symbol.to-string-tag\",\n      \"es.object.to-string\",\n      \"es.math.to-string-tag\",\n      \"es.json.to-string-tag\",\n    ]),\n    unscopables: define(\"symbol/unscopables\", [\"es.symbol.unscopables\"]),\n  },\n\n  URL: {\n    canParse: define(\"url/can-parse\", [\"web.url.can-parse\", \"web.url\"]),\n  },\n\n  WeakMap: {\n    from: define(null, [\"esnext.weak-map.from\", ...WeakMapDependencies]),\n    of: define(null, [\"esnext.weak-map.of\", ...WeakMapDependencies]),\n  },\n\n  WeakSet: {\n    from: define(null, [\"esnext.weak-set.from\", ...WeakSetDependencies]),\n    of: define(null, [\"esnext.weak-set.of\", ...WeakSetDependencies]),\n  },\n\n  Int8Array: TypedArrayStaticMethods(\"es.typed-array.int8-array\"),\n  Uint8Array: {\n    fromBase64: define(null, [\n      \"esnext.uint8-array.from-base64\",\n      ...TypedArrayDependencies,\n    ]),\n    fromHex: define(null, [\n      \"esnext.uint8-array.from-hex\",\n      ...TypedArrayDependencies,\n    ]),\n    ...TypedArrayStaticMethods(\"es.typed-array.uint8-array\"),\n  },\n  Uint8ClampedArray: TypedArrayStaticMethods(\n    \"es.typed-array.uint8-clamped-array\",\n  ),\n  Int16Array: TypedArrayStaticMethods(\"es.typed-array.int16-array\"),\n  Uint16Array: TypedArrayStaticMethods(\"es.typed-array.uint16-array\"),\n  Int32Array: TypedArrayStaticMethods(\"es.typed-array.int32-array\"),\n  Uint32Array: TypedArrayStaticMethods(\"es.typed-array.uint32-array\"),\n  Float32Array: TypedArrayStaticMethods(\"es.typed-array.float32-array\"),\n  Float64Array: TypedArrayStaticMethods(\"es.typed-array.float64-array\"),\n\n  WebAssembly: {\n    CompileError: define(null, ErrorDependencies),\n    LinkError: define(null, ErrorDependencies),\n    RuntimeError: define(null, ErrorDependencies),\n  },\n};\n\nexport const InstanceProperties = {\n  asIndexedPairs: define(\"instance/asIndexedPairs\", [\n    \"esnext.async-iterator.as-indexed-pairs\",\n    ...AsyncIteratorDependencies,\n    \"esnext.iterator.as-indexed-pairs\",\n    ...IteratorDependencies,\n  ]),\n  at: define(\"instance/at\", [\n    // TODO: We should introduce overloaded instance methods definition\n    // Before that is implemented, the `esnext.string.at` must be the first\n    // In pure mode, the provider resolves the descriptor as a \"pure\" `esnext.string.at`\n    // and treats the compat-data of `esnext.string.at` as the compat-data of\n    // pure import `instance/at`. The first polyfill here should have the lowest corejs\n    // supported versions.\n    \"esnext.string.at\",\n    \"es.string.at-alternative\",\n    \"es.array.at\",\n  ]),\n  anchor: define(null, [\"es.string.anchor\"]),\n  big: define(null, [\"es.string.big\"]),\n  bind: define(\"instance/bind\", [\"es.function.bind\"]),\n  blink: define(null, [\"es.string.blink\"]),\n  bold: define(null, [\"es.string.bold\"]),\n  codePointAt: define(\"instance/code-point-at\", [\"es.string.code-point-at\"]),\n  codePoints: define(\"instance/code-points\", [\"esnext.string.code-points\"]),\n  concat: define(\"instance/concat\", [\"es.array.concat\"], undefined, [\"String\"]),\n  copyWithin: define(\"instance/copy-within\", [\"es.array.copy-within\"]),\n  demethodize: define(\"instance/demethodize\", [\"esnext.function.demethodize\"]),\n  description: define(null, [\"es.symbol\", \"es.symbol.description\"]),\n  dotAll: define(null, [\"es.regexp.dot-all\"]),\n  drop: define(null, [\n    \"esnext.async-iterator.drop\",\n    ...AsyncIteratorDependencies,\n    \"esnext.iterator.drop\",\n    ...IteratorDependencies,\n  ]),\n  emplace: define(\"instance/emplace\", [\n    \"esnext.map.emplace\",\n    \"esnext.weak-map.emplace\",\n  ]),\n  endsWith: define(\"instance/ends-with\", [\"es.string.ends-with\"]),\n  entries: define(\"instance/entries\", ArrayNatureIteratorsWithTag),\n  every: define(\"instance/every\", [\n    \"es.array.every\",\n    \"esnext.async-iterator.every\",\n    // TODO: add async iterator dependencies when we support sub-dependencies\n    // esnext.async-iterator.every depends on es.promise\n    // but we don't want to pull es.promise when esnext.async-iterator is disabled\n    //\n    // ...AsyncIteratorDependencies\n    \"esnext.iterator.every\",\n    ...IteratorDependencies,\n  ]),\n  exec: define(null, [\"es.regexp.exec\"]),\n  fill: define(\"instance/fill\", [\"es.array.fill\"]),\n  filter: define(\"instance/filter\", [\n    \"es.array.filter\",\n    \"esnext.async-iterator.filter\",\n    \"esnext.iterator.filter\",\n    ...IteratorDependencies,\n  ]),\n  filterReject: define(\"instance/filterReject\", [\"esnext.array.filter-reject\"]),\n  finally: define(null, [\"es.promise.finally\", ...PromiseDependencies]),\n  find: define(\"instance/find\", [\n    \"es.array.find\",\n    \"esnext.async-iterator.find\",\n    \"esnext.iterator.find\",\n    ...IteratorDependencies,\n  ]),\n  findIndex: define(\"instance/find-index\", [\"es.array.find-index\"]),\n  findLast: define(\"instance/find-last\", [\"es.array.find-last\"]),\n  findLastIndex: define(\"instance/find-last-index\", [\n    \"es.array.find-last-index\",\n  ]),\n  fixed: define(null, [\"es.string.fixed\"]),\n  flags: define(\"instance/flags\", [\"es.regexp.flags\"]),\n  flatMap: define(\"instance/flat-map\", [\n    \"es.array.flat-map\",\n    \"es.array.unscopables.flat-map\",\n    \"esnext.async-iterator.flat-map\",\n    \"esnext.iterator.flat-map\",\n    ...IteratorDependencies,\n  ]),\n  flat: define(\"instance/flat\", [\"es.array.flat\", \"es.array.unscopables.flat\"]),\n  getFloat16: define(null, [\n    \"esnext.data-view.get-float16\",\n    ...DataViewDependencies,\n  ]),\n  getUint8Clamped: define(null, [\n    \"esnext.data-view.get-uint8-clamped\",\n    ...DataViewDependencies,\n  ]),\n  getYear: define(null, [\"es.date.get-year\"]),\n  group: define(\"instance/group\", [\"esnext.array.group\"]),\n  groupBy: define(\"instance/group-by\", [\"esnext.array.group-by\"]),\n  groupByToMap: define(\"instance/group-by-to-map\", [\n    \"esnext.array.group-by-to-map\",\n    \"es.map\",\n    \"es.object.to-string\",\n  ]),\n  groupToMap: define(\"instance/group-to-map\", [\n    \"esnext.array.group-to-map\",\n    \"es.map\",\n    \"es.object.to-string\",\n  ]),\n  fontcolor: define(null, [\"es.string.fontcolor\"]),\n  fontsize: define(null, [\"es.string.fontsize\"]),\n  forEach: define(\"instance/for-each\", [\n    \"es.array.for-each\",\n    \"esnext.async-iterator.for-each\",\n    \"esnext.iterator.for-each\",\n    ...IteratorDependencies,\n    \"web.dom-collections.for-each\",\n  ]),\n  includes: define(\"instance/includes\", [\n    \"es.array.includes\",\n    \"es.string.includes\",\n  ]),\n  indexed: define(null, [\n    \"esnext.async-iterator.indexed\",\n    ...AsyncIteratorDependencies,\n    \"esnext.iterator.indexed\",\n    ...IteratorDependencies,\n  ]),\n  indexOf: define(\"instance/index-of\", [\"es.array.index-of\"]),\n  isWellFormed: define(\"instance/is-well-formed\", [\"es.string.is-well-formed\"]),\n  italic: define(null, [\"es.string.italics\"]),\n  join: define(null, [\"es.array.join\"]),\n  keys: define(\"instance/keys\", ArrayNatureIteratorsWithTag),\n  lastIndex: define(null, [\"esnext.array.last-index\"]),\n  lastIndexOf: define(\"instance/last-index-of\", [\"es.array.last-index-of\"]),\n  lastItem: define(null, [\"esnext.array.last-item\"]),\n  link: define(null, [\"es.string.link\"]),\n  map: define(\"instance/map\", [\n    \"es.array.map\",\n    \"esnext.async-iterator.map\",\n    \"esnext.iterator.map\",\n  ]),\n  match: define(null, [\"es.string.match\", \"es.regexp.exec\"]),\n  matchAll: define(\"instance/match-all\", [\n    \"es.string.match-all\",\n    \"es.regexp.exec\",\n  ]),\n  name: define(null, [\"es.function.name\"]),\n  padEnd: define(\"instance/pad-end\", [\"es.string.pad-end\"]),\n  padStart: define(\"instance/pad-start\", [\"es.string.pad-start\"]),\n  push: define(\"instance/push\", [\"es.array.push\"]),\n  reduce: define(\"instance/reduce\", [\n    \"es.array.reduce\",\n    \"esnext.async-iterator.reduce\",\n    \"esnext.iterator.reduce\",\n    ...IteratorDependencies,\n  ]),\n  reduceRight: define(\"instance/reduce-right\", [\"es.array.reduce-right\"]),\n  repeat: define(\"instance/repeat\", [\"es.string.repeat\"]),\n  replace: define(null, [\"es.string.replace\", \"es.regexp.exec\"]),\n  replaceAll: define(\"instance/replace-all\", [\n    \"es.string.replace-all\",\n    \"es.string.replace\",\n    \"es.regexp.exec\",\n  ]),\n  reverse: define(\"instance/reverse\", [\"es.array.reverse\"]),\n  search: define(null, [\"es.string.search\", \"es.regexp.exec\"]),\n  setFloat16: define(null, [\n    \"esnext.data-view.set-float16\",\n    ...DataViewDependencies,\n  ]),\n  setUint8Clamped: define(null, [\n    \"esnext.data-view.set-uint8-clamped\",\n    ...DataViewDependencies,\n  ]),\n  setYear: define(null, [\"es.date.set-year\"]),\n  slice: define(\"instance/slice\", [\"es.array.slice\"]),\n  small: define(null, [\"es.string.small\"]),\n  some: define(\"instance/some\", [\n    \"es.array.some\",\n    \"esnext.async-iterator.some\",\n    \"esnext.iterator.some\",\n    ...IteratorDependencies,\n  ]),\n  sort: define(\"instance/sort\", [\"es.array.sort\"]),\n  splice: define(\"instance/splice\", [\"es.array.splice\"]),\n  split: define(null, [\"es.string.split\", \"es.regexp.exec\"]),\n  startsWith: define(\"instance/starts-with\", [\"es.string.starts-with\"]),\n  sticky: define(null, [\"es.regexp.sticky\"]),\n  strike: define(null, [\"es.string.strike\"]),\n  sub: define(null, [\"es.string.sub\"]),\n  substr: define(null, [\"es.string.substr\"]),\n  sup: define(null, [\"es.string.sup\"]),\n  take: define(null, [\n    \"esnext.async-iterator.take\",\n    ...AsyncIteratorDependencies,\n    \"esnext.iterator.take\",\n    ...IteratorDependencies,\n  ]),\n  test: define(null, [\"es.regexp.test\", \"es.regexp.exec\"]),\n  toArray: define(null, [\n    \"esnext.async-iterator.to-array\",\n    ...AsyncIteratorDependencies,\n    \"esnext.iterator.to-array\",\n    ...IteratorDependencies,\n  ]),\n  toAsync: define(null, [\n    \"esnext.iterator.to-async\",\n    ...IteratorDependencies,\n    ...AsyncIteratorDependencies,\n    ...AsyncIteratorProblemMethods,\n  ]),\n  toExponential: define(null, [\"es.number.to-exponential\"]),\n  toFixed: define(null, [\"es.number.to-fixed\"]),\n  toGMTString: define(null, [\"es.date.to-gmt-string\"]),\n  toISOString: define(null, [\"es.date.to-iso-string\"]),\n  toJSON: define(null, [\"es.date.to-json\"]),\n  toPrecision: define(null, [\"es.number.to-precision\"]),\n  toReversed: define(\"instance/to-reversed\", [\"es.array.to-reversed\"]),\n  toSorted: define(\"instance/to-sorted\", [\n    \"es.array.to-sorted\",\n    \"es.array.sort\",\n  ]),\n  toSpliced: define(\"instance/to-spliced\", [\"es.array.to-spliced\"]),\n  toString: define(null, [\n    \"es.object.to-string\",\n    \"es.error.to-string\",\n    \"es.date.to-string\",\n    \"es.regexp.to-string\",\n  ]),\n  toWellFormed: define(\"instance/to-well-formed\", [\"es.string.to-well-formed\"]),\n  trim: define(\"instance/trim\", [\"es.string.trim\"]),\n  trimEnd: define(\"instance/trim-end\", [\"es.string.trim-end\"]),\n  trimLeft: define(\"instance/trim-left\", [\"es.string.trim-start\"]),\n  trimRight: define(\"instance/trim-right\", [\"es.string.trim-end\"]),\n  trimStart: define(\"instance/trim-start\", [\"es.string.trim-start\"]),\n  uniqueBy: define(\"instance/unique-by\", [\"esnext.array.unique-by\", \"es.map\"]),\n  unshift: define(\"instance/unshift\", [\"es.array.unshift\"]),\n  unThis: define(\"instance/un-this\", [\"esnext.function.un-this\"]),\n  values: define(\"instance/values\", ArrayNatureIteratorsWithTag),\n  with: define(\"instance/with\", [\"es.array.with\"]),\n  __defineGetter__: define(null, [\"es.object.define-getter\"]),\n  __defineSetter__: define(null, [\"es.object.define-setter\"]),\n  __lookupGetter__: define(null, [\"es.object.lookup-getter\"]),\n  __lookupSetter__: define(null, [\"es.object.lookup-setter\"]),\n  [\"__proto__\"]: define(null, [\"es.object.proto\"]),\n};\n", "// This file contains the list of paths supported by @babel/runtime-corejs3.\n// It must _not_ be edited, as all new features should go through direct\n// injection of core-js-pure imports.\n\nexport const stable = new Set([\n  \"array\",\n  \"array/from\",\n  \"array/is-array\",\n  \"array/of\",\n  \"clear-immediate\",\n  \"date/now\",\n  \"instance/bind\",\n  \"instance/code-point-at\",\n  \"instance/concat\",\n  \"instance/copy-within\",\n  \"instance/ends-with\",\n  \"instance/entries\",\n  \"instance/every\",\n  \"instance/fill\",\n  \"instance/filter\",\n  \"instance/find\",\n  \"instance/find-index\",\n  \"instance/flags\",\n  \"instance/flat\",\n  \"instance/flat-map\",\n  \"instance/for-each\",\n  \"instance/includes\",\n  \"instance/index-of\",\n  \"instance/keys\",\n  \"instance/last-index-of\",\n  \"instance/map\",\n  \"instance/pad-end\",\n  \"instance/pad-start\",\n  \"instance/reduce\",\n  \"instance/reduce-right\",\n  \"instance/repeat\",\n  \"instance/reverse\",\n  \"instance/slice\",\n  \"instance/some\",\n  \"instance/sort\",\n  \"instance/splice\",\n  \"instance/starts-with\",\n  \"instance/trim\",\n  \"instance/trim-end\",\n  \"instance/trim-left\",\n  \"instance/trim-right\",\n  \"instance/trim-start\",\n  \"instance/values\",\n  \"json/stringify\",\n  \"map\",\n  \"math/acosh\",\n  \"math/asinh\",\n  \"math/atanh\",\n  \"math/cbrt\",\n  \"math/clz32\",\n  \"math/cosh\",\n  \"math/expm1\",\n  \"math/fround\",\n  \"math/hypot\",\n  \"math/imul\",\n  \"math/log10\",\n  \"math/log1p\",\n  \"math/log2\",\n  \"math/sign\",\n  \"math/sinh\",\n  \"math/tanh\",\n  \"math/trunc\",\n  \"number/epsilon\",\n  \"number/is-finite\",\n  \"number/is-integer\",\n  \"number/is-nan\",\n  \"number/is-safe-integer\",\n  \"number/max-safe-integer\",\n  \"number/min-safe-integer\",\n  \"number/parse-float\",\n  \"number/parse-int\",\n  \"object/assign\",\n  \"object/create\",\n  \"object/define-properties\",\n  \"object/define-property\",\n  \"object/entries\",\n  \"object/freeze\",\n  \"object/from-entries\",\n  \"object/get-own-property-descriptor\",\n  \"object/get-own-property-descriptors\",\n  \"object/get-own-property-names\",\n  \"object/get-own-property-symbols\",\n  \"object/get-prototype-of\",\n  \"object/is\",\n  \"object/is-extensible\",\n  \"object/is-frozen\",\n  \"object/is-sealed\",\n  \"object/keys\",\n  \"object/prevent-extensions\",\n  \"object/seal\",\n  \"object/set-prototype-of\",\n  \"object/values\",\n  \"parse-float\",\n  \"parse-int\",\n  \"promise\",\n  \"queue-microtask\",\n  \"reflect/apply\",\n  \"reflect/construct\",\n  \"reflect/define-property\",\n  \"reflect/delete-property\",\n  \"reflect/get\",\n  \"reflect/get-own-property-descriptor\",\n  \"reflect/get-prototype-of\",\n  \"reflect/has\",\n  \"reflect/is-extensible\",\n  \"reflect/own-keys\",\n  \"reflect/prevent-extensions\",\n  \"reflect/set\",\n  \"reflect/set-prototype-of\",\n  \"set\",\n  \"set-immediate\",\n  \"set-interval\",\n  \"set-timeout\",\n  \"string/from-code-point\",\n  \"string/raw\",\n  \"symbol\",\n  \"symbol/async-iterator\",\n  \"symbol/for\",\n  \"symbol/has-instance\",\n  \"symbol/is-concat-spreadable\",\n  \"symbol/iterator\",\n  \"symbol/key-for\",\n  \"symbol/match\",\n  \"symbol/replace\",\n  \"symbol/search\",\n  \"symbol/species\",\n  \"symbol/split\",\n  \"symbol/to-primitive\",\n  \"symbol/to-string-tag\",\n  \"symbol/unscopables\",\n  \"url\",\n  \"url-search-params\",\n  \"weak-map\",\n  \"weak-set\",\n]);\n\nexport const proposals = new Set([\n  ...stable,\n  \"aggregate-error\",\n  \"composite-key\",\n  \"composite-symbol\",\n  \"global-this\",\n  \"instance/at\",\n  \"instance/code-points\",\n  \"instance/match-all\",\n  \"instance/replace-all\",\n  \"math/clamp\",\n  \"math/degrees\",\n  \"math/deg-per-rad\",\n  \"math/fscale\",\n  \"math/iaddh\",\n  \"math/imulh\",\n  \"math/isubh\",\n  \"math/rad-per-deg\",\n  \"math/radians\",\n  \"math/scale\",\n  \"math/seeded-prng\",\n  \"math/signbit\",\n  \"math/umulh\",\n  \"number/from-string\",\n  \"observable\",\n  \"reflect/define-metadata\",\n  \"reflect/delete-metadata\",\n  \"reflect/get-metadata\",\n  \"reflect/get-metadata-keys\",\n  \"reflect/get-own-metadata\",\n  \"reflect/get-own-metadata-keys\",\n  \"reflect/has-metadata\",\n  \"reflect/has-own-metadata\",\n  \"reflect/metadata\",\n  \"symbol/dispose\",\n  \"symbol/observable\",\n  \"symbol/pattern-match\",\n]);\n", "import type { CoreJSPolyfillDescriptor } from \"./built-in-definitions\";\nimport { types as t, type NodePath } from \"@babel/core\";\n\nexport default function canSkipPolyfill(\n  desc: CoreJSPolyfillDescriptor,\n  path: NodePath,\n) {\n  const { node, parent } = path;\n  switch (desc.name) {\n    case \"es.string.split\": {\n      if (!t.isCallExpression(parent, { callee: node })) return false;\n      if (parent.arguments.length < 1) return true;\n      const splitter = parent.arguments[0];\n      return t.isStringLiteral(splitter) || t.isTemplateLiteral(splitter);\n    }\n  }\n}\n", "import { types as t } from \"@babel/core\";\nimport corejsEntries from \"../core-js-compat/entries.js\";\n\nexport const BABEL_RUNTIME = \"@babel/runtime-corejs3\";\n\nexport function callMethod(path: any, id: t.Identifier) {\n  const { object } = path.node;\n\n  let context1, context2;\n  if (t.isIdentifier(object)) {\n    context1 = object;\n    context2 = t.cloneNode(object);\n  } else {\n    context1 = path.scope.generateDeclaredUidIdentifier(\"context\");\n    context2 = t.assignmentExpression(\"=\", t.cloneNode(context1), object);\n  }\n\n  path.replaceWith(\n    t.memberExpression(t.callExpression(id, [context2]), t.identifier(\"call\")),\n  );\n\n  path.parentPath.unshiftContainer(\"arguments\", context1);\n}\n\nexport function isCoreJSSource(source: string) {\n  if (typeof source === \"string\") {\n    source = source\n      .replace(/\\\\/g, \"/\")\n      .replace(/(\\/(index)?)?(\\.js)?$/i, \"\")\n      .toLowerCase();\n  }\n\n  return (\n    Object.prototype.hasOwnProperty.call(corejsEntries, source) &&\n    corejsEntries[source]\n  );\n}\n\nexport function coreJSModule(name: string) {\n  return `core-js/modules/${name}.js`;\n}\n\nexport function coreJSPureHelper(\n  name: string,\n  useBabelRuntime: boolean,\n  ext: string,\n) {\n  return useBabelRuntime\n    ? `${BABEL_RUNTIME}/core-js/${name}${ext}`\n    : `core-js-pure/features/${name}.js`;\n}\n", "import corejs3Polyfills from \"../core-js-compat/data.js\";\nimport corejs3ShippedProposalsList from \"./shipped-proposals\";\nimport getModulesListForTargetVersion from \"../core-js-compat/get-modules-list-for-target-version.js\";\nimport {\n  BuiltIns,\n  CommonIterators,\n  PromiseDependencies,\n  PromiseDependenciesWithIterators,\n  StaticProperties,\n  InstanceProperties,\n  DecoratorMetadataDependencies,\n  type CoreJSPolyfillDescriptor,\n} from \"./built-in-definitions\";\nimport * as BabelRuntimePaths from \"./babel-runtime-corejs3-paths\";\nimport canSkipPolyfill from \"./usage-filters\";\n\nimport type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\nimport {\n  callMethod,\n  coreJSModule,\n  isCoreJSSource,\n  coreJSPureHelper,\n  BABEL_RUNTIME,\n} from \"./utils\";\n\nimport defineProvider from \"@babel/helper-define-polyfill-provider\";\n\nconst presetEnvCompat = \"#__secret_key__@babel/preset-env__compatibility\";\nconst runtimeCompat = \"#__secret_key__@babel/runtime__compatibility\";\n\ntype Options = {\n  version?: number | string;\n  proposals?: boolean;\n  shippedProposals?: boolean;\n  [presetEnvCompat]?: { noRuntimeName: boolean };\n  [runtimeCompat]: {\n    useBabelRuntime: boolean;\n    ext: string;\n  };\n};\n\nconst uniqueObjects = [\n  \"array\",\n  \"string\",\n\n  \"iterator\",\n  \"async-iterator\",\n  \"dom-collections\",\n].map(v => new RegExp(`[a-z]*\\\\.${v}\\\\..*`));\n\nconst esnextFallback = (\n  name: string,\n  cb: (name: string) => boolean,\n): boolean => {\n  if (cb(name)) return true;\n  if (!name.startsWith(\"es.\")) return false;\n  const fallback = `esnext.${name.slice(3)}`;\n  if (!corejs3Polyfills[fallback]) return false;\n  return cb(fallback);\n};\n\nexport default defineProvider<Options>(function (\n  { getUtils, method, shouldInjectPolyfill, createMetaResolver, debug, babel },\n  {\n    version = 3,\n    proposals,\n    shippedProposals,\n    [presetEnvCompat]: { noRuntimeName = false } = {},\n    [runtimeCompat]: { useBabelRuntime = false, ext = \".js\" } = {},\n  },\n) {\n  const isWebpack = babel.caller(caller => caller?.name === \"babel-loader\");\n\n  const resolve = createMetaResolver({\n    global: BuiltIns,\n    static: StaticProperties,\n    instance: InstanceProperties,\n  });\n\n  const available = new Set(getModulesListForTargetVersion(version));\n\n  function getCoreJSPureBase(useProposalBase) {\n    return useBabelRuntime\n      ? useProposalBase\n        ? `${BABEL_RUNTIME}/core-js`\n        : `${BABEL_RUNTIME}/core-js-stable`\n      : useProposalBase\n        ? \"core-js-pure/features\"\n        : \"core-js-pure/stable\";\n  }\n\n  function maybeInjectGlobalImpl(name: string, utils) {\n    if (shouldInjectPolyfill(name)) {\n      debug(name);\n      utils.injectGlobalImport(coreJSModule(name), name);\n      return true;\n    }\n    return false;\n  }\n\n  function maybeInjectGlobal(names: string[], utils, fallback = true) {\n    for (const name of names) {\n      if (fallback) {\n        esnextFallback(name, name => maybeInjectGlobalImpl(name, utils));\n      } else {\n        maybeInjectGlobalImpl(name, utils);\n      }\n    }\n  }\n\n  function maybeInjectPure(\n    desc: CoreJSPolyfillDescriptor,\n    hint,\n    utils,\n    object?,\n  ) {\n    if (\n      desc.pure &&\n      !(object && desc.exclude && desc.exclude.includes(object)) &&\n      esnextFallback(desc.name, shouldInjectPolyfill)\n    ) {\n      const { name } = desc;\n      let useProposalBase = false;\n      if (proposals || (shippedProposals && name.startsWith(\"esnext.\"))) {\n        useProposalBase = true;\n      } else if (name.startsWith(\"es.\") && !available.has(name)) {\n        useProposalBase = true;\n      }\n      if (\n        useBabelRuntime &&\n        !(\n          useProposalBase\n            ? BabelRuntimePaths.proposals\n            : BabelRuntimePaths.stable\n        ).has(desc.pure)\n      ) {\n        return;\n      }\n      const coreJSPureBase = getCoreJSPureBase(useProposalBase);\n      return utils.injectDefaultImport(\n        `${coreJSPureBase}/${desc.pure}${ext}`,\n        hint,\n      );\n    }\n  }\n\n  function isFeatureStable(name) {\n    if (name.startsWith(\"esnext.\")) {\n      const esName = `es.${name.slice(7)}`;\n      // If its imaginative esName is not in latest compat data, it means\n      // the proposal is not stage 4\n      return esName in corejs3Polyfills;\n    }\n    return true;\n  }\n\n  return {\n    name: \"corejs3\",\n\n    runtimeName: noRuntimeName ? null : BABEL_RUNTIME,\n\n    polyfills: corejs3Polyfills,\n\n    filterPolyfills(name) {\n      if (!available.has(name)) return false;\n      if (proposals || method === \"entry-global\") return true;\n      if (shippedProposals && corejs3ShippedProposalsList.has(name)) {\n        return true;\n      }\n      return isFeatureStable(name);\n    },\n\n    entryGlobal(meta, utils, path) {\n      if (meta.kind !== \"import\") return;\n\n      const modules = isCoreJSSource(meta.source);\n      if (!modules) return;\n\n      if (\n        modules.length === 1 &&\n        meta.source === coreJSModule(modules[0]) &&\n        shouldInjectPolyfill(modules[0])\n      ) {\n        // Avoid infinite loop: do not replace imports with a new copy of\n        // themselves.\n        debug(null);\n        return;\n      }\n\n      const modulesSet = new Set(modules);\n      const filteredModules = modules.filter(module => {\n        if (!module.startsWith(\"esnext.\")) return true;\n        const stable = module.replace(\"esnext.\", \"es.\");\n        if (modulesSet.has(stable) && shouldInjectPolyfill(stable)) {\n          return false;\n        }\n        return true;\n      });\n\n      maybeInjectGlobal(filteredModules, utils, false);\n      path.remove();\n    },\n\n    usageGlobal(meta, utils, path) {\n      const resolved = resolve(meta);\n      if (!resolved) return;\n\n      if (canSkipPolyfill(resolved.desc, path)) return;\n\n      let deps = resolved.desc.global;\n\n      if (\n        resolved.kind !== \"global\" &&\n        \"object\" in meta &&\n        meta.object &&\n        meta.placement === \"prototype\"\n      ) {\n        const low = meta.object.toLowerCase();\n        deps = deps.filter(m =>\n          uniqueObjects.some(v => v.test(m)) ? m.includes(low) : true,\n        );\n      }\n\n      maybeInjectGlobal(deps, utils);\n\n      return true;\n    },\n\n    usagePure(meta, utils, path) {\n      if (meta.kind === \"in\") {\n        if (meta.key === \"Symbol.iterator\") {\n          path.replaceWith(\n            t.callExpression(\n              utils.injectDefaultImport(\n                coreJSPureHelper(\"is-iterable\", useBabelRuntime, ext),\n                \"isIterable\",\n              ),\n              [(path.node as t.BinaryExpression).right], // meta.kind === \"in\" narrows this\n            ),\n          );\n        }\n        return;\n      }\n\n      if (path.parentPath.isUnaryExpression({ operator: \"delete\" })) return;\n\n      if (meta.kind === \"property\") {\n        // We can't compile destructuring and updateExpression.\n        if (!path.isMemberExpression()) return;\n        if (!path.isReferenced()) return;\n        if (path.parentPath.isUpdateExpression()) return;\n        if (t.isSuper(path.node.object)) {\n          return;\n        }\n\n        if (meta.key === \"Symbol.iterator\") {\n          if (!shouldInjectPolyfill(\"es.symbol.iterator\")) return;\n\n          const { parent, node } = path;\n          if (t.isCallExpression(parent, { callee: node })) {\n            if (parent.arguments.length === 0) {\n              path.parentPath.replaceWith(\n                t.callExpression(\n                  utils.injectDefaultImport(\n                    coreJSPureHelper(\"get-iterator\", useBabelRuntime, ext),\n                    \"getIterator\",\n                  ),\n                  [node.object],\n                ),\n              );\n              path.skip();\n            } else {\n              callMethod(\n                path,\n                utils.injectDefaultImport(\n                  coreJSPureHelper(\"get-iterator-method\", useBabelRuntime, ext),\n                  \"getIteratorMethod\",\n                ),\n              );\n            }\n          } else {\n            path.replaceWith(\n              t.callExpression(\n                utils.injectDefaultImport(\n                  coreJSPureHelper(\"get-iterator-method\", useBabelRuntime, ext),\n                  \"getIteratorMethod\",\n                ),\n                [path.node.object],\n              ),\n            );\n          }\n\n          return;\n        }\n      }\n\n      let resolved = resolve(meta);\n      if (!resolved) return;\n\n      if (canSkipPolyfill(resolved.desc, path)) return;\n\n      if (\n        useBabelRuntime &&\n        resolved.desc.pure &&\n        resolved.desc.pure.slice(-6) === \"/index\"\n      ) {\n        // Remove /index, since it doesn't exist in @babel/runtime-corejs3s\n        resolved = {\n          ...resolved,\n          desc: {\n            ...resolved.desc,\n            pure: resolved.desc.pure.slice(0, -6),\n          },\n        };\n      }\n\n      if (resolved.kind === \"global\") {\n        const id = maybeInjectPure(resolved.desc, resolved.name, utils);\n        if (id) path.replaceWith(id);\n      } else if (resolved.kind === \"static\") {\n        const id = maybeInjectPure(\n          resolved.desc,\n          resolved.name,\n          utils,\n          // @ts-expect-error\n          meta.object,\n        );\n        if (id) path.replaceWith(id);\n      } else if (resolved.kind === \"instance\") {\n        const id = maybeInjectPure(\n          resolved.desc,\n          `${resolved.name}InstanceProperty`,\n          utils,\n          // @ts-expect-error\n          meta.object,\n        );\n        if (!id) return;\n\n        const { node } = path as NodePath<t.MemberExpression>;\n        if (t.isCallExpression(path.parent, { callee: node })) {\n          callMethod(path, id);\n        } else {\n          path.replaceWith(t.callExpression(id, [node.object]));\n        }\n      }\n    },\n\n    visitor: method === \"usage-global\" && {\n      // import(\"foo\")\n      CallExpression(path: NodePath<t.CallExpression>) {\n        if (path.get(\"callee\").isImport()) {\n          const utils = getUtils(path);\n\n          if (isWebpack) {\n            // Webpack uses Promise.all to handle dynamic import.\n            maybeInjectGlobal(PromiseDependenciesWithIterators, utils);\n          } else {\n            maybeInjectGlobal(PromiseDependencies, utils);\n          }\n        }\n      },\n\n      // (async function () { }).finally(...)\n      Function(path: NodePath<t.Function>) {\n        if (path.node.async) {\n          maybeInjectGlobal(PromiseDependencies, getUtils(path));\n        }\n      },\n\n      // for-of, [a, b] = c\n      \"ForOfStatement|ArrayPattern\"(\n        path: NodePath<t.ForOfStatement | t.ArrayPattern>,\n      ) {\n        maybeInjectGlobal(CommonIterators, getUtils(path));\n      },\n\n      // [...spread]\n      SpreadElement(path: NodePath<t.SpreadElement>) {\n        if (!path.parentPath.isObjectExpression()) {\n          maybeInjectGlobal(CommonIterators, getUtils(path));\n        }\n      },\n\n      // yield*\n      YieldExpression(path: NodePath<t.YieldExpression>) {\n        if (path.node.delegate) {\n          maybeInjectGlobal(CommonIterators, getUtils(path));\n        }\n      },\n\n      // Decorators metadata\n      Class(path: NodePath<t.Class>) {\n        const hasDecorators =\n          path.node.decorators?.length ||\n          path.node.body.body.some(\n            el => (el as t.ClassMethod).decorators?.length,\n          );\n        if (hasDecorators) {\n          maybeInjectGlobal(DecoratorMetadataDependencies, getUtils(path));\n        }\n      },\n    },\n  };\n});\n"], "names": ["Set", "polyfillsOrder", "Object", "keys", "corejs3Polyfills", "for<PERSON>ach", "name", "index", "define", "pure", "global", "exclude", "sort", "a", "b", "typed", "modules", "TypedArrayDependencies", "ArrayNatureIterators", "CommonIterators", "ArrayNatureIteratorsWithTag", "CommonIteratorsWithTag", "ErrorDependencies", "SuppressedErrorDependencies", "PromiseDependencies", "PromiseDependenciesWithIterators", "SymbolDependencies", "MapDependencies", "SetDependencies", "WeakMapDependencies", "WeakSetDependencies", "DOMExceptionDependencies", "URLSearchParamsDependencies", "AsyncIteratorDependencies", "AsyncIteratorProblemMethods", "IteratorDependencies", "DecoratorMetadataDependencies", "TypedArrayStaticMethods", "base", "from", "fromAsync", "of", "DataViewDependencies", "BuiltIns", "AsyncDisposableStack", "AsyncIterator", "AggregateError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "Date", "DOMException", "DisposableStack", "Error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Iterator", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "Map", "Number", "Observable", "Promise", "RangeError", "ReferenceError", "Reflect", "RegExp", "SuppressedError", "Symbol", "SyntaxError", "TypeError", "URIError", "URL", "URLSearchParams", "WeakMap", "WeakSet", "atob", "btoa", "clearImmediate", "compositeKey", "compositeSymbol", "escape", "fetch", "globalThis", "parseFloat", "parseInt", "queueMicrotask", "self", "setImmediate", "setInterval", "setTimeout", "structuredClone", "unescape", "StaticProperties", "Array", "isArray", "isTemplateObject", "<PERSON><PERSON><PERSON><PERSON>", "BigInt", "range", "now", "Function", "isCallable", "isConstructor", "JSON", "isRawJSON", "parse", "rawJSON", "stringify", "Math", "DEG_PER_RAD", "RAD_PER_DEG", "acosh", "asinh", "atanh", "cbrt", "clamp", "clz32", "cosh", "degrees", "expm1", "fround", "f16round", "fscale", "hypot", "iaddh", "imul", "imulh", "<PERSON><PERSON><PERSON>", "log10", "log1p", "log2", "radians", "scale", "seededPRNG", "sign", "signbit", "sinh", "tanh", "trunc", "umulh", "groupBy", "keyBy", "EPSILON", "MAX_SAFE_INTEGER", "MIN_SAFE_INTEGER", "fromString", "isFinite", "isInteger", "isNaN", "isSafeInteger", "assign", "create", "defineProperties", "defineProperty", "entries", "freeze", "fromEntries", "getOwnPropertyDescriptor", "getOwnPropertyDescriptors", "getOwnPropertyNames", "getOwnPropertySymbols", "getPrototypeOf", "hasOwn", "is", "isExtensible", "isFrozen", "isSealed", "preventExtensions", "seal", "setPrototypeOf", "values", "all", "allSettled", "any", "race", "try", "withResolvers", "apply", "construct", "defineMetadata", "deleteMetadata", "deleteProperty", "get", "getMetadata", "getMetadataKeys", "getOwnMetadata", "getOwnMetadataKeys", "has", "hasMetadata", "hasOwnMetadata", "metadata", "ownKeys", "set", "String", "cooked", "dedent", "fromCodePoint", "raw", "asyncDispose", "asyncIterator", "dispose", "for", "hasInstance", "isConcatSpreadable", "isRegistered", "isRegisteredSymbol", "isWellKnown", "isWellKnownSymbol", "iterator", "keyFor", "match", "matcher", "matchAll", "metadataKey", "observable", "patternMatch", "replace", "search", "species", "split", "toPrimitive", "toStringTag", "unscopables", "canParse", "fromBase64", "fromHex", "WebAssembly", "CompileError", "LinkError", "RuntimeError", "InstanceProperties", "asIndexedPairs", "at", "anchor", "big", "bind", "blink", "bold", "codePointAt", "codePoints", "concat", "undefined", "copyWithin", "demethodize", "description", "dotAll", "drop", "emplace", "endsWith", "every", "exec", "fill", "filter", "filterReject", "finally", "find", "findIndex", "findLast", "findLastIndex", "fixed", "flags", "flatMap", "flat", "getFloat16", "getUint8Clamped", "getYear", "group", "groupByToMap", "groupToMap", "fontcolor", "fontsize", "includes", "indexed", "indexOf", "isWellFormed", "italic", "join", "lastIndex", "lastIndexOf", "lastItem", "link", "map", "padEnd", "padStart", "push", "reduce", "reduceRight", "repeat", "replaceAll", "reverse", "setFloat16", "setUint8Clamped", "setYear", "slice", "small", "some", "splice", "startsWith", "sticky", "strike", "sub", "substr", "sup", "take", "test", "toArray", "to<PERSON><PERSON>", "toExponential", "toFixed", "toGMTString", "toISOString", "toJSON", "toPrecision", "toReversed", "toSorted", "toSpliced", "toString", "to<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim", "trimEnd", "trimLeft", "trimRight", "trimStart", "uniqueBy", "unshift", "unThis", "with", "__defineGetter__", "__defineSetter__", "__lookupGetter__", "__lookupSetter__", "stable", "proposals", "types", "t", "_babel", "default", "canSkipPolyfill", "desc", "path", "node", "parent", "isCallExpression", "callee", "arguments", "length", "splitter", "isStringLiteral", "isTemplateLiteral", "BABEL_RUNTIME", "callMethod", "id", "object", "context1", "context2", "isIdentifier", "cloneNode", "scope", "generateDeclaredUidIdentifier", "assignmentExpression", "replaceWith", "memberExpression", "callExpression", "identifier", "parentPath", "unshiftContainer", "isCoreJSSource", "source", "toLowerCase", "prototype", "hasOwnProperty", "call", "corejsEntries", "coreJSModule", "coreJSPureHelper", "useBabelRuntime", "ext", "presetEnvCompat", "runtimeCompat", "uniqueObjects", "v", "esnextFallback", "cb", "fallback", "define<PERSON>rovider", "getUtils", "method", "shouldInjectPolyfill", "createMetaResolver", "debug", "babel", "version", "shippedProposals", "noRuntimeName", "isWebpack", "caller", "resolve", "static", "instance", "available", "getModulesListForTargetVersion", "getCoreJSPureBase", "useProposalBase", "maybeInjectGlobalImpl", "utils", "injectGlobalImport", "maybeInjectGlobal", "names", "maybeInjectPure", "hint", "BabelRuntimePaths", "coreJSPureBase", "injectDefaultImport", "isFeatureStable", "esName", "runtimeName", "polyfills", "filterPolyfills", "corejs3ShippedProposalsList", "entryGlobal", "meta", "kind", "modulesSet", "filteredModules", "module", "remove", "usageGlobal", "resolved", "deps", "placement", "low", "m", "usagePure", "key", "right", "isUnaryExpression", "operator", "isMemberExpression", "isReferenced", "isUpdateExpression", "is<PERSON><PERSON><PERSON>", "skip", "visitor", "CallExpression", "isImport", "async", "ForOfStatement|ArrayPattern", "SpreadElement", "isObjectExpression", "YieldExpression", "delegate", "Class", "_path$node$decorators", "hasDecorators", "decorators", "body", "el", "_decorators"], "mappings": ";;;;;;AAAA;;AAEA,kCAAe,IAAIA,GAAG,CAAS,CAC7B,qCAAqC,EACrC,yBAAyB,EACzB,oBAAoB,EACpB,2BAA2B,EAC3B,6BAA6B,EAC7B,sBAAsB,EACtB,uBAAuB,EACvB,wBAAwB,EACxB,sBAAsB,EACtB,0BAA0B,EAC1B,0BAA0B,EAC1B,sBAAsB,EACtB,qBAAqB,EACrB,wBAAwB,EACxB,sBAAsB,EACtB,sBAAsB,EACtB,0BAA0B,EAC1B,yBAAyB,EACzB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,4BAA4B,EAC5B,gCAAgC,EAChC,4BAA4B,EAC5B,8BAA8B,EAC9B,oCAAoC,EACpC,qBAAqB,EACrB,6BAA6B,EAC7B,uBAAuB,EACvB,wBAAwB,CACzB,CAAC;;ACrBF,MAAMC,cAAc,GAAG,EAAE;AACzBC,MAAM,CAACC,IAAI,CAACC,gBAAgB,CAAC,CAACC,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;EACrDN,cAAc,CAACK,IAAI,CAAC,GAAGC,KAAK;AAC9B,CAAC,CAAC;AAEF,MAAMC,MAAM,GAAGA,CACbC,IAAI,EACJC,MAAM,EACNJ,IAAI,GAAGI,MAAM,CAAC,CAAC,CAAC,EAChBC,OAAQ,KACqB;EAC7B,OAAO;IACLL,IAAI;IACJG,IAAI;IACJC,MAAM,EAAEA,MAAM,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKb,cAAc,CAACY,CAAC,CAAC,GAAGZ,cAAc,CAACa,CAAC,CAAC,CAAC;IACpEH;GACD;AACH,CAAC;AAED,MAAMI,KAAK,GAAGA,CAAC,GAAGC,OAAO,KACvBR,MAAM,CAAC,IAAI,EAAE,CAAC,GAAGQ,OAAO,EAAE,GAAGC,sBAAsB,CAAC,CAAC;AAEvD,MAAMC,oBAAoB,GAAG,CAC3B,mBAAmB,EACnB,8BAA8B,CAC/B;AAEM,MAAMC,eAAe,GAAG,CAAC,oBAAoB,EAAE,GAAGD,oBAAoB,CAAC;AAE9E,MAAME,2BAA2B,GAAG,CAClC,qBAAqB,EACrB,GAAGF,oBAAoB,CACxB;AAED,MAAMG,sBAAsB,GAAG,CAAC,qBAAqB,EAAE,GAAGF,eAAe,CAAC;AAE1E,MAAMG,iBAAiB,GAAG,CAAC,gBAAgB,EAAE,oBAAoB,CAAC;AAElE,MAAMC,2BAA2B,GAAG,CAClC,qCAAqC,EACrC,GAAGD,iBAAiB,CACrB;AAED,MAAML,sBAAsB,GAAG,CAC7B,mBAAmB,EACnB,4BAA4B,EAC5B,sBAAsB,EACtB,qBAAqB,EACrB,uBAAuB,EACvB,qBAAqB,EACrB,2BAA2B,EAC3B,0BAA0B,EAC1B,gCAAgC,EAChC,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,qBAAqB,EACrB,8BAA8B,EAC9B,oBAAoB,EACpB,uBAAuB,EACvB,6BAA6B,EAC7B,wBAAwB,EACxB,oBAAoB,EACpB,sBAAsB,EACtB,qBAAqB,EACrB,qBAAqB,EACrB,yBAAyB,EACzB,iCAAiC,EACjC,4BAA4B,EAC5B,0BAA0B,EAC1B,0BAA0B,EAC1B,qBAAqB,EACrB,qBAAqB,EACrB,mBAAmB,EACnB,uBAAuB,EACvB,cAAc,EACd,0BAA0B,EAC1B,0BAA0B,EAC1B,0CAA0C,EAC1C,kCAAkC,EAClC,6BAA6B,EAC7B,+BAA+B,EAC/B,8BAA8B,CAC/B;AAEM,MAAMO,mBAAmB,GAAG,CAAC,YAAY,EAAE,qBAAqB,CAAC;AAEjE,MAAMC,gCAAgC,GAAG,CAC9C,GAAGD,mBAAmB,EACtB,GAAGL,eAAe,CACnB;AAED,MAAMO,kBAAkB,GAAG,CACzB,WAAW,EACX,uBAAuB,EACvB,qBAAqB,CACtB;AAED,MAAMC,eAAe,GAAG,CACtB,QAAQ,EACR,uBAAuB,EACvB,oBAAoB,EACpB,kBAAkB,EAClB,mBAAmB,EACnB,iBAAiB,EACjB,qBAAqB,EACrB,qBAAqB,EACrB,mBAAmB,EACnB,qBAAqB,EACrB,uBAAuB,EACvB,kBAAkB,EAClB,mBAAmB,EACnB,iBAAiB,EACjB,mBAAmB,EACnB,GAAGN,sBAAsB,CAC1B;AAED,MAAMO,eAAe,GAAG,CACtB,QAAQ,EACR,oBAAoB,EACpB,uBAAuB,EACvB,uBAAuB,EACvB,0BAA0B,EAC1B,kBAAkB,EAClB,mBAAmB,EACnB,iBAAiB,EACjB,yBAAyB,EACzB,4BAA4B,EAC5B,6BAA6B,EAC7B,gCAAgC,EAChC,yBAAyB,EACzB,4BAA4B,EAC5B,2BAA2B,EAC3B,8BAA8B,EAC9B,iBAAiB,EACjB,gBAAgB,EAChB,mBAAmB,EACnB,iBAAiB,EACjB,iCAAiC,EACjC,oCAAoC,EACpC,kBAAkB,EAClB,qBAAqB,EACrB,GAAGP,sBAAsB,CAC1B;AAED,MAAMQ,mBAAmB,GAAG,CAC1B,aAAa,EACb,4BAA4B,EAC5B,yBAAyB,EACzB,GAAGR,sBAAsB,CAC1B;AAED,MAAMS,mBAAmB,GAAG,CAC1B,aAAa,EACb,yBAAyB,EACzB,4BAA4B,EAC5B,GAAGT,sBAAsB,CAC1B;AAED,MAAMU,wBAAwB,GAAG,CAC/B,+BAA+B,EAC/B,yBAAyB,EACzB,iCAAiC,EACjC,oBAAoB,CACrB;AAED,MAAMC,2BAA2B,GAAG,CAClC,uBAAuB,EACvB,8BAA8B,EAC9B,2BAA2B,EAC3B,4BAA4B,EAC5B,GAAGX,sBAAsB,CAC1B;AAED,MAAMY,yBAAyB,GAAG,CAChC,mCAAmC,EACnC,GAAGT,mBAAmB,CACvB;AAED,MAAMU,2BAA2B,GAAG,CAClC,6BAA6B,EAC7B,8BAA8B,EAC9B,4BAA4B,EAC5B,gCAAgC,EAChC,gCAAgC,EAChC,2BAA2B,EAC3B,8BAA8B,EAC9B,4BAA4B,CAC7B;AAED,MAAMC,oBAAoB,GAAG,CAC3B,6BAA6B,EAC7B,qBAAqB,CACtB;AAEM,MAAMC,6BAA6B,GAAG,CAC3C,wBAAwB,EACxB,0BAA0B,CAC3B;AAED,MAAMC,uBAAuB,GAAIC,IAAY,KAAM;EACjDC,IAAI,EAAE/B,MAAM,CAAC,IAAI,EAAE,CAAC,qBAAqB,EAAE8B,IAAI,EAAE,GAAGrB,sBAAsB,CAAC,CAAC;EAC5EuB,SAAS,EAAEhC,MAAM,CAAC,IAAI,EAAE,CACtB,+BAA+B,EAC/B8B,IAAI,EACJ,GAAGb,gCAAgC,EACnC,GAAGR,sBAAsB,CAC1B,CAAC;EACFwB,EAAE,EAAEjC,MAAM,CAAC,IAAI,EAAE,CAAC,mBAAmB,EAAE8B,IAAI,EAAE,GAAGrB,sBAAsB,CAAC;AACzE,CAAC,CAAC;AAEF,MAAMyB,oBAAoB,GAAG,CAC3B,cAAc,EACd,6BAA6B,EAC7B,uBAAuB,EACvB,0BAA0B,EAC1B,0BAA0B,EAC1B,0CAA0C,EAC1C,qBAAqB,CACtB;AAEM,MAAMC,QAA6C,GAAG;EAC3DC,oBAAoB,EAAEpC,MAAM,CAAC,8BAA8B,EAAE,CAC3D,2CAA2C,EAC3C,qBAAqB,EACrB,qCAAqC,EACrC,yBAAyB,EACzB,GAAGgB,mBAAmB,EACtB,GAAGD,2BAA2B,CAC/B,CAAC;EACFsB,aAAa,EAAErC,MAAM,CAAC,sBAAsB,EAAEyB,yBAAyB,CAAC;EACxEa,cAAc,EAAEtC,MAAM,CAAC,iBAAiB,EAAE,CACxC,oBAAoB,EACpB,GAAGc,iBAAiB,EACpB,GAAGD,sBAAsB,EACzB,0BAA0B,CAC3B,CAAC;EACF0B,WAAW,EAAEvC,MAAM,CAAC,IAAI,EAAE,CACxB,6BAA6B,EAC7B,uBAAuB,EACvB,cAAc,EACd,0BAA0B,EAC1B,0BAA0B,EAC1B,0CAA0C,EAC1C,qBAAqB,CACtB,CAAC;EACFwC,QAAQ,EAAExC,MAAM,CAAC,IAAI,EAAEkC,oBAAoB,CAAC;EAC5CO,IAAI,EAAEzC,MAAM,CAAC,IAAI,EAAE,CAAC,mBAAmB,CAAC,CAAC;EACzC0C,YAAY,EAAE1C,MAAM,CAAC,qBAAqB,EAAEuB,wBAAwB,CAAC;EACrEoB,eAAe,EAAE3C,MAAM,CAAC,wBAAwB,EAAE,CAChD,qCAAqC,EACrC,qBAAqB,EACrB,yBAAyB,EACzB,GAAGe,2BAA2B,CAC/B,CAAC;EACF6B,KAAK,EAAE5C,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;EACtC+B,SAAS,EAAE7C,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;EAC1CgC,YAAY,EAAEvC,KAAK,CAAC,8BAA8B,CAAC;EACnDwC,YAAY,EAAExC,KAAK,CAAC,8BAA8B,CAAC;EACnDyC,SAAS,EAAEzC,KAAK,CAAC,2BAA2B,CAAC;EAC7C0C,UAAU,EAAE1C,KAAK,CAAC,4BAA4B,CAAC;EAC/C2C,UAAU,EAAE3C,KAAK,CAAC,4BAA4B,CAAC;EAC/C4C,QAAQ,EAAEnD,MAAM,CAAC,gBAAgB,EAAE2B,oBAAoB,CAAC;EACxDyB,UAAU,EAAE7C,KAAK,CACf,4BAA4B,EAC5B,8BAA8B,EAC9B,2BACF,CAAC;EACD8C,iBAAiB,EAAE9C,KAAK,CAAC,oCAAoC,CAAC;EAC9D+C,WAAW,EAAE/C,KAAK,CAAC,6BAA6B,CAAC;EACjDgD,WAAW,EAAEhD,KAAK,CAAC,6BAA6B,CAAC;EACjDiD,GAAG,EAAExD,MAAM,CAAC,WAAW,EAAEmB,eAAe,CAAC;EACzCsC,MAAM,EAAEzD,MAAM,CAAC,IAAI,EAAE,CAAC,uBAAuB,CAAC,CAAC;EAC/C0D,UAAU,EAAE1D,MAAM,CAAC,kBAAkB,EAAE,CACrC,mBAAmB,EACnB,0BAA0B,EAC1B,qBAAqB,EACrB,GAAGa,sBAAsB,CAC1B,CAAC;EACF8C,OAAO,EAAE3D,MAAM,CAAC,eAAe,EAAEgB,mBAAmB,CAAC;EACrD4C,UAAU,EAAE5D,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;EAC3C+C,cAAc,EAAE7D,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;EAC/CgD,OAAO,EAAE9D,MAAM,CAAC,IAAI,EAAE,CAAC,0BAA0B,EAAE,qBAAqB,CAAC,CAAC;EAC1E+D,MAAM,EAAE/D,MAAM,CAAC,IAAI,EAAE,CACnB,uBAAuB,EACvB,mBAAmB,EACnB,gBAAgB,EAChB,kBAAkB,EAClB,qBAAqB,CACtB,CAAC;EACFR,GAAG,EAAEQ,MAAM,CAAC,WAAW,EAAEoB,eAAe,CAAC;EACzC4C,eAAe,EAAEhE,MAAM,CAAC,kBAAkB,EAAEe,2BAA2B,CAAC;EACxEkD,MAAM,EAAEjE,MAAM,CAAC,cAAc,EAAEkB,kBAAkB,CAAC;EAClDgD,WAAW,EAAElE,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;EAC5CqD,SAAS,EAAEnE,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;EAC1CsD,QAAQ,EAAEpE,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;EACzCuD,GAAG,EAAErE,MAAM,CAAC,WAAW,EAAE,CACvB,SAAS,EACT,iBAAiB,EACjB,GAAGwB,2BAA2B,CAC/B,CAAC;EACF8C,eAAe,EAAEtE,MAAM,CACrB,yBAAyB,EACzBwB,2BACF,CAAC;EACD+C,OAAO,EAAEvE,MAAM,CAAC,gBAAgB,EAAEqB,mBAAmB,CAAC;EACtDmD,OAAO,EAAExE,MAAM,CAAC,gBAAgB,EAAEsB,mBAAmB,CAAC;EAEtDmD,IAAI,EAAEzE,MAAM,CAAC,MAAM,EAAE,CAAC,UAAU,EAAE,GAAGuB,wBAAwB,CAAC,CAAC;EAC/DmD,IAAI,EAAE1E,MAAM,CAAC,MAAM,EAAE,CAAC,UAAU,EAAE,GAAGuB,wBAAwB,CAAC,CAAC;EAC/DoD,cAAc,EAAE3E,MAAM,CAAC,iBAAiB,EAAE,CAAC,eAAe,CAAC,CAAC;EAC5D4E,YAAY,EAAE5E,MAAM,CAAC,eAAe,EAAE,CAAC,sBAAsB,CAAC,CAAC;EAC/D6E,eAAe,EAAE7E,MAAM,CAAC,kBAAkB,EAAE,CAAC,yBAAyB,CAAC,CAAC;EACxE8E,MAAM,EAAE9E,MAAM,CAAC,QAAQ,EAAE,CAAC,WAAW,CAAC,CAAC;EACvC+E,KAAK,EAAE/E,MAAM,CAAC,IAAI,EAAEgB,mBAAmB,CAAC;EACxCgE,UAAU,EAAEhF,MAAM,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;EACrDiF,UAAU,EAAEjF,MAAM,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;EACrDkF,QAAQ,EAAElF,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;EAC/CmF,cAAc,EAAEnF,MAAM,CAAC,iBAAiB,EAAE,CAAC,qBAAqB,CAAC,CAAC;EAClEoF,IAAI,EAAEpF,MAAM,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,CAAC;EAClCqF,YAAY,EAAErF,MAAM,CAAC,eAAe,EAAE,CAAC,eAAe,CAAC,CAAC;EACxDsF,WAAW,EAAEtF,MAAM,CAAC,cAAc,EAAE,CAAC,YAAY,CAAC,CAAC;EACnDuF,UAAU,EAAEvF,MAAM,CAAC,aAAa,EAAE,CAAC,YAAY,CAAC,CAAC;EACjDwF,eAAe,EAAExF,MAAM,CAAC,kBAAkB,EAAE,CAC1C,sBAAsB,EACtB,GAAGuB,wBAAwB,EAC3B,mBAAmB,EACnB,gBAAgB,EAChB,qBAAqB,EACrB,QAAQ,EACR,QAAQ,CACT,CAAC;EACFkE,QAAQ,EAAEzF,MAAM,CAAC,UAAU,EAAE,CAAC,aAAa,CAAC;AAC9C,CAAC;AAEM,MAAM0F,gBAAsD,GAAG;EACpErD,aAAa,EAAE;IACbN,IAAI,EAAE/B,MAAM,CAAC,qBAAqB,EAAE,CAClC,4BAA4B,EAC5B,GAAGyB,yBAAyB,EAC5B,GAAGC,2BAA2B,EAC9B,GAAGf,eAAe,CACnB;GACF;EACDgF,KAAK,EAAE;IACL5D,IAAI,EAAE/B,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,EAAE,oBAAoB,CAAC,CAAC;IACnEgC,SAAS,EAAEhC,MAAM,CAAC,kBAAkB,EAAE,CACpC,yBAAyB,EACzB,GAAGiB,gCAAgC,CACpC,CAAC;IACF2E,OAAO,EAAE5F,MAAM,CAAC,gBAAgB,EAAE,CAAC,mBAAmB,CAAC,CAAC;IACxD6F,gBAAgB,EAAE7F,MAAM,CAAC,0BAA0B,EAAE,CACnD,iCAAiC,CAClC,CAAC;IACFiC,EAAE,EAAEjC,MAAM,CAAC,UAAU,EAAE,CAAC,aAAa,CAAC;GACvC;EAEDuC,WAAW,EAAE;IACXuD,MAAM,EAAE9F,MAAM,CAAC,IAAI,EAAE,CAAC,yBAAyB,CAAC;GACjD;EAED+F,MAAM,EAAE;IACNC,KAAK,EAAEhG,MAAM,CAAC,cAAc,EAAE,CAC5B,qBAAqB,EACrB,qBAAqB,CACtB;GACF;EAEDyC,IAAI,EAAE;IACJwD,GAAG,EAAEjG,MAAM,CAAC,UAAU,EAAE,CAAC,aAAa,CAAC;GACxC;EAEDkG,QAAQ,EAAE;IACRC,UAAU,EAAEnG,MAAM,CAAC,sBAAsB,EAAE,CAAC,6BAA6B,CAAC,CAAC;IAC3EoG,aAAa,EAAEpG,MAAM,CAAC,yBAAyB,EAAE,CAC/C,gCAAgC,CACjC;GACF;EAEDmD,QAAQ,EAAE;IACRpB,IAAI,EAAE/B,MAAM,CAAC,eAAe,EAAE,CAC5B,sBAAsB,EACtB,GAAG2B,oBAAoB,EACvB,GAAGhB,eAAe,CACnB,CAAC;IACFqF,KAAK,EAAEhG,MAAM,CAAC,gBAAgB,EAAE,CAC9B,uBAAuB,EACvB,qBAAqB,CACtB;GACF;EAEDqG,IAAI,EAAE;IACJC,SAAS,EAAEtG,MAAM,CAAC,kBAAkB,EAAE,CAAC,yBAAyB,CAAC,CAAC;IAClEuG,KAAK,EAAEvG,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;IACpEwG,OAAO,EAAExG,MAAM,CAAC,eAAe,EAAE,CAC/B,sBAAsB,EACtB,kBAAkB,EAClB,kBAAkB,CACnB,CAAC;IACFyG,SAAS,EAAEzG,MAAM,CACf,gBAAgB,EAChB,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,EACxC,WACF;GACD;EAED0G,IAAI,EAAE;IACJC,WAAW,EAAE3G,MAAM,CAAC,kBAAkB,EAAE,CAAC,yBAAyB,CAAC,CAAC;IACpE4G,WAAW,EAAE5G,MAAM,CAAC,kBAAkB,EAAE,CAAC,yBAAyB,CAAC,CAAC;IACpE6G,KAAK,EAAE7G,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9C8G,KAAK,EAAE9G,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9C+G,KAAK,EAAE/G,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9CgH,IAAI,EAAEhH,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;IAC3CiH,KAAK,EAAEjH,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAClDkH,KAAK,EAAElH,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9CmH,IAAI,EAAEnH,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;IAC3CoH,OAAO,EAAEpH,MAAM,CAAC,cAAc,EAAE,CAAC,qBAAqB,CAAC,CAAC;IACxDqH,KAAK,EAAErH,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9CsH,MAAM,EAAEtH,MAAM,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IACjDuH,QAAQ,EAAEvH,MAAM,CAAC,eAAe,EAAE,CAAC,sBAAsB,CAAC,CAAC;IAC3DwH,MAAM,EAAExH,MAAM,CAAC,aAAa,EAAE,CAAC,oBAAoB,CAAC,CAAC;IACrDyH,KAAK,EAAEzH,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9C0H,KAAK,EAAE1H,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAClD2H,IAAI,EAAE3H,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;IAC3C4H,KAAK,EAAE5H,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAClD6H,KAAK,EAAE7H,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAClD8H,KAAK,EAAE9H,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9C+H,KAAK,EAAE/H,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9CgI,IAAI,EAAEhI,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;IAC3CiI,OAAO,EAAEjI,MAAM,CAAC,cAAc,EAAE,CAAC,qBAAqB,CAAC,CAAC;IACxDkI,KAAK,EAAElI,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAClDmI,UAAU,EAAEnI,MAAM,CAAC,kBAAkB,EAAE,CAAC,yBAAyB,CAAC,CAAC;IACnEoI,IAAI,EAAEpI,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;IAC3CqI,OAAO,EAAErI,MAAM,CAAC,cAAc,EAAE,CAAC,qBAAqB,CAAC,CAAC;IACxDsI,IAAI,EAAEtI,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;IAC3CuI,IAAI,EAAEvI,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;IAC3CwI,KAAK,EAAExI,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9CyI,KAAK,EAAEzI,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC;GAClD;EAEDwD,GAAG,EAAE;IACHzB,IAAI,EAAE/B,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,EAAE,GAAGmB,eAAe,CAAC,CAAC;IAC3DuH,OAAO,EAAE1I,MAAM,CAAC,cAAc,EAAE,CAAC,iBAAiB,EAAE,GAAGmB,eAAe,CAAC,CAAC;IACxEwH,KAAK,EAAE3I,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,EAAE,GAAGmB,eAAe,CAAC,CAAC;IACtEc,EAAE,EAAEjC,MAAM,CAAC,IAAI,EAAE,CAAC,eAAe,EAAE,GAAGmB,eAAe,CAAC;GACvD;EAEDsC,MAAM,EAAE;IACNmF,OAAO,EAAE5I,MAAM,CAAC,gBAAgB,EAAE,CAAC,mBAAmB,CAAC,CAAC;IACxD6I,gBAAgB,EAAE7I,MAAM,CAAC,yBAAyB,EAAE,CAClD,4BAA4B,CAC7B,CAAC;IACF8I,gBAAgB,EAAE9I,MAAM,CAAC,yBAAyB,EAAE,CAClD,4BAA4B,CAC7B,CAAC;IACF+I,UAAU,EAAE/I,MAAM,CAAC,oBAAoB,EAAE,CAAC,2BAA2B,CAAC,CAAC;IACvEgJ,QAAQ,EAAEhJ,MAAM,CAAC,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,CAAC;IAC7DiJ,SAAS,EAAEjJ,MAAM,CAAC,mBAAmB,EAAE,CAAC,sBAAsB,CAAC,CAAC;IAChEkJ,KAAK,EAAElJ,MAAM,CAAC,eAAe,EAAE,CAAC,kBAAkB,CAAC,CAAC;IACpDmJ,aAAa,EAAEnJ,MAAM,CAAC,wBAAwB,EAAE,CAC9C,2BAA2B,CAC5B,CAAC;IACFiF,UAAU,EAAEjF,MAAM,CAAC,oBAAoB,EAAE,CAAC,uBAAuB,CAAC,CAAC;IACnEkF,QAAQ,EAAElF,MAAM,CAAC,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,CAAC;IAC7DgG,KAAK,EAAEhG,MAAM,CAAC,cAAc,EAAE,CAC5B,qBAAqB,EACrB,qBAAqB,CACtB;GACF;EAEDN,MAAM,EAAE;IACN0J,MAAM,EAAEpJ,MAAM,CAAC,eAAe,EAAE,CAAC,kBAAkB,CAAC,CAAC;IACrDqJ,MAAM,EAAErJ,MAAM,CAAC,eAAe,EAAE,CAAC,kBAAkB,CAAC,CAAC;IACrDsJ,gBAAgB,EAAEtJ,MAAM,CAAC,0BAA0B,EAAE,CACnD,6BAA6B,CAC9B,CAAC;IACFuJ,cAAc,EAAEvJ,MAAM,CAAC,wBAAwB,EAAE,CAC/C,2BAA2B,CAC5B,CAAC;IACFwJ,OAAO,EAAExJ,MAAM,CAAC,gBAAgB,EAAE,CAAC,mBAAmB,CAAC,CAAC;IACxDyJ,MAAM,EAAEzJ,MAAM,CAAC,eAAe,EAAE,CAAC,kBAAkB,CAAC,CAAC;IACrD0J,WAAW,EAAE1J,MAAM,CAAC,qBAAqB,EAAE,CACzC,wBAAwB,EACxB,mBAAmB,CACpB,CAAC;IACF2J,wBAAwB,EAAE3J,MAAM,CAAC,oCAAoC,EAAE,CACrE,uCAAuC,CACxC,CAAC;IACF4J,yBAAyB,EAAE5J,MAAM,CAAC,qCAAqC,EAAE,CACvE,wCAAwC,CACzC,CAAC;IACF6J,mBAAmB,EAAE7J,MAAM,CAAC,+BAA+B,EAAE,CAC3D,kCAAkC,CACnC,CAAC;IACF8J,qBAAqB,EAAE9J,MAAM,CAAC,iCAAiC,EAAE,CAC/D,WAAW,CACZ,CAAC;IACF+J,cAAc,EAAE/J,MAAM,CAAC,yBAAyB,EAAE,CAChD,4BAA4B,CAC7B,CAAC;IACF0I,OAAO,EAAE1I,MAAM,CAAC,iBAAiB,EAAE,CACjC,oBAAoB,EACpB,kBAAkB,CACnB,CAAC;IACFgK,MAAM,EAAEhK,MAAM,CAAC,gBAAgB,EAAE,CAAC,mBAAmB,CAAC,CAAC;IACvDiK,EAAE,EAAEjK,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;IACzCkK,YAAY,EAAElK,MAAM,CAAC,sBAAsB,EAAE,CAAC,yBAAyB,CAAC,CAAC;IACzEmK,QAAQ,EAAEnK,MAAM,CAAC,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,CAAC;IAC7DoK,QAAQ,EAAEpK,MAAM,CAAC,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,CAAC;IAC7DL,IAAI,EAAEK,MAAM,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IAC/CqK,iBAAiB,EAAErK,MAAM,CAAC,2BAA2B,EAAE,CACrD,8BAA8B,CAC/B,CAAC;IACFsK,IAAI,EAAEtK,MAAM,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IAC/CuK,cAAc,EAAEvK,MAAM,CAAC,yBAAyB,EAAE,CAChD,4BAA4B,CAC7B,CAAC;IACFwK,MAAM,EAAExK,MAAM,CAAC,eAAe,EAAE,CAAC,kBAAkB,CAAC;GACrD;EAED2D,OAAO,EAAE;IACP8G,GAAG,EAAEzK,MAAM,CAAC,IAAI,EAAEiB,gCAAgC,CAAC;IACnDyJ,UAAU,EAAE1K,MAAM,CAAC,qBAAqB,EAAE,CACxC,wBAAwB,EACxB,GAAGiB,gCAAgC,CACpC,CAAC;IACF0J,GAAG,EAAE3K,MAAM,CAAC,aAAa,EAAE,CACzB,gBAAgB,EAChB,oBAAoB,EACpB,GAAGiB,gCAAgC,CACpC,CAAC;IACF2J,IAAI,EAAE5K,MAAM,CAAC,IAAI,EAAEiB,gCAAgC,CAAC;IACpD4J,GAAG,EAAE7K,MAAM,CAAC,aAAa,EAAE,CAAC,oBAAoB,EAAE,GAAGgB,mBAAmB,CAAC,CAAC;IAC1E8J,aAAa,EAAE9K,MAAM,CAAC,wBAAwB,EAAE,CAC9C,2BAA2B,EAC3B,GAAGgB,mBAAmB,CACvB;GACF;EAED8C,OAAO,EAAE;IACPiH,KAAK,EAAE/K,MAAM,CAAC,eAAe,EAAE,CAAC,kBAAkB,CAAC,CAAC;IACpDgL,SAAS,EAAEhL,MAAM,CAAC,mBAAmB,EAAE,CAAC,sBAAsB,CAAC,CAAC;IAChEiL,cAAc,EAAEjL,MAAM,CAAC,yBAAyB,EAAE,CAChD,gCAAgC,CACjC,CAAC;IACFuJ,cAAc,EAAEvJ,MAAM,CAAC,yBAAyB,EAAE,CAChD,4BAA4B,CAC7B,CAAC;IACFkL,cAAc,EAAElL,MAAM,CAAC,yBAAyB,EAAE,CAChD,gCAAgC,CACjC,CAAC;IACFmL,cAAc,EAAEnL,MAAM,CAAC,yBAAyB,EAAE,CAChD,4BAA4B,CAC7B,CAAC;IACFoL,GAAG,EAAEpL,MAAM,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IAC9CqL,WAAW,EAAErL,MAAM,CAAC,sBAAsB,EAAE,CAC1C,6BAA6B,CAC9B,CAAC;IACFsL,eAAe,EAAEtL,MAAM,CAAC,2BAA2B,EAAE,CACnD,kCAAkC,CACnC,CAAC;IACFuL,cAAc,EAAEvL,MAAM,CAAC,0BAA0B,EAAE,CACjD,iCAAiC,CAClC,CAAC;IACFwL,kBAAkB,EAAExL,MAAM,CAAC,+BAA+B,EAAE,CAC1D,sCAAsC,CACvC,CAAC;IACF2J,wBAAwB,EAAE3J,MAAM,CAAC,qCAAqC,EAAE,CACtE,wCAAwC,CACzC,CAAC;IACF+J,cAAc,EAAE/J,MAAM,CAAC,0BAA0B,EAAE,CACjD,6BAA6B,CAC9B,CAAC;IACFyL,GAAG,EAAEzL,MAAM,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IAC9C0L,WAAW,EAAE1L,MAAM,CAAC,sBAAsB,EAAE,CAC1C,6BAA6B,CAC9B,CAAC;IACF2L,cAAc,EAAE3L,MAAM,CAAC,0BAA0B,EAAE,CACjD,iCAAiC,CAClC,CAAC;IACFkK,YAAY,EAAElK,MAAM,CAAC,uBAAuB,EAAE,CAAC,0BAA0B,CAAC,CAAC;IAC3E4L,QAAQ,EAAE5L,MAAM,CAAC,kBAAkB,EAAE,CAAC,yBAAyB,CAAC,CAAC;IACjE6L,OAAO,EAAE7L,MAAM,CAAC,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,CAAC;IAC5DqK,iBAAiB,EAAErK,MAAM,CAAC,4BAA4B,EAAE,CACtD,+BAA+B,CAChC,CAAC;IACF8L,GAAG,EAAE9L,MAAM,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IAC9CuK,cAAc,EAAEvK,MAAM,CAAC,0BAA0B,EAAE,CACjD,6BAA6B,CAC9B;GACF;EAED+D,MAAM,EAAE;IACNe,MAAM,EAAE9E,MAAM,CAAC,eAAe,EAAE,CAAC,sBAAsB,CAAC;GACzD;EAEDR,GAAG,EAAE;IACHuC,IAAI,EAAE/B,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,EAAE,GAAGoB,eAAe,CAAC,CAAC;IAC3Da,EAAE,EAAEjC,MAAM,CAAC,IAAI,EAAE,CAAC,eAAe,EAAE,GAAGoB,eAAe,CAAC;GACvD;EAED2K,MAAM,EAAE;IACNC,MAAM,EAAEhM,MAAM,CAAC,eAAe,EAAE,CAAC,sBAAsB,CAAC,CAAC;IACzDiM,MAAM,EAAEjM,MAAM,CAAC,eAAe,EAAE,CAC9B,sBAAsB,EACtB,2BAA2B,EAC3B,aAAa,CACd,CAAC;IACFkM,aAAa,EAAElM,MAAM,CAAC,wBAAwB,EAAE,CAC9C,2BAA2B,CAC5B,CAAC;IACFmM,GAAG,EAAEnM,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC;GAC5C;EAEDiE,MAAM,EAAE;IACNmI,YAAY,EAAEpM,MAAM,CAAC,sBAAsB,EAAE,CAC3C,6BAA6B,EAC7B,qCAAqC,CACtC,CAAC;IACFqM,aAAa,EAAErM,MAAM,CAAC,uBAAuB,EAAE,CAC7C,0BAA0B,CAC3B,CAAC;IACFsM,OAAO,EAAEtM,MAAM,CAAC,gBAAgB,EAAE,CAChC,uBAAuB,EACvB,yBAAyB,CAC1B,CAAC;IACFuM,GAAG,EAAEvM,MAAM,CAAC,YAAY,EAAE,EAAE,EAAE,WAAW,CAAC;IAC1CwM,WAAW,EAAExM,MAAM,CAAC,qBAAqB,EAAE,CACzC,wBAAwB,EACxB,0BAA0B,CAC3B,CAAC;IACFyM,kBAAkB,EAAEzM,MAAM,CAAC,6BAA6B,EAAE,CACxD,gCAAgC,EAChC,iBAAiB,CAClB,CAAC;IACF0M,YAAY,EAAE1M,MAAM,CAAC,sBAAsB,EAAE,CAC3C,6BAA6B,EAC7B,WAAW,CACZ,CAAC;IACF2M,kBAAkB,EAAE3M,MAAM,CAAC,6BAA6B,EAAE,CACxD,oCAAoC,EACpC,WAAW,CACZ,CAAC;IACF4M,WAAW,EAAE5M,MAAM,CAAC,sBAAsB,EAAE,CAC1C,6BAA6B,EAC7B,WAAW,CACZ,CAAC;IACF6M,iBAAiB,EAAE7M,MAAM,CAAC,6BAA6B,EAAE,CACvD,oCAAoC,EACpC,WAAW,CACZ,CAAC;IACF8M,QAAQ,EAAE9M,MAAM,CAAC,iBAAiB,EAAE,CAClC,oBAAoB,EACpB,GAAGa,sBAAsB,CAC1B,CAAC;IACFkM,MAAM,EAAE/M,MAAM,CAAC,gBAAgB,EAAE,EAAE,EAAE,WAAW,CAAC;IACjDgN,KAAK,EAAEhN,MAAM,CAAC,cAAc,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;IACrEiN,OAAO,EAAEjN,MAAM,CAAC,gBAAgB,EAAE,CAAC,uBAAuB,CAAC,CAAC;IAC5DkN,QAAQ,EAAElN,MAAM,CAAC,kBAAkB,EAAE,CACnC,qBAAqB,EACrB,qBAAqB,CACtB,CAAC;IACF4L,QAAQ,EAAE5L,MAAM,CAAC,iBAAiB,EAAE4B,6BAA6B,CAAC;IAClEuL,WAAW,EAAEnN,MAAM,CAAC,qBAAqB,EAAE,CAAC,4BAA4B,CAAC,CAAC;IAC1EoN,UAAU,EAAEpN,MAAM,CAAC,mBAAmB,EAAE,CAAC,0BAA0B,CAAC,CAAC;IACrEqN,YAAY,EAAErN,MAAM,CAAC,sBAAsB,EAAE,CAC3C,6BAA6B,CAC9B,CAAC;IACFsN,OAAO,EAAEtN,MAAM,CAAC,gBAAgB,EAAE,CAChC,mBAAmB,EACnB,mBAAmB,CACpB,CAAC;IACFuN,MAAM,EAAEvN,MAAM,CAAC,eAAe,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;IACzEwN,OAAO,EAAExN,MAAM,CAAC,gBAAgB,EAAE,CAChC,mBAAmB,EACnB,kBAAkB,CACnB,CAAC;IACFyN,KAAK,EAAEzN,MAAM,CAAC,cAAc,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;IACrE0N,WAAW,EAAE1N,MAAM,CAAC,qBAAqB,EAAE,CACzC,wBAAwB,EACxB,sBAAsB,CACvB,CAAC;IACF2N,WAAW,EAAE3N,MAAM,CAAC,sBAAsB,EAAE,CAC1C,yBAAyB,EACzB,qBAAqB,EACrB,uBAAuB,EACvB,uBAAuB,CACxB,CAAC;IACF4N,WAAW,EAAE5N,MAAM,CAAC,oBAAoB,EAAE,CAAC,uBAAuB,CAAC;GACpE;EAEDqE,GAAG,EAAE;IACHwJ,QAAQ,EAAE7N,MAAM,CAAC,eAAe,EAAE,CAAC,mBAAmB,EAAE,SAAS,CAAC;GACnE;EAEDuE,OAAO,EAAE;IACPxC,IAAI,EAAE/B,MAAM,CAAC,IAAI,EAAE,CAAC,sBAAsB,EAAE,GAAGqB,mBAAmB,CAAC,CAAC;IACpEY,EAAE,EAAEjC,MAAM,CAAC,IAAI,EAAE,CAAC,oBAAoB,EAAE,GAAGqB,mBAAmB,CAAC;GAChE;EAEDmD,OAAO,EAAE;IACPzC,IAAI,EAAE/B,MAAM,CAAC,IAAI,EAAE,CAAC,sBAAsB,EAAE,GAAGsB,mBAAmB,CAAC,CAAC;IACpEW,EAAE,EAAEjC,MAAM,CAAC,IAAI,EAAE,CAAC,oBAAoB,EAAE,GAAGsB,mBAAmB,CAAC;GAChE;EAED0B,SAAS,EAAEnB,uBAAuB,CAAC,2BAA2B,CAAC;EAC/DuB,UAAU,EAAE;IACV0K,UAAU,EAAE9N,MAAM,CAAC,IAAI,EAAE,CACvB,gCAAgC,EAChC,GAAGS,sBAAsB,CAC1B,CAAC;IACFsN,OAAO,EAAE/N,MAAM,CAAC,IAAI,EAAE,CACpB,6BAA6B,EAC7B,GAAGS,sBAAsB,CAC1B,CAAC;IACF,GAAGoB,uBAAuB,CAAC,4BAA4B;GACxD;EACDwB,iBAAiB,EAAExB,uBAAuB,CACxC,oCACF,CAAC;EACDoB,UAAU,EAAEpB,uBAAuB,CAAC,4BAA4B,CAAC;EACjEyB,WAAW,EAAEzB,uBAAuB,CAAC,6BAA6B,CAAC;EACnEqB,UAAU,EAAErB,uBAAuB,CAAC,4BAA4B,CAAC;EACjE0B,WAAW,EAAE1B,uBAAuB,CAAC,6BAA6B,CAAC;EACnEiB,YAAY,EAAEjB,uBAAuB,CAAC,8BAA8B,CAAC;EACrEkB,YAAY,EAAElB,uBAAuB,CAAC,8BAA8B,CAAC;EAErEmM,WAAW,EAAE;IACXC,YAAY,EAAEjO,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;IAC7CoN,SAAS,EAAElO,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;IAC1CqN,YAAY,EAAEnO,MAAM,CAAC,IAAI,EAAEc,iBAAiB;;AAEhD,CAAC;AAEM,MAAMsN,kBAAkB,GAAG;EAChCC,cAAc,EAAErO,MAAM,CAAC,yBAAyB,EAAE,CAChD,wCAAwC,EACxC,GAAGyB,yBAAyB,EAC5B,kCAAkC,EAClC,GAAGE,oBAAoB,CACxB,CAAC;EACF2M,EAAE,EAAEtO,MAAM,CAAC,aAAa,EAAE;;;;;;;EAOxB,kBAAkB,EAClB,0BAA0B,EAC1B,aAAa,CACd,CAAC;EACFuO,MAAM,EAAEvO,MAAM,CAAC,IAAI,EAAE,CAAC,kBAAkB,CAAC,CAAC;EAC1CwO,GAAG,EAAExO,MAAM,CAAC,IAAI,EAAE,CAAC,eAAe,CAAC,CAAC;EACpCyO,IAAI,EAAEzO,MAAM,CAAC,eAAe,EAAE,CAAC,kBAAkB,CAAC,CAAC;EACnD0O,KAAK,EAAE1O,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,CAAC,CAAC;EACxC2O,IAAI,EAAE3O,MAAM,CAAC,IAAI,EAAE,CAAC,gBAAgB,CAAC,CAAC;EACtC4O,WAAW,EAAE5O,MAAM,CAAC,wBAAwB,EAAE,CAAC,yBAAyB,CAAC,CAAC;EAC1E6O,UAAU,EAAE7O,MAAM,CAAC,sBAAsB,EAAE,CAAC,2BAA2B,CAAC,CAAC;EACzE8O,MAAM,EAAE9O,MAAM,CAAC,iBAAiB,EAAE,CAAC,iBAAiB,CAAC,EAAE+O,SAAS,EAAE,CAAC,QAAQ,CAAC,CAAC;EAC7EC,UAAU,EAAEhP,MAAM,CAAC,sBAAsB,EAAE,CAAC,sBAAsB,CAAC,CAAC;EACpEiP,WAAW,EAAEjP,MAAM,CAAC,sBAAsB,EAAE,CAAC,6BAA6B,CAAC,CAAC;EAC5EkP,WAAW,EAAElP,MAAM,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,uBAAuB,CAAC,CAAC;EACjEmP,MAAM,EAAEnP,MAAM,CAAC,IAAI,EAAE,CAAC,mBAAmB,CAAC,CAAC;EAC3CoP,IAAI,EAAEpP,MAAM,CAAC,IAAI,EAAE,CACjB,4BAA4B,EAC5B,GAAGyB,yBAAyB,EAC5B,sBAAsB,EACtB,GAAGE,oBAAoB,CACxB,CAAC;EACF0N,OAAO,EAAErP,MAAM,CAAC,kBAAkB,EAAE,CAClC,oBAAoB,EACpB,yBAAyB,CAC1B,CAAC;EACFsP,QAAQ,EAAEtP,MAAM,CAAC,oBAAoB,EAAE,CAAC,qBAAqB,CAAC,CAAC;EAC/DwJ,OAAO,EAAExJ,MAAM,CAAC,kBAAkB,EAAEY,2BAA2B,CAAC;EAChE2O,KAAK,EAAEvP,MAAM,CAAC,gBAAgB,EAAE,CAC9B,gBAAgB,EAChB,6BAA6B;;;;;;EAM7B,uBAAuB,EACvB,GAAG2B,oBAAoB,CACxB,CAAC;EACF6N,IAAI,EAAExP,MAAM,CAAC,IAAI,EAAE,CAAC,gBAAgB,CAAC,CAAC;EACtCyP,IAAI,EAAEzP,MAAM,CAAC,eAAe,EAAE,CAAC,eAAe,CAAC,CAAC;EAChD0P,MAAM,EAAE1P,MAAM,CAAC,iBAAiB,EAAE,CAChC,iBAAiB,EACjB,8BAA8B,EAC9B,wBAAwB,EACxB,GAAG2B,oBAAoB,CACxB,CAAC;EACFgO,YAAY,EAAE3P,MAAM,CAAC,uBAAuB,EAAE,CAAC,4BAA4B,CAAC,CAAC;EAC7E4P,OAAO,EAAE5P,MAAM,CAAC,IAAI,EAAE,CAAC,oBAAoB,EAAE,GAAGgB,mBAAmB,CAAC,CAAC;EACrE6O,IAAI,EAAE7P,MAAM,CAAC,eAAe,EAAE,CAC5B,eAAe,EACf,4BAA4B,EAC5B,sBAAsB,EACtB,GAAG2B,oBAAoB,CACxB,CAAC;EACFmO,SAAS,EAAE9P,MAAM,CAAC,qBAAqB,EAAE,CAAC,qBAAqB,CAAC,CAAC;EACjE+P,QAAQ,EAAE/P,MAAM,CAAC,oBAAoB,EAAE,CAAC,oBAAoB,CAAC,CAAC;EAC9DgQ,aAAa,EAAEhQ,MAAM,CAAC,0BAA0B,EAAE,CAChD,0BAA0B,CAC3B,CAAC;EACFiQ,KAAK,EAAEjQ,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,CAAC,CAAC;EACxCkQ,KAAK,EAAElQ,MAAM,CAAC,gBAAgB,EAAE,CAAC,iBAAiB,CAAC,CAAC;EACpDmQ,OAAO,EAAEnQ,MAAM,CAAC,mBAAmB,EAAE,CACnC,mBAAmB,EACnB,+BAA+B,EAC/B,gCAAgC,EAChC,0BAA0B,EAC1B,GAAG2B,oBAAoB,CACxB,CAAC;EACFyO,IAAI,EAAEpQ,MAAM,CAAC,eAAe,EAAE,CAAC,eAAe,EAAE,2BAA2B,CAAC,CAAC;EAC7EqQ,UAAU,EAAErQ,MAAM,CAAC,IAAI,EAAE,CACvB,8BAA8B,EAC9B,GAAGkC,oBAAoB,CACxB,CAAC;EACFoO,eAAe,EAAEtQ,MAAM,CAAC,IAAI,EAAE,CAC5B,oCAAoC,EACpC,GAAGkC,oBAAoB,CACxB,CAAC;EACFqO,OAAO,EAAEvQ,MAAM,CAAC,IAAI,EAAE,CAAC,kBAAkB,CAAC,CAAC;EAC3CwQ,KAAK,EAAExQ,MAAM,CAAC,gBAAgB,EAAE,CAAC,oBAAoB,CAAC,CAAC;EACvD0I,OAAO,EAAE1I,MAAM,CAAC,mBAAmB,EAAE,CAAC,uBAAuB,CAAC,CAAC;EAC/DyQ,YAAY,EAAEzQ,MAAM,CAAC,0BAA0B,EAAE,CAC/C,8BAA8B,EAC9B,QAAQ,EACR,qBAAqB,CACtB,CAAC;EACF0Q,UAAU,EAAE1Q,MAAM,CAAC,uBAAuB,EAAE,CAC1C,2BAA2B,EAC3B,QAAQ,EACR,qBAAqB,CACtB,CAAC;EACF2Q,SAAS,EAAE3Q,MAAM,CAAC,IAAI,EAAE,CAAC,qBAAqB,CAAC,CAAC;EAChD4Q,QAAQ,EAAE5Q,MAAM,CAAC,IAAI,EAAE,CAAC,oBAAoB,CAAC,CAAC;EAC9CH,OAAO,EAAEG,MAAM,CAAC,mBAAmB,EAAE,CACnC,mBAAmB,EACnB,gCAAgC,EAChC,0BAA0B,EAC1B,GAAG2B,oBAAoB,EACvB,8BAA8B,CAC/B,CAAC;EACFkP,QAAQ,EAAE7Q,MAAM,CAAC,mBAAmB,EAAE,CACpC,mBAAmB,EACnB,oBAAoB,CACrB,CAAC;EACF8Q,OAAO,EAAE9Q,MAAM,CAAC,IAAI,EAAE,CACpB,+BAA+B,EAC/B,GAAGyB,yBAAyB,EAC5B,yBAAyB,EACzB,GAAGE,oBAAoB,CACxB,CAAC;EACFoP,OAAO,EAAE/Q,MAAM,CAAC,mBAAmB,EAAE,CAAC,mBAAmB,CAAC,CAAC;EAC3DgR,YAAY,EAAEhR,MAAM,CAAC,yBAAyB,EAAE,CAAC,0BAA0B,CAAC,CAAC;EAC7EiR,MAAM,EAAEjR,MAAM,CAAC,IAAI,EAAE,CAAC,mBAAmB,CAAC,CAAC;EAC3CkR,IAAI,EAAElR,MAAM,CAAC,IAAI,EAAE,CAAC,eAAe,CAAC,CAAC;EACrCL,IAAI,EAAEK,MAAM,CAAC,eAAe,EAAEY,2BAA2B,CAAC;EAC1DuQ,SAAS,EAAEnR,MAAM,CAAC,IAAI,EAAE,CAAC,yBAAyB,CAAC,CAAC;EACpDoR,WAAW,EAAEpR,MAAM,CAAC,wBAAwB,EAAE,CAAC,wBAAwB,CAAC,CAAC;EACzEqR,QAAQ,EAAErR,MAAM,CAAC,IAAI,EAAE,CAAC,wBAAwB,CAAC,CAAC;EAClDsR,IAAI,EAAEtR,MAAM,CAAC,IAAI,EAAE,CAAC,gBAAgB,CAAC,CAAC;EACtCuR,GAAG,EAAEvR,MAAM,CAAC,cAAc,EAAE,CAC1B,cAAc,EACd,2BAA2B,EAC3B,qBAAqB,CACtB,CAAC;EACFgN,KAAK,EAAEhN,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;EAC1DkN,QAAQ,EAAElN,MAAM,CAAC,oBAAoB,EAAE,CACrC,qBAAqB,EACrB,gBAAgB,CACjB,CAAC;EACFF,IAAI,EAAEE,MAAM,CAAC,IAAI,EAAE,CAAC,kBAAkB,CAAC,CAAC;EACxCwR,MAAM,EAAExR,MAAM,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,CAAC,CAAC;EACzDyR,QAAQ,EAAEzR,MAAM,CAAC,oBAAoB,EAAE,CAAC,qBAAqB,CAAC,CAAC;EAC/D0R,IAAI,EAAE1R,MAAM,CAAC,eAAe,EAAE,CAAC,eAAe,CAAC,CAAC;EAChD2R,MAAM,EAAE3R,MAAM,CAAC,iBAAiB,EAAE,CAChC,iBAAiB,EACjB,8BAA8B,EAC9B,wBAAwB,EACxB,GAAG2B,oBAAoB,CACxB,CAAC;EACFiQ,WAAW,EAAE5R,MAAM,CAAC,uBAAuB,EAAE,CAAC,uBAAuB,CAAC,CAAC;EACvE6R,MAAM,EAAE7R,MAAM,CAAC,iBAAiB,EAAE,CAAC,kBAAkB,CAAC,CAAC;EACvDsN,OAAO,EAAEtN,MAAM,CAAC,IAAI,EAAE,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;EAC9D8R,UAAU,EAAE9R,MAAM,CAAC,sBAAsB,EAAE,CACzC,uBAAuB,EACvB,mBAAmB,EACnB,gBAAgB,CACjB,CAAC;EACF+R,OAAO,EAAE/R,MAAM,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,CAAC,CAAC;EACzDuN,MAAM,EAAEvN,MAAM,CAAC,IAAI,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;EAC5DgS,UAAU,EAAEhS,MAAM,CAAC,IAAI,EAAE,CACvB,8BAA8B,EAC9B,GAAGkC,oBAAoB,CACxB,CAAC;EACF+P,eAAe,EAAEjS,MAAM,CAAC,IAAI,EAAE,CAC5B,oCAAoC,EACpC,GAAGkC,oBAAoB,CACxB,CAAC;EACFgQ,OAAO,EAAElS,MAAM,CAAC,IAAI,EAAE,CAAC,kBAAkB,CAAC,CAAC;EAC3CmS,KAAK,EAAEnS,MAAM,CAAC,gBAAgB,EAAE,CAAC,gBAAgB,CAAC,CAAC;EACnDoS,KAAK,EAAEpS,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,CAAC,CAAC;EACxCqS,IAAI,EAAErS,MAAM,CAAC,eAAe,EAAE,CAC5B,eAAe,EACf,4BAA4B,EAC5B,sBAAsB,EACtB,GAAG2B,oBAAoB,CACxB,CAAC;EACFvB,IAAI,EAAEJ,MAAM,CAAC,eAAe,EAAE,CAAC,eAAe,CAAC,CAAC;EAChDsS,MAAM,EAAEtS,MAAM,CAAC,iBAAiB,EAAE,CAAC,iBAAiB,CAAC,CAAC;EACtDyN,KAAK,EAAEzN,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;EAC1DuS,UAAU,EAAEvS,MAAM,CAAC,sBAAsB,EAAE,CAAC,uBAAuB,CAAC,CAAC;EACrEwS,MAAM,EAAExS,MAAM,CAAC,IAAI,EAAE,CAAC,kBAAkB,CAAC,CAAC;EAC1CyS,MAAM,EAAEzS,MAAM,CAAC,IAAI,EAAE,CAAC,kBAAkB,CAAC,CAAC;EAC1C0S,GAAG,EAAE1S,MAAM,CAAC,IAAI,EAAE,CAAC,eAAe,CAAC,CAAC;EACpC2S,MAAM,EAAE3S,MAAM,CAAC,IAAI,EAAE,CAAC,kBAAkB,CAAC,CAAC;EAC1C4S,GAAG,EAAE5S,MAAM,CAAC,IAAI,EAAE,CAAC,eAAe,CAAC,CAAC;EACpC6S,IAAI,EAAE7S,MAAM,CAAC,IAAI,EAAE,CACjB,4BAA4B,EAC5B,GAAGyB,yBAAyB,EAC5B,sBAAsB,EACtB,GAAGE,oBAAoB,CACxB,CAAC;EACFmR,IAAI,EAAE9S,MAAM,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;EACxD+S,OAAO,EAAE/S,MAAM,CAAC,IAAI,EAAE,CACpB,gCAAgC,EAChC,GAAGyB,yBAAyB,EAC5B,0BAA0B,EAC1B,GAAGE,oBAAoB,CACxB,CAAC;EACFqR,OAAO,EAAEhT,MAAM,CAAC,IAAI,EAAE,CACpB,0BAA0B,EAC1B,GAAG2B,oBAAoB,EACvB,GAAGF,yBAAyB,EAC5B,GAAGC,2BAA2B,CAC/B,CAAC;EACFuR,aAAa,EAAEjT,MAAM,CAAC,IAAI,EAAE,CAAC,0BAA0B,CAAC,CAAC;EACzDkT,OAAO,EAAElT,MAAM,CAAC,IAAI,EAAE,CAAC,oBAAoB,CAAC,CAAC;EAC7CmT,WAAW,EAAEnT,MAAM,CAAC,IAAI,EAAE,CAAC,uBAAuB,CAAC,CAAC;EACpDoT,WAAW,EAAEpT,MAAM,CAAC,IAAI,EAAE,CAAC,uBAAuB,CAAC,CAAC;EACpDqT,MAAM,EAAErT,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,CAAC,CAAC;EACzCsT,WAAW,EAAEtT,MAAM,CAAC,IAAI,EAAE,CAAC,wBAAwB,CAAC,CAAC;EACrDuT,UAAU,EAAEvT,MAAM,CAAC,sBAAsB,EAAE,CAAC,sBAAsB,CAAC,CAAC;EACpEwT,QAAQ,EAAExT,MAAM,CAAC,oBAAoB,EAAE,CACrC,oBAAoB,EACpB,eAAe,CAChB,CAAC;EACFyT,SAAS,EAAEzT,MAAM,CAAC,qBAAqB,EAAE,CAAC,qBAAqB,CAAC,CAAC;EACjE0T,QAAQ,EAAE1T,MAAM,CAAC,IAAI,EAAE,CACrB,qBAAqB,EACrB,oBAAoB,EACpB,mBAAmB,EACnB,qBAAqB,CACtB,CAAC;EACF2T,YAAY,EAAE3T,MAAM,CAAC,yBAAyB,EAAE,CAAC,0BAA0B,CAAC,CAAC;EAC7E4T,IAAI,EAAE5T,MAAM,CAAC,eAAe,EAAE,CAAC,gBAAgB,CAAC,CAAC;EACjD6T,OAAO,EAAE7T,MAAM,CAAC,mBAAmB,EAAE,CAAC,oBAAoB,CAAC,CAAC;EAC5D8T,QAAQ,EAAE9T,MAAM,CAAC,oBAAoB,EAAE,CAAC,sBAAsB,CAAC,CAAC;EAChE+T,SAAS,EAAE/T,MAAM,CAAC,qBAAqB,EAAE,CAAC,oBAAoB,CAAC,CAAC;EAChEgU,SAAS,EAAEhU,MAAM,CAAC,qBAAqB,EAAE,CAAC,sBAAsB,CAAC,CAAC;EAClEiU,QAAQ,EAAEjU,MAAM,CAAC,oBAAoB,EAAE,CAAC,wBAAwB,EAAE,QAAQ,CAAC,CAAC;EAC5EkU,OAAO,EAAElU,MAAM,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,CAAC,CAAC;EACzDmU,MAAM,EAAEnU,MAAM,CAAC,kBAAkB,EAAE,CAAC,yBAAyB,CAAC,CAAC;EAC/DwK,MAAM,EAAExK,MAAM,CAAC,iBAAiB,EAAEY,2BAA2B,CAAC;EAC9DwT,IAAI,EAAEpU,MAAM,CAAC,eAAe,EAAE,CAAC,eAAe,CAAC,CAAC;EAChDqU,gBAAgB,EAAErU,MAAM,CAAC,IAAI,EAAE,CAAC,yBAAyB,CAAC,CAAC;EAC3DsU,gBAAgB,EAAEtU,MAAM,CAAC,IAAI,EAAE,CAAC,yBAAyB,CAAC,CAAC;EAC3DuU,gBAAgB,EAAEvU,MAAM,CAAC,IAAI,EAAE,CAAC,yBAAyB,CAAC,CAAC;EAC3DwU,gBAAgB,EAAExU,MAAM,CAAC,IAAI,EAAE,CAAC,yBAAyB,CAAC,CAAC;EAC3D,CAAC,WAAW,GAAGA,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,CAAC;AACjD,CAAC;;AC79BD;AACA;AACA;;AAEO,MAAMyU,MAAM,GAAG,IAAIjV,GAAG,CAAC,CAC5B,OAAO,EACP,YAAY,EACZ,gBAAgB,EAChB,UAAU,EACV,iBAAiB,EACjB,UAAU,EACV,eAAe,EACf,wBAAwB,EACxB,iBAAiB,EACjB,sBAAsB,EACtB,oBAAoB,EACpB,kBAAkB,EAClB,gBAAgB,EAChB,eAAe,EACf,iBAAiB,EACjB,eAAe,EACf,qBAAqB,EACrB,gBAAgB,EAChB,eAAe,EACf,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,eAAe,EACf,wBAAwB,EACxB,cAAc,EACd,kBAAkB,EAClB,oBAAoB,EACpB,iBAAiB,EACjB,uBAAuB,EACvB,iBAAiB,EACjB,kBAAkB,EAClB,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,iBAAiB,EACjB,sBAAsB,EACtB,eAAe,EACf,mBAAmB,EACnB,oBAAoB,EACpB,qBAAqB,EACrB,qBAAqB,EACrB,iBAAiB,EACjB,gBAAgB,EAChB,KAAK,EACL,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,YAAY,EACZ,gBAAgB,EAChB,kBAAkB,EAClB,mBAAmB,EACnB,eAAe,EACf,wBAAwB,EACxB,yBAAyB,EACzB,yBAAyB,EACzB,oBAAoB,EACpB,kBAAkB,EAClB,eAAe,EACf,eAAe,EACf,0BAA0B,EAC1B,wBAAwB,EACxB,gBAAgB,EAChB,eAAe,EACf,qBAAqB,EACrB,oCAAoC,EACpC,qCAAqC,EACrC,+BAA+B,EAC/B,iCAAiC,EACjC,yBAAyB,EACzB,WAAW,EACX,sBAAsB,EACtB,kBAAkB,EAClB,kBAAkB,EAClB,aAAa,EACb,2BAA2B,EAC3B,aAAa,EACb,yBAAyB,EACzB,eAAe,EACf,aAAa,EACb,WAAW,EACX,SAAS,EACT,iBAAiB,EACjB,eAAe,EACf,mBAAmB,EACnB,yBAAyB,EACzB,yBAAyB,EACzB,aAAa,EACb,qCAAqC,EACrC,0BAA0B,EAC1B,aAAa,EACb,uBAAuB,EACvB,kBAAkB,EAClB,4BAA4B,EAC5B,aAAa,EACb,0BAA0B,EAC1B,KAAK,EACL,eAAe,EACf,cAAc,EACd,aAAa,EACb,wBAAwB,EACxB,YAAY,EACZ,QAAQ,EACR,uBAAuB,EACvB,YAAY,EACZ,qBAAqB,EACrB,6BAA6B,EAC7B,iBAAiB,EACjB,gBAAgB,EAChB,cAAc,EACd,gBAAgB,EAChB,eAAe,EACf,gBAAgB,EAChB,cAAc,EACd,qBAAqB,EACrB,sBAAsB,EACtB,oBAAoB,EACpB,KAAK,EACL,mBAAmB,EACnB,UAAU,EACV,UAAU,CACX,CAAC;AAEK,MAAMkV,SAAS,GAAG,IAAIlV,GAAG,CAAC,CAC/B,GAAGiV,MAAM,EACT,iBAAiB,EACjB,eAAe,EACf,kBAAkB,EAClB,aAAa,EACb,aAAa,EACb,sBAAsB,EACtB,oBAAoB,EACpB,sBAAsB,EACtB,YAAY,EACZ,cAAc,EACd,kBAAkB,EAClB,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,kBAAkB,EAClB,cAAc,EACd,YAAY,EACZ,kBAAkB,EAClB,cAAc,EACd,YAAY,EACZ,oBAAoB,EACpB,YAAY,EACZ,yBAAyB,EACzB,yBAAyB,EACzB,sBAAsB,EACtB,2BAA2B,EAC3B,0BAA0B,EAC1B,+BAA+B,EAC/B,sBAAsB,EACtB,0BAA0B,EAC1B,kBAAkB,EAClB,gBAAgB,EAChB,mBAAmB,EACnB,sBAAsB,CACvB,CAAC;;;ECjLOE,KAAK,EAAIC;AAAC,IAAAC,MAAA,CAAAC,OAAA,IAAAD,MAAA;AAEJ,SAASE,eAAeA,CACrCC,IAA8B,EAC9BC,IAAc,EACd;EACA,MAAM;IAAEC,IAAI;IAAEC;GAAQ,GAAGF,IAAI;EAC7B,QAAQD,IAAI,CAAClV,IAAI;IACf,KAAK,iBAAiB;MAAE;QACtB,IAAI,CAAC8U,GAAC,CAACQ,gBAAgB,CAACD,MAAM,EAAE;UAAEE,MAAM,EAAEH;SAAM,CAAC,EAAE,OAAO,KAAK;QAC/D,IAAIC,MAAM,CAACG,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI;QAC5C,MAAMC,QAAQ,GAAGL,MAAM,CAACG,SAAS,CAAC,CAAC,CAAC;QACpC,OAAOV,GAAC,CAACa,eAAe,CAACD,QAAQ,CAAC,IAAIZ,GAAC,CAACc,iBAAiB,CAACF,QAAQ,CAAC;;;AAGzE;;;EChBSb,KAAK,EAAIC;AAAC,IAAAC,MAAA,CAAAC,OAAA,IAAAD,MAAA;AAGZ,MAAMc,aAAa,GAAG,wBAAwB;AAE9C,SAASC,UAAUA,CAACX,IAAS,EAAEY,EAAgB,EAAE;EACtD,MAAM;IAAEC;GAAQ,GAAGb,IAAI,CAACC,IAAI;EAE5B,IAAIa,QAAQ,EAAEC,QAAQ;EACtB,IAAIpB,GAAC,CAACqB,YAAY,CAACH,MAAM,CAAC,EAAE;IAC1BC,QAAQ,GAAGD,MAAM;IACjBE,QAAQ,GAAGpB,GAAC,CAACsB,SAAS,CAACJ,MAAM,CAAC;GAC/B,MAAM;IACLC,QAAQ,GAAGd,IAAI,CAACkB,KAAK,CAACC,6BAA6B,CAAC,SAAS,CAAC;IAC9DJ,QAAQ,GAAGpB,GAAC,CAACyB,oBAAoB,CAAC,GAAG,EAAEzB,GAAC,CAACsB,SAAS,CAACH,QAAQ,CAAC,EAAED,MAAM,CAAC;;EAGvEb,IAAI,CAACqB,WAAW,CACd1B,GAAC,CAAC2B,gBAAgB,CAAC3B,GAAC,CAAC4B,cAAc,CAACX,EAAE,EAAE,CAACG,QAAQ,CAAC,CAAC,EAAEpB,GAAC,CAAC6B,UAAU,CAAC,MAAM,CAAC,CAC3E,CAAC;EAEDxB,IAAI,CAACyB,UAAU,CAACC,gBAAgB,CAAC,WAAW,EAAEZ,QAAQ,CAAC;AACzD;AAEO,SAASa,cAAcA,CAACC,MAAc,EAAE;EAC7C,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9BA,MAAM,GAAGA,MAAM,CACZvJ,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CACnBA,OAAO,CAAC,wBAAwB,EAAE,EAAE,CAAC,CACrCwJ,WAAW,EAAE;;EAGlB,OACEpX,MAAM,CAACqX,SAAS,CAACC,cAAc,CAACC,IAAI,CAACC,aAAa,EAAEL,MAAM,CAAC,IAC3DK,aAAa,CAACL,MAAM,CAAC;AAEzB;AAEO,SAASM,YAAYA,CAACrX,IAAY,EAAE;EACzC,OAAQ,mBAAkBA,IAAK,KAAI;AACrC;AAEO,SAASsX,gBAAgBA,CAC9BtX,IAAY,EACZuX,eAAwB,EACxBC,GAAW,EACX;EACA,OAAOD,eAAe,GACjB,GAAE1B,aAAc,YAAW7V,IAAK,GAAEwX,GAAI,EAAC,GACvC,yBAAwBxX,IAAK,KAAI;AACxC;;ACpC8C;EAGrC6U,KAAK,EAAIC;AAAC,IAAAC,MAAA,CAAAC,OAAA,IAAAD,MAAA;AAWnB,MAAM0C,eAAe,GAAG,iDAAiD;AACzE,MAAMC,aAAa,GAAG,8CAA8C;AAapE,MAAMC,aAAa,GAAG,CACpB,OAAO,EACP,QAAQ,EAER,UAAU,EACV,gBAAgB,EAChB,iBAAiB,CAClB,CAAClG,GAAG,CAACmG,CAAC,IAAI,IAAI3T,MAAM,CAAE,YAAW2T,CAAE,OAAM,CAAC,CAAC;AAE5C,MAAMC,cAAc,GAAGA,CACrB7X,IAAY,EACZ8X,EAA6B,KACjB;EACZ,IAAIA,EAAE,CAAC9X,IAAI,CAAC,EAAE,OAAO,IAAI;EACzB,IAAI,CAACA,IAAI,CAACyS,UAAU,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;EACzC,MAAMsF,QAAQ,GAAI,UAAS/X,IAAI,CAACqS,KAAK,CAAC,CAAC,CAAE,EAAC;EAC1C,IAAI,CAACvS,gBAAgB,CAACiY,QAAQ,CAAC,EAAE,OAAO,KAAK;EAC7C,OAAOD,EAAE,CAACC,QAAQ,CAAC;AACrB,CAAC;AAED,YAAeC,cAAc,CAAU,UACrC;EAAEC,QAAQ;EAAEC,MAAM;EAAEC,oBAAoB;EAAEC,kBAAkB;EAAEC,KAAK;EAAEC;AAAM,CAAC,EAC5E;EACEC,OAAO,GAAG,CAAC;aACX3D,WAAS;EACT4D,gBAAgB;EAChB,CAACf,eAAe,GAAG;IAAEgB,aAAa,GAAG;GAAO,GAAG,EAAE;EACjD,CAACf,aAAa,GAAG;IAAEH,eAAe,GAAG,KAAK;IAAEC,GAAG,GAAG;GAAO,GAAG;AAC9D,CAAC,EACD;EACA,MAAMkB,SAAS,GAAGJ,KAAK,CAACK,MAAM,CAACA,MAAM,IAAI,CAAAA,MAAM,oBAANA,MAAM,CAAE3Y,IAAI,MAAK,cAAc,CAAC;EAEzE,MAAM4Y,OAAO,GAAGR,kBAAkB,CAAC;IACjChY,MAAM,EAAEiC,QAAQ;IAChBwW,MAAM,EAAEjT,gBAAgB;IACxBkT,QAAQ,EAAExK;GACX,CAAC;EAEF,MAAMyK,SAAS,GAAG,IAAIrZ,GAAG,CAACsZ,8BAA8B,CAACT,OAAO,CAAC,CAAC;EAElE,SAASU,iBAAiBA,CAACC,eAAe,EAAE;IAC1C,OAAO3B,eAAe,GAClB2B,eAAe,GACZ,GAAErD,aAAc,UAAS,GACzB,GAAEA,aAAc,iBAAgB,GACnCqD,eAAe,GACb,uBAAuB,GACvB,qBAAqB;;EAG7B,SAASC,qBAAqBA,CAACnZ,IAAY,EAAEoZ,KAAK,EAAE;IAClD,IAAIjB,oBAAoB,CAACnY,IAAI,CAAC,EAAE;MAC9BqY,KAAK,CAACrY,IAAI,CAAC;MACXoZ,KAAK,CAACC,kBAAkB,CAAChC,YAAY,CAACrX,IAAI,CAAC,EAAEA,IAAI,CAAC;MAClD,OAAO,IAAI;;IAEb,OAAO,KAAK;;EAGd,SAASsZ,iBAAiBA,CAACC,KAAe,EAAEH,KAAK,EAAErB,QAAQ,GAAG,IAAI,EAAE;IAClE,KAAK,MAAM/X,IAAI,IAAIuZ,KAAK,EAAE;MACxB,IAAIxB,QAAQ,EAAE;QACZF,cAAc,CAAC7X,IAAI,EAAEA,IAAI,IAAImZ,qBAAqB,CAACnZ,IAAI,EAAEoZ,KAAK,CAAC,CAAC;OACjE,MAAM;QACLD,qBAAqB,CAACnZ,IAAI,EAAEoZ,KAAK,CAAC;;;;EAKxC,SAASI,eAAeA,CACtBtE,IAA8B,EAC9BuE,IAAI,EACJL,KAAK,EACLpD,MAAO,EACP;IACA,IACEd,IAAI,CAAC/U,IAAI,IACT,EAAE6V,MAAM,IAAId,IAAI,CAAC7U,OAAO,IAAI6U,IAAI,CAAC7U,OAAO,CAAC0Q,QAAQ,CAACiF,MAAM,CAAC,CAAC,IAC1D6B,cAAc,CAAC3C,IAAI,CAAClV,IAAI,EAAEmY,oBAAoB,CAAC,EAC/C;MACA,MAAM;QAAEnY;OAAM,GAAGkV,IAAI;MACrB,IAAIgE,eAAe,GAAG,KAAK;MAC3B,IAAItE,WAAS,IAAK4D,gBAAgB,IAAIxY,IAAI,CAACyS,UAAU,CAAC,SAAS,CAAE,EAAE;QACjEyG,eAAe,GAAG,IAAI;OACvB,MAAM,IAAIlZ,IAAI,CAACyS,UAAU,CAAC,KAAK,CAAC,IAAI,CAACsG,SAAS,CAACpN,GAAG,CAAC3L,IAAI,CAAC,EAAE;QACzDkZ,eAAe,GAAG,IAAI;;MAExB,IACE3B,eAAe,IACf,CAAC,CACC2B,eAAe,GACXQ,SAA2B,GAC3BA,MAAwB,EAC5B/N,GAAG,CAACuJ,IAAI,CAAC/U,IAAI,CAAC,EAChB;QACA;;MAEF,MAAMwZ,cAAc,GAAGV,iBAAiB,CAACC,eAAe,CAAC;MACzD,OAAOE,KAAK,CAACQ,mBAAmB,CAC7B,GAAED,cAAe,IAAGzE,IAAI,CAAC/U,IAAK,GAAEqX,GAAI,EAAC,EACtCiC,IACF,CAAC;;;EAIL,SAASI,eAAeA,CAAC7Z,IAAI,EAAE;IAC7B,IAAIA,IAAI,CAACyS,UAAU,CAAC,SAAS,CAAC,EAAE;MAC9B,MAAMqH,MAAM,GAAI,MAAK9Z,IAAI,CAACqS,KAAK,CAAC,CAAC,CAAE,EAAC;;;MAGpC,OAAOyH,MAAM,IAAIha,gBAAgB;;IAEnC,OAAO,IAAI;;EAGb,OAAO;IACLE,IAAI,EAAE,SAAS;IAEf+Z,WAAW,EAAEtB,aAAa,GAAG,IAAI,GAAG5C,aAAa;IAEjDmE,SAAS,EAAEla,gBAAgB;IAE3Bma,eAAeA,CAACja,IAAI,EAAE;MACpB,IAAI,CAAC+Y,SAAS,CAACpN,GAAG,CAAC3L,IAAI,CAAC,EAAE,OAAO,KAAK;MACtC,IAAI4U,WAAS,IAAIsD,MAAM,KAAK,cAAc,EAAE,OAAO,IAAI;MACvD,IAAIM,gBAAgB,IAAI0B,2BAA2B,CAACvO,GAAG,CAAC3L,IAAI,CAAC,EAAE;QAC7D,OAAO,IAAI;;MAEb,OAAO6Z,eAAe,CAAC7Z,IAAI,CAAC;KAC7B;IAEDma,WAAWA,CAACC,IAAI,EAAEhB,KAAK,EAAEjE,IAAI,EAAE;MAC7B,IAAIiF,IAAI,CAACC,IAAI,KAAK,QAAQ,EAAE;MAE5B,MAAM3Z,OAAO,GAAGoW,cAAc,CAACsD,IAAI,CAACrD,MAAM,CAAC;MAC3C,IAAI,CAACrW,OAAO,EAAE;MAEd,IACEA,OAAO,CAAC+U,MAAM,KAAK,CAAC,IACpB2E,IAAI,CAACrD,MAAM,KAAKM,YAAY,CAAC3W,OAAO,CAAC,CAAC,CAAC,CAAC,IACxCyX,oBAAoB,CAACzX,OAAO,CAAC,CAAC,CAAC,CAAC,EAChC;;;QAGA2X,KAAK,CAAC,IAAI,CAAC;QACX;;MAGF,MAAMiC,UAAU,GAAG,IAAI5a,GAAG,CAACgB,OAAO,CAAC;MACnC,MAAM6Z,eAAe,GAAG7Z,OAAO,CAACkP,MAAM,CAAC4K,MAAM,IAAI;QAC/C,IAAI,CAACA,MAAM,CAAC/H,UAAU,CAAC,SAAS,CAAC,EAAE,OAAO,IAAI;QAC9C,MAAMkC,MAAM,GAAG6F,MAAM,CAAChN,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC;QAC/C,IAAI8M,UAAU,CAAC3O,GAAG,CAACgJ,MAAM,CAAC,IAAIwD,oBAAoB,CAACxD,MAAM,CAAC,EAAE;UAC1D,OAAO,KAAK;;QAEd,OAAO,IAAI;OACZ,CAAC;MAEF2E,iBAAiB,CAACiB,eAAe,EAAEnB,KAAK,EAAE,KAAK,CAAC;MAChDjE,IAAI,CAACsF,MAAM,EAAE;KACd;IAEDC,WAAWA,CAACN,IAAI,EAAEhB,KAAK,EAAEjE,IAAI,EAAE;MAC7B,MAAMwF,QAAQ,GAAG/B,OAAO,CAACwB,IAAI,CAAC;MAC9B,IAAI,CAACO,QAAQ,EAAE;MAEf,IAAI1F,eAAe,CAAC0F,QAAQ,CAACzF,IAAI,EAAEC,IAAI,CAAC,EAAE;MAE1C,IAAIyF,IAAI,GAAGD,QAAQ,CAACzF,IAAI,CAAC9U,MAAM;MAE/B,IACEua,QAAQ,CAACN,IAAI,KAAK,QAAQ,IAC1B,QAAQ,IAAID,IAAI,IAChBA,IAAI,CAACpE,MAAM,IACXoE,IAAI,CAACS,SAAS,KAAK,WAAW,EAC9B;QACA,MAAMC,GAAG,GAAGV,IAAI,CAACpE,MAAM,CAACgB,WAAW,EAAE;QACrC4D,IAAI,GAAGA,IAAI,CAAChL,MAAM,CAACmL,CAAC,IAClBpD,aAAa,CAACpF,IAAI,CAACqF,CAAC,IAAIA,CAAC,CAAC5E,IAAI,CAAC+H,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAChK,QAAQ,CAAC+J,GAAG,CAAC,GAAG,IACzD,CAAC;;MAGHxB,iBAAiB,CAACsB,IAAI,EAAExB,KAAK,CAAC;MAE9B,OAAO,IAAI;KACZ;IAED4B,SAASA,CAACZ,IAAI,EAAEhB,KAAK,EAAEjE,IAAI,EAAE;MAC3B,IAAIiF,IAAI,CAACC,IAAI,KAAK,IAAI,EAAE;QACtB,IAAID,IAAI,CAACa,GAAG,KAAK,iBAAiB,EAAE;UAClC9F,IAAI,CAACqB,WAAW,CACd1B,CAAC,CAAC4B,cAAc,CACd0C,KAAK,CAACQ,mBAAmB,CACvBtC,gBAAgB,CAAC,aAAa,EAAEC,eAAe,EAAEC,GAAG,CAAC,EACrD,YACF,CAAC,EACD,CAAErC,IAAI,CAACC,IAAI,CAAwB8F,KAAK,CAAC;WAE7C,CAAC;;;QAEH;;MAGF,IAAI/F,IAAI,CAACyB,UAAU,CAACuE,iBAAiB,CAAC;QAAEC,QAAQ,EAAE;OAAU,CAAC,EAAE;MAE/D,IAAIhB,IAAI,CAACC,IAAI,KAAK,UAAU,EAAE;;QAE5B,IAAI,CAAClF,IAAI,CAACkG,kBAAkB,EAAE,EAAE;QAChC,IAAI,CAAClG,IAAI,CAACmG,YAAY,EAAE,EAAE;QAC1B,IAAInG,IAAI,CAACyB,UAAU,CAAC2E,kBAAkB,EAAE,EAAE;QAC1C,IAAIzG,CAAC,CAAC0G,OAAO,CAACrG,IAAI,CAACC,IAAI,CAACY,MAAM,CAAC,EAAE;UAC/B;;QAGF,IAAIoE,IAAI,CAACa,GAAG,KAAK,iBAAiB,EAAE;UAClC,IAAI,CAAC9C,oBAAoB,CAAC,oBAAoB,CAAC,EAAE;UAEjD,MAAM;YAAE9C,MAAM;YAAED;WAAM,GAAGD,IAAI;UAC7B,IAAIL,CAAC,CAACQ,gBAAgB,CAACD,MAAM,EAAE;YAAEE,MAAM,EAAEH;WAAM,CAAC,EAAE;YAChD,IAAIC,MAAM,CAACG,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;cACjCN,IAAI,CAACyB,UAAU,CAACJ,WAAW,CACzB1B,CAAC,CAAC4B,cAAc,CACd0C,KAAK,CAACQ,mBAAmB,CACvBtC,gBAAgB,CAAC,cAAc,EAAEC,eAAe,EAAEC,GAAG,CAAC,EACtD,aACF,CAAC,EACD,CAACpC,IAAI,CAACY,MAAM,CACd,CACF,CAAC;cACDb,IAAI,CAACsG,IAAI,EAAE;aACZ,MAAM;cACL3F,UAAU,CACRX,IAAI,EACJiE,KAAK,CAACQ,mBAAmB,CACvBtC,gBAAgB,CAAC,qBAAqB,EAAEC,eAAe,EAAEC,GAAG,CAAC,EAC7D,mBACF,CACF,CAAC;;WAEJ,MAAM;YACLrC,IAAI,CAACqB,WAAW,CACd1B,CAAC,CAAC4B,cAAc,CACd0C,KAAK,CAACQ,mBAAmB,CACvBtC,gBAAgB,CAAC,qBAAqB,EAAEC,eAAe,EAAEC,GAAG,CAAC,EAC7D,mBACF,CAAC,EACD,CAACrC,IAAI,CAACC,IAAI,CAACY,MAAM,CACnB,CACF,CAAC;;UAGH;;;MAIJ,IAAI2E,QAAQ,GAAG/B,OAAO,CAACwB,IAAI,CAAC;MAC5B,IAAI,CAACO,QAAQ,EAAE;MAEf,IAAI1F,eAAe,CAAC0F,QAAQ,CAACzF,IAAI,EAAEC,IAAI,CAAC,EAAE;MAE1C,IACEoC,eAAe,IACfoD,QAAQ,CAACzF,IAAI,CAAC/U,IAAI,IAClBwa,QAAQ,CAACzF,IAAI,CAAC/U,IAAI,CAACkS,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EACzC;;QAEAsI,QAAQ,GAAG;UACT,GAAGA,QAAQ;UACXzF,IAAI,EAAE;YACJ,GAAGyF,QAAQ,CAACzF,IAAI;YAChB/U,IAAI,EAAEwa,QAAQ,CAACzF,IAAI,CAAC/U,IAAI,CAACkS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;SAEvC;;MAGH,IAAIsI,QAAQ,CAACN,IAAI,KAAK,QAAQ,EAAE;QAC9B,MAAMtE,EAAE,GAAGyD,eAAe,CAACmB,QAAQ,CAACzF,IAAI,EAAEyF,QAAQ,CAAC3a,IAAI,EAAEoZ,KAAK,CAAC;QAC/D,IAAIrD,EAAE,EAAEZ,IAAI,CAACqB,WAAW,CAACT,EAAE,CAAC;OAC7B,MAAM,IAAI4E,QAAQ,CAACN,IAAI,KAAK,QAAQ,EAAE;QACrC,MAAMtE,EAAE,GAAGyD,eAAe,CACxBmB,QAAQ,CAACzF,IAAI,EACbyF,QAAQ,CAAC3a,IAAI,EACboZ,KAAK;;QAELgB,IAAI,CAACpE,MACP,CAAC;QACD,IAAID,EAAE,EAAEZ,IAAI,CAACqB,WAAW,CAACT,EAAE,CAAC;OAC7B,MAAM,IAAI4E,QAAQ,CAACN,IAAI,KAAK,UAAU,EAAE;QACvC,MAAMtE,EAAE,GAAGyD,eAAe,CACxBmB,QAAQ,CAACzF,IAAI,EACZ,GAAEyF,QAAQ,CAAC3a,IAAK,kBAAiB,EAClCoZ,KAAK;;QAELgB,IAAI,CAACpE,MACP,CAAC;QACD,IAAI,CAACD,EAAE,EAAE;QAET,MAAM;UAAEX;SAAM,GAAGD,IAAoC;QACrD,IAAIL,CAAC,CAACQ,gBAAgB,CAACH,IAAI,CAACE,MAAM,EAAE;UAAEE,MAAM,EAAEH;SAAM,CAAC,EAAE;UACrDU,UAAU,CAACX,IAAI,EAAEY,EAAE,CAAC;SACrB,MAAM;UACLZ,IAAI,CAACqB,WAAW,CAAC1B,CAAC,CAAC4B,cAAc,CAACX,EAAE,EAAE,CAACX,IAAI,CAACY,MAAM,CAAC,CAAC,CAAC;;;KAG1D;IAED0F,OAAO,EAAExD,MAAM,KAAK,cAAc,IAAI;;MAEpCyD,cAAcA,CAACxG,IAAgC,EAAE;QAC/C,IAAIA,IAAI,CAAC7J,GAAG,CAAC,QAAQ,CAAC,CAACsQ,QAAQ,EAAE,EAAE;UACjC,MAAMxC,KAAK,GAAGnB,QAAQ,CAAC9C,IAAI,CAAC;UAE5B,IAAIuD,SAAS,EAAE;;YAEbY,iBAAiB,CAACnY,gCAAgC,EAAEiY,KAAK,CAAC;WAC3D,MAAM;YACLE,iBAAiB,CAACpY,mBAAmB,EAAEkY,KAAK,CAAC;;;OAGlD;;MAGDhT,QAAQA,CAAC+O,IAA0B,EAAE;QACnC,IAAIA,IAAI,CAACC,IAAI,CAACyG,KAAK,EAAE;UACnBvC,iBAAiB,CAACpY,mBAAmB,EAAE+W,QAAQ,CAAC9C,IAAI,CAAC,CAAC;;OAEzD;;MAGD,6BAA6B2G,CAC3B3G,IAAiD,EACjD;QACAmE,iBAAiB,CAACzY,eAAe,EAAEoX,QAAQ,CAAC9C,IAAI,CAAC,CAAC;OACnD;;MAGD4G,aAAaA,CAAC5G,IAA+B,EAAE;QAC7C,IAAI,CAACA,IAAI,CAACyB,UAAU,CAACoF,kBAAkB,EAAE,EAAE;UACzC1C,iBAAiB,CAACzY,eAAe,EAAEoX,QAAQ,CAAC9C,IAAI,CAAC,CAAC;;OAErD;;MAGD8G,eAAeA,CAAC9G,IAAiC,EAAE;QACjD,IAAIA,IAAI,CAACC,IAAI,CAAC8G,QAAQ,EAAE;UACtB5C,iBAAiB,CAACzY,eAAe,EAAEoX,QAAQ,CAAC9C,IAAI,CAAC,CAAC;;OAErD;;MAGDgH,KAAKA,CAAChH,IAAuB,EAAE;QAAA,IAAAiH,qBAAA;QAC7B,MAAMC,aAAa,GACjB,EAAAD,qBAAA,GAAAjH,IAAI,CAACC,IAAI,CAACkH,UAAU,qBAApBF,qBAAA,CAAsB3G,MAAM,KAC5BN,IAAI,CAACC,IAAI,CAACmH,IAAI,CAACA,IAAI,CAAChK,IAAI,CACtBiK,EAAE;UAAA,IAAAC,WAAA;UAAA,QAAAA,WAAA,GAAKD,EAAE,CAAmBF,UAAU,qBAAhCG,WAAA,CAAkChH,MAAM;SAChD,CAAC;QACH,IAAI4G,aAAa,EAAE;UACjB/C,iBAAiB,CAACxX,6BAA6B,EAAEmW,QAAQ,CAAC9C,IAAI,CAAC,CAAC;;;;GAIvE;AACH,CAAC,CAAC;;;;"}