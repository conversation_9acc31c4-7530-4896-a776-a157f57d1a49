{"name": "bonjour-service", "version": "1.2.1", "description": "A Bonjour/Zeroconf implementation in TypeScript", "main": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"fast-deep-equal": "^3.1.3", "multicast-dns": "^7.2.5"}, "devDependencies": {"@types/node": "^16.18.11", "@typescript-eslint/eslint-plugin": "^5.22.0", "@typescript-eslint/parser": "^5.22.0", "after-all": "^2.0.2", "eslint": "^8.15.0", "standard": "^17.0.0", "tape": "^5.2.2", "ts-node-dev": "^2.0.0", "typescript": "^4.4.4"}, "scripts": {"test": "standard --fix && tape test/*.js", "build": "tsc --removeComments true", "dev": "ts-node-dev --respawn --transpile-only ./src/index.ts", "example:simple": "node examples/simple"}, "repository": {"type": "git", "url": "https://github.com/onlxltd/bonjour-service.git"}, "keywords": ["bonjour", "zeroconf", "zero", "configuration", "mdns", "dns", "service", "discovery", "multicast", "broadcast", "dns-sd"], "author": "ON LX Lited <<EMAIL>> (https://labs.onlx.ltd)", "license": "MIT", "bugs": {"url": "https://github.com/onlxltd/bonjour-service/issues"}, "homepage": "https://github.com/onlxltd/bonjour-service"}