{"name": "@webassemblyjs/helper-wasm-section", "version": "1.12.1", "description": "", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.12.1", "@webassemblyjs/helper-buffer": "1.12.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.6", "@webassemblyjs/wasm-gen": "1.12.1"}, "devDependencies": {"@webassemblyjs/wasm-parser": "1.12.1"}, "gitHead": "67c75cf2da65e5588b5e81c10b0d7692d174760b"}