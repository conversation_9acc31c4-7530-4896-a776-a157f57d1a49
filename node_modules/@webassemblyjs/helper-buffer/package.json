{"name": "@webassemblyjs/helper-buffer", "version": "1.12.1", "description": "Buffer manipulation utility", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"@webassemblyjs/wasm-parser": "1.12.1", "jest-diff": "^24.0.0"}, "gitHead": "67c75cf2da65e5588b5e81c10b0d7692d174760b"}