# Installation
> `npm install --save @types/imagemin`

# Summary
This package contains type definitions for imagemin (https://github.com/imagemin/imagemin#readme).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/imagemin.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/imagemin/index.d.ts)
````ts
/// <reference types="node" />

/**
 * @async
 */
declare function imagemin(input: readonly string[], options?: Options): Promise<Result[]>;

declare namespace imagemin {
    /**
     * @async
     */
    function buffer(input: Buffer, options?: BufferOptions): Promise<Buffer>;
}

export type Plugin = (input: Uint8Array) => Promise<Uint8Array>;

export interface Options {
    destination?: string | undefined;
    plugins: readonly Plugin[];
    glob?: boolean | undefined;
}

export interface Result {
    data: Buffer;
    sourcePath: string;
    destinationPath: string;
}

export interface BufferOptions {
    plugins: readonly Plugin[];
}

export default imagemin;

````

### Additional Details
 * Last updated: Mon, 03 Jun 2024 21:07:33 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node)

# Credits
These definitions were written by [Romain Faust](https://github.com/romain-faust), and [Jeff Chan](https://github.com/hkjeffchan).
