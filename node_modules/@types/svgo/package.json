{"name": "@types/svgo", "version": "1.3.6", "description": "TypeScript definitions for svgo", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/svgo", "license": "MIT", "contributors": [{"name": "Bradley Ayers", "url": "https://github.com/bradleyayers", "githubUsername": "bradleyayers"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/giladgray", "githubUsername": "giladgray"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Aankhen", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jankarres", "githubUsername": "jan<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/gavingregory", "githubUsername": "gaving<PERSON>gory"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/svgo"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "a1859e50480342db713c1b9e0739d99933148398dc6f0557a6a7429e58a46463", "typeScriptVersion": "3.6"}