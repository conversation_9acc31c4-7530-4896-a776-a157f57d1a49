# Installation
> `npm install --save @types/clean-css`

# Summary
This package contains type definitions for clean-css (https://github.com/clean-css/clean-css).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/clean-css.

### Additional Details
 * Last updated: Mon, 20 Nov 2023 23:36:24 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node), [source-map](https://npmjs.com/package/source-map)

# Credits
These definitions were written by [<PERSON><PERSON>](https://github.com/tkrotoff), and [<PERSON>](https://github.com/GolaWaya).
