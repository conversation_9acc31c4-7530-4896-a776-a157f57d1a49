{"name": "@types/imagemin-svgo", "version": "8.0.1", "description": "TypeScript definitions for imagemin-svgo", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/romain-faust", "githubUsername": "romain-faust"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/imagemin-svgo"}, "scripts": {}, "dependencies": {"@types/imagemin": "*", "@types/svgo": "^1"}, "typesPublisherContentHash": "e1aaa696ba0d69c1db2211228694de895b78f42c78dc496d7b8c107b63148c02", "typeScriptVersion": "3.5"}