/**
 * Turkmen translation for bootstrap-datepicker
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <n<PERSON><PERSON><PERSON><PERSON><PERSON>@outlook.com>
 */
;(function($){
	$.fn.datepicker.dates['tk'] = {
		days: ["Ýekşenbe", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"],
		daysShort: ["Ýek", "Du<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"],
		daysMin: ["Ýe", "<PERSON>", "<PERSON>", "<PERSON>a", "<PERSON>e", "<PERSON>", "<PERSON>e"],
		months: ["Ýanwar", "<PERSON><PERSON>", "<PERSON>", "<PERSON>el", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Awgust", "<PERSON>týabr", "Okt<PERSON>abr", "Noýabr", "Dekabr"],
		monthsShort: ["Ýan", "Few", "<PERSON>", "Apr", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Awg", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "De<PERSON>"],
		today: "<PERSON><PERSON> gün",
		monthsTitle: "<PERSON>ý<PERSON>",
		clear: "Aýyr",
		weekStart: 1,
		format: "dd.mm.yyyy"
	};
}(jQuery));
