/**
 * Estonian translation for bootstrap-datepicker
 * <PERSON><PERSON> <https://github.com/anroots>
 * Fixes by <PERSON><PERSON><PERSON> <<https://github.com/ragulka>
 */
;(function($){
	$.fn.datepicker.dates['et'] = {
		days: ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"],
		daysShort: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"],
		daysMin: ["P", "E", "T", "K", "N", "R", "L"],
		months: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Aprill", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "August", "September", "Oktoober", "November", "Detsember"],
		monthsShort: ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Aug", "Sept", "Ok<PERSON>", "<PERSON>", "Det<PERSON>"],
		today: "Täna",
		clear: "<PERSON>ü<PERSON><PERSON><PERSON>",
		weekStart: 1,
		format: "dd.mm.yyyy"
	};
}(jQuery));
