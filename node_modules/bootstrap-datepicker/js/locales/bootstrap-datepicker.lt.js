/**
 * Lithuanian translation for bootstrap-datepicker
 * <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 */

;(function($){
    $.fn.datepicker.dates['lt'] = {
        days: ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pirmadie<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Trečiadienis", "Ketvirtadien<PERSON>", "Penktadienis", "Šeštadienis"],
        daysShort: ["S", "Pr", "A", "T", "K", "Pn", "Š"],
        daysMin: ["Sk", "Pr", "An", "Tr", "Ke", "Pn", "Št"],
        months: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Rugsėji<PERSON>", "<PERSON><PERSON>", "Lapkrit<PERSON>", "<PERSON><PERSON>od<PERSON>"],
        monthsShort: ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>ugp", "Rugs", "<PERSON>", "Lap", "<PERSON>ru"],
        today: "Šiandien",
        monthsTitle:"<PERSON>ėnesiai",
        clear:"<PERSON><PERSON><PERSON><PERSON><PERSON>",
        weekStart: 1,
        format:"yyyy-mm-dd"
    };
}(jQuery));
