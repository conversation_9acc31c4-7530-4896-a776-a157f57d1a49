{"version": 3, "file": "carousel.js", "sources": ["../src/carousel.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.0): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst VERSION = '4.6.0'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst ARROW_LEFT_KEYCODE = 37 // KeyboardEvent.which value for left arrow key\nconst ARROW_RIGHT_KEYCODE = 39 // KeyboardEvent.which value for right arrow key\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst DIRECTION_NEXT = 'next'\nconst DIRECTION_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_RIGHT = 'carousel-item-right'\nconst CLASS_NAME_LEFT = 'carousel-item-left'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-slide], [data-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-ride=\"carousel\"]'\n\nconst PointerType = {\n  TOUCH: 'touch',\n  PEN: 'pen'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel {\n  constructor(element, config) {\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._element = element\n    this._indicatorsElement = this._element.querySelector(SELECTOR_INDICATORS)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent || window.MSPointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    const $element = $(this._element)\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden &&\n      ($element.is(':visible') && $element.css('visibility') !== 'hidden')) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (this._element.querySelector(SELECTOR_NEXT_PREV)) {\n      Util.triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = this._element.querySelector(SELECTOR_ACTIVE_ITEM)\n\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      $(this._element).one(EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex ?\n      DIRECTION_NEXT :\n      DIRECTION_PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    $(this._element).off(EVENT_KEY)\n    $.removeData(this._element, DATA_KEY)\n\n    this._items = null\n    this._config = null\n    this._element = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    Util.typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      $(this._element).on(EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      $(this._element)\n        .on(EVENT_MOUSEENTER, event => this.pause(event))\n        .on(EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    if (!this._touchSupported) {\n      return\n    }\n\n    const start = event => {\n      if (this._pointerEvent && PointerType[event.originalEvent.pointerType.toUpperCase()]) {\n        this.touchStartX = event.originalEvent.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.originalEvent.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      if (event.originalEvent.touches && event.originalEvent.touches.length > 1) {\n        this.touchDeltaX = 0\n      } else {\n        this.touchDeltaX = event.originalEvent.touches[0].clientX - this.touchStartX\n      }\n    }\n\n    const end = event => {\n      if (this._pointerEvent && PointerType[event.originalEvent.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.originalEvent.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    $(this._element.querySelectorAll(SELECTOR_ITEM_IMG))\n      .on(EVENT_DRAG_START, e => e.preventDefault())\n\n    if (this._pointerEvent) {\n      $(this._element).on(EVENT_POINTERDOWN, event => start(event))\n      $(this._element).on(EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      $(this._element).on(EVENT_TOUCHSTART, event => start(event))\n      $(this._element).on(EVENT_TOUCHMOVE, event => move(event))\n      $(this._element).on(EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.which) {\n      case ARROW_LEFT_KEYCODE:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEYCODE:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      [].slice.call(element.parentNode.querySelectorAll(SELECTOR_ITEM)) :\n      []\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === DIRECTION_NEXT\n    const isPrevDirection = direction === DIRECTION_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = isPrevDirection && activeIndex === 0 ||\n                            isNextDirection && activeIndex === lastItemIndex\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = direction === DIRECTION_PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] : this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(this._element.querySelector(SELECTOR_ACTIVE_ITEM))\n    const slideEvent = $.Event(EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n\n    $(this._element).trigger(slideEvent)\n\n    return slideEvent\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = [].slice.call(this._indicatorsElement.querySelectorAll(SELECTOR_ACTIVE))\n      $(indicators).removeClass(CLASS_NAME_ACTIVE)\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        $(nextIndicator).addClass(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || this._element.querySelector(SELECTOR_ACTIVE_ITEM)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = parseInt(element.getAttribute('data-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = this._element.querySelector(SELECTOR_ACTIVE_ITEM)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || activeElement &&\n      this._getItemByDirection(direction, activeElement)\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === DIRECTION_NEXT) {\n      directionalClassName = CLASS_NAME_LEFT\n      orderClassName = CLASS_NAME_NEXT\n      eventDirectionName = DIRECTION_LEFT\n    } else {\n      directionalClassName = CLASS_NAME_RIGHT\n      orderClassName = CLASS_NAME_PREV\n      eventDirectionName = DIRECTION_RIGHT\n    }\n\n    if (nextElement && $(nextElement).hasClass(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    const slidEvent = $.Event(EVENT_SLID, {\n      relatedTarget: nextElement,\n      direction: eventDirectionName,\n      from: activeElementIndex,\n      to: nextElementIndex\n    })\n\n    if ($(this._element).hasClass(CLASS_NAME_SLIDE)) {\n      $(nextElement).addClass(orderClassName)\n\n      Util.reflow(nextElement)\n\n      $(activeElement).addClass(directionalClassName)\n      $(nextElement).addClass(directionalClassName)\n\n      const transitionDuration = Util.getTransitionDurationFromElement(activeElement)\n\n      $(activeElement)\n        .one(Util.TRANSITION_END, () => {\n          $(nextElement)\n            .removeClass(`${directionalClassName} ${orderClassName}`)\n            .addClass(CLASS_NAME_ACTIVE)\n\n          $(activeElement).removeClass(`${CLASS_NAME_ACTIVE} ${orderClassName} ${directionalClassName}`)\n\n          this._isSliding = false\n\n          setTimeout(() => $(this._element).trigger(slidEvent), 0)\n        })\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      $(activeElement).removeClass(CLASS_NAME_ACTIVE)\n      $(nextElement).addClass(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      $(this._element).trigger(slidEvent)\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      let _config = {\n        ...Default,\n        ...$(this).data()\n      }\n\n      if (typeof config === 'object') {\n        _config = {\n          ..._config,\n          ...config\n        }\n      }\n\n      const action = typeof config === 'string' ? config : _config.slide\n\n      if (!data) {\n        data = new Carousel(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'number') {\n        data.to(config)\n      } else if (typeof action === 'string') {\n        if (typeof data[action] === 'undefined') {\n          throw new TypeError(`No method named \"${action}\"`)\n        }\n\n        data[action]()\n      } else if (_config.interval && _config.ride) {\n        data.pause()\n        data.cycle()\n      }\n    })\n  }\n\n  static _dataApiClickHandler(event) {\n    const selector = Util.getSelectorFromElement(this)\n\n    if (!selector) {\n      return\n    }\n\n    const target = $(selector)[0]\n\n    if (!target || !$(target).hasClass(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...$(target).data(),\n      ...$(this).data()\n    }\n    const slideIndex = this.getAttribute('data-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel._jQueryInterface.call($(target), config)\n\n    if (slideIndex) {\n      $(target).data(DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document).on(EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel._dataApiClickHandler)\n\n$(window).on(EVENT_LOAD_DATA_API, () => {\n  const carousels = [].slice.call(document.querySelectorAll(SELECTOR_DATA_RIDE))\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    const $carousel = $(carousels[i])\n    Carousel._jQueryInterface.call($carousel, $carousel.data())\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Carousel._jQueryInterface\n$.fn[NAME].Constructor = Carousel\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Carousel._jQueryInterface\n}\n\nexport default Carousel\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "ARROW_LEFT_KEYCODE", "ARROW_RIGHT_KEYCODE", "TOUCHEVENT_COMPAT_WAIT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "DIRECTION_NEXT", "DIRECTION_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "EVENT_CLICK_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_ACTIVE", "CLASS_NAME_SLIDE", "CLASS_NAME_RIGHT", "CLASS_NAME_LEFT", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "CLASS_NAME_POINTER_EVENT", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_NEXT_PREV", "SELECTOR_INDICATORS", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "PointerType", "TOUCH", "PEN", "Carousel", "element", "config", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_element", "_indicatorsElement", "querySelector", "_touchSupported", "document", "documentElement", "navigator", "maxTouchPoints", "_pointerEvent", "Boolean", "window", "PointerEvent", "MSPointerEvent", "_addEventListeners", "next", "_slide", "nextWhenVisible", "$element", "hidden", "is", "css", "prev", "event", "<PERSON><PERSON>", "triggerTransitionEnd", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "length", "one", "direction", "dispose", "off", "removeData", "typeCheckConfig", "_handleSwipe", "absDeltax", "Math", "abs", "on", "_keydown", "_addTouchEventListeners", "start", "originalEvent", "pointerType", "toUpperCase", "clientX", "touches", "move", "end", "clearTimeout", "setTimeout", "querySelectorAll", "e", "preventDefault", "classList", "add", "test", "target", "tagName", "which", "parentNode", "slice", "call", "indexOf", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "isGoingToWrap", "delta", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "slideEvent", "Event", "from", "trigger", "_setActiveIndicatorElement", "indicators", "removeClass", "nextIndicator", "children", "addClass", "elementInterval", "parseInt", "getAttribute", "defaultInterval", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "hasClass", "isDefaultPrevented", "slidEvent", "reflow", "transitionDuration", "getTransitionDurationFromElement", "TRANSITION_END", "emulateTransitionEnd", "_jQueryInterface", "each", "data", "action", "TypeError", "ride", "_dataApiClickHandler", "selector", "getSelectorFromElement", "slideIndex", "carousels", "i", "len", "$carousel", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;EACA;EACA;EACA;EACA;;EAEA,IAAMA,IAAI,GAAG,UAAb;EACA,IAAMC,OAAO,GAAG,OAAhB;EACA,IAAMC,QAAQ,GAAG,aAAjB;EACA,IAAMC,SAAS,SAAOD,QAAtB;EACA,IAAME,YAAY,GAAG,WAArB;EACA,IAAMC,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B;EACA,IAAMQ,kBAAkB,GAAG,EAA3B;;EACA,IAAMC,mBAAmB,GAAG,EAA5B;;EACA,IAAMC,sBAAsB,GAAG,GAA/B;;EACA,IAAMC,eAAe,GAAG,EAAxB;EAEA,IAAMC,OAAO,GAAG;EACdC,EAAAA,QAAQ,EAAE,IADI;EAEdC,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,KAAK,EAAE,KAHO;EAIdC,EAAAA,KAAK,EAAE,OAJO;EAKdC,EAAAA,IAAI,EAAE,IALQ;EAMdC,EAAAA,KAAK,EAAE;EANO,CAAhB;EASA,IAAMC,WAAW,GAAG;EAClBN,EAAAA,QAAQ,EAAE,kBADQ;EAElBC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,KAAK,EAAE,kBAHW;EAIlBC,EAAAA,KAAK,EAAE,kBAJW;EAKlBC,EAAAA,IAAI,EAAE,SALY;EAMlBC,EAAAA,KAAK,EAAE;EANW,CAApB;EASA,IAAME,cAAc,GAAG,MAAvB;EACA,IAAMC,cAAc,GAAG,MAAvB;EACA,IAAMC,cAAc,GAAG,MAAvB;EACA,IAAMC,eAAe,GAAG,OAAxB;EAEA,IAAMC,WAAW,aAAWrB,SAA5B;EACA,IAAMsB,UAAU,YAAUtB,SAA1B;EACA,IAAMuB,aAAa,eAAavB,SAAhC;EACA,IAAMwB,gBAAgB,kBAAgBxB,SAAtC;EACA,IAAMyB,gBAAgB,kBAAgBzB,SAAtC;EACA,IAAM0B,gBAAgB,kBAAgB1B,SAAtC;EACA,IAAM2B,eAAe,iBAAe3B,SAApC;EACA,IAAM4B,cAAc,gBAAc5B,SAAlC;EACA,IAAM6B,iBAAiB,mBAAiB7B,SAAxC;EACA,IAAM8B,eAAe,iBAAe9B,SAApC;EACA,IAAM+B,gBAAgB,iBAAe/B,SAArC;EACA,IAAMgC,mBAAmB,YAAUhC,SAAV,GAAsBC,YAA/C;EACA,IAAMgC,oBAAoB,aAAWjC,SAAX,GAAuBC,YAAjD;EAEA,IAAMiC,mBAAmB,GAAG,UAA5B;EACA,IAAMC,iBAAiB,GAAG,QAA1B;EACA,IAAMC,gBAAgB,GAAG,OAAzB;EACA,IAAMC,gBAAgB,GAAG,qBAAzB;EACA,IAAMC,eAAe,GAAG,oBAAxB;EACA,IAAMC,eAAe,GAAG,oBAAxB;EACA,IAAMC,eAAe,GAAG,oBAAxB;EACA,IAAMC,wBAAwB,GAAG,eAAjC;EAEA,IAAMC,eAAe,GAAG,SAAxB;EACA,IAAMC,oBAAoB,GAAG,uBAA7B;EACA,IAAMC,aAAa,GAAG,gBAAtB;EACA,IAAMC,iBAAiB,GAAG,oBAA1B;EACA,IAAMC,kBAAkB,GAAG,0CAA3B;EACA,IAAMC,mBAAmB,GAAG,sBAA5B;EACA,IAAMC,mBAAmB,GAAG,+BAA5B;EACA,IAAMC,kBAAkB,GAAG,wBAA3B;EAEA,IAAMC,WAAW,GAAG;EAClBC,EAAAA,KAAK,EAAE,OADW;EAElBC,EAAAA,GAAG,EAAE;EAFa,CAApB;EAKA;EACA;EACA;EACA;EACA;;MACMC;EACJ,oBAAYC,OAAZ,EAAqBC,MAArB,EAA6B;EAC3B,SAAKC,MAAL,GAAc,IAAd;EACA,SAAKC,SAAL,GAAiB,IAAjB;EACA,SAAKC,cAAL,GAAsB,IAAtB;EACA,SAAKC,SAAL,GAAiB,KAAjB;EACA,SAAKC,UAAL,GAAkB,KAAlB;EACA,SAAKC,YAAL,GAAoB,IAApB;EACA,SAAKC,WAAL,GAAmB,CAAnB;EACA,SAAKC,WAAL,GAAmB,CAAnB;EAEA,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgBV,MAAhB,CAAf;EACA,SAAKW,QAAL,GAAgBZ,OAAhB;EACA,SAAKa,kBAAL,GAA0B,KAAKD,QAAL,CAAcE,aAAd,CAA4BrB,mBAA5B,CAA1B;EACA,SAAKsB,eAAL,GAAuB,kBAAkBC,QAAQ,CAACC,eAA3B,IAA8CC,SAAS,CAACC,cAAV,GAA2B,CAAhG;EACA,SAAKC,aAAL,GAAqBC,OAAO,CAACC,MAAM,CAACC,YAAP,IAAuBD,MAAM,CAACE,cAA/B,CAA5B;;EAEA,SAAKC,kBAAL;EACD;;;;;EAYD;WAEAC,OAAA,gBAAO;EACL,QAAI,CAAC,KAAKpB,UAAV,EAAsB;EACpB,WAAKqB,MAAL,CAAYhE,cAAZ;EACD;EACF;;WAEDiE,kBAAA,2BAAkB;EAChB,QAAMC,QAAQ,GAAGhF,qBAAC,CAAC,KAAK+D,QAAN,CAAlB,CADgB;EAGhB;;EACA,QAAI,CAACI,QAAQ,CAACc,MAAV,IACDD,QAAQ,CAACE,EAAT,CAAY,UAAZ,KAA2BF,QAAQ,CAACG,GAAT,CAAa,YAAb,MAA+B,QAD7D,EACwE;EACtE,WAAKN,IAAL;EACD;EACF;;WAEDO,OAAA,gBAAO;EACL,QAAI,CAAC,KAAK3B,UAAV,EAAsB;EACpB,WAAKqB,MAAL,CAAY/D,cAAZ;EACD;EACF;;WAEDL,QAAA,eAAM2E,KAAN,EAAa;EACX,QAAI,CAACA,KAAL,EAAY;EACV,WAAK7B,SAAL,GAAiB,IAAjB;EACD;;EAED,QAAI,KAAKO,QAAL,CAAcE,aAAd,CAA4BtB,kBAA5B,CAAJ,EAAqD;EACnD2C,MAAAA,wBAAI,CAACC,oBAAL,CAA0B,KAAKxB,QAA/B;EACA,WAAKyB,KAAL,CAAW,IAAX;EACD;;EAEDC,IAAAA,aAAa,CAAC,KAAKnC,SAAN,CAAb;EACA,SAAKA,SAAL,GAAiB,IAAjB;EACD;;WAEDkC,QAAA,eAAMH,KAAN,EAAa;EACX,QAAI,CAACA,KAAL,EAAY;EACV,WAAK7B,SAAL,GAAiB,KAAjB;EACD;;EAED,QAAI,KAAKF,SAAT,EAAoB;EAClBmC,MAAAA,aAAa,CAAC,KAAKnC,SAAN,CAAb;EACA,WAAKA,SAAL,GAAiB,IAAjB;EACD;;EAED,QAAI,KAAKO,OAAL,CAAatD,QAAb,IAAyB,CAAC,KAAKiD,SAAnC,EAA8C;EAC5C,WAAKkC,eAAL;;EAEA,WAAKpC,SAAL,GAAiBqC,WAAW,CAC1B,CAACxB,QAAQ,CAACyB,eAAT,GAA2B,KAAKb,eAAhC,GAAkD,KAAKF,IAAxD,EAA8DgB,IAA9D,CAAmE,IAAnE,CAD0B,EAE1B,KAAKhC,OAAL,CAAatD,QAFa,CAA5B;EAID;EACF;;WAEDuF,KAAA,YAAGC,KAAH,EAAU;EAAA;;EACR,SAAKxC,cAAL,GAAsB,KAAKQ,QAAL,CAAcE,aAAd,CAA4BzB,oBAA5B,CAAtB;;EAEA,QAAMwD,WAAW,GAAG,KAAKC,aAAL,CAAmB,KAAK1C,cAAxB,CAApB;;EAEA,QAAIwC,KAAK,GAAG,KAAK1C,MAAL,CAAY6C,MAAZ,GAAqB,CAA7B,IAAkCH,KAAK,GAAG,CAA9C,EAAiD;EAC/C;EACD;;EAED,QAAI,KAAKtC,UAAT,EAAqB;EACnBzD,MAAAA,qBAAC,CAAC,KAAK+D,QAAN,CAAD,CAAiBoC,GAAjB,CAAqBhF,UAArB,EAAiC;EAAA,eAAM,KAAI,CAAC2E,EAAL,CAAQC,KAAR,CAAN;EAAA,OAAjC;EACA;EACD;;EAED,QAAIC,WAAW,KAAKD,KAApB,EAA2B;EACzB,WAAKrF,KAAL;EACA,WAAK8E,KAAL;EACA;EACD;;EAED,QAAMY,SAAS,GAAGL,KAAK,GAAGC,WAAR,GAChBlF,cADgB,GAEhBC,cAFF;;EAIA,SAAK+D,MAAL,CAAYsB,SAAZ,EAAuB,KAAK/C,MAAL,CAAY0C,KAAZ,CAAvB;EACD;;WAEDM,UAAA,mBAAU;EACRrG,IAAAA,qBAAC,CAAC,KAAK+D,QAAN,CAAD,CAAiBuC,GAAjB,CAAqBzG,SAArB;EACAG,IAAAA,qBAAC,CAACuG,UAAF,CAAa,KAAKxC,QAAlB,EAA4BnE,QAA5B;EAEA,SAAKyD,MAAL,GAAc,IAAd;EACA,SAAKQ,OAAL,GAAe,IAAf;EACA,SAAKE,QAAL,GAAgB,IAAhB;EACA,SAAKT,SAAL,GAAiB,IAAjB;EACA,SAAKE,SAAL,GAAiB,IAAjB;EACA,SAAKC,UAAL,GAAkB,IAAlB;EACA,SAAKF,cAAL,GAAsB,IAAtB;EACA,SAAKS,kBAAL,GAA0B,IAA1B;EACD;;;WAIDF,aAAA,oBAAWV,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACD9C,OADC,EAED8C,MAFC,CAAN;EAIAkC,IAAAA,wBAAI,CAACkB,eAAL,CAAqB9G,IAArB,EAA2B0D,MAA3B,EAAmCvC,WAAnC;EACA,WAAOuC,MAAP;EACD;;WAEDqD,eAAA,wBAAe;EACb,QAAMC,SAAS,GAAGC,IAAI,CAACC,GAAL,CAAS,KAAKhD,WAAd,CAAlB;;EAEA,QAAI8C,SAAS,IAAIrG,eAAjB,EAAkC;EAChC;EACD;;EAED,QAAM+F,SAAS,GAAGM,SAAS,GAAG,KAAK9C,WAAnC;EAEA,SAAKA,WAAL,GAAmB,CAAnB,CATa;;EAYb,QAAIwC,SAAS,GAAG,CAAhB,EAAmB;EACjB,WAAKhB,IAAL;EACD,KAdY;;;EAiBb,QAAIgB,SAAS,GAAG,CAAhB,EAAmB;EACjB,WAAKvB,IAAL;EACD;EACF;;WAEDD,qBAAA,8BAAqB;EAAA;;EACnB,QAAI,KAAKf,OAAL,CAAarD,QAAjB,EAA2B;EACzBR,MAAAA,qBAAC,CAAC,KAAK+D,QAAN,CAAD,CAAiB8C,EAAjB,CAAoBzF,aAApB,EAAmC,UAAAiE,KAAK;EAAA,eAAI,MAAI,CAACyB,QAAL,CAAczB,KAAd,CAAJ;EAAA,OAAxC;EACD;;EAED,QAAI,KAAKxB,OAAL,CAAanD,KAAb,KAAuB,OAA3B,EAAoC;EAClCV,MAAAA,qBAAC,CAAC,KAAK+D,QAAN,CAAD,CACG8C,EADH,CACMxF,gBADN,EACwB,UAAAgE,KAAK;EAAA,eAAI,MAAI,CAAC3E,KAAL,CAAW2E,KAAX,CAAJ;EAAA,OAD7B,EAEGwB,EAFH,CAEMvF,gBAFN,EAEwB,UAAA+D,KAAK;EAAA,eAAI,MAAI,CAACG,KAAL,CAAWH,KAAX,CAAJ;EAAA,OAF7B;EAGD;;EAED,QAAI,KAAKxB,OAAL,CAAajD,KAAjB,EAAwB;EACtB,WAAKmG,uBAAL;EACD;EACF;;WAEDA,0BAAA,mCAA0B;EAAA;;EACxB,QAAI,CAAC,KAAK7C,eAAV,EAA2B;EACzB;EACD;;EAED,QAAM8C,KAAK,GAAG,SAARA,KAAQ,CAAA3B,KAAK,EAAI;EACrB,UAAI,MAAI,CAACd,aAAL,IAAsBxB,WAAW,CAACsC,KAAK,CAAC4B,aAAN,CAAoBC,WAApB,CAAgCC,WAAhC,EAAD,CAArC,EAAsF;EACpF,QAAA,MAAI,CAACxD,WAAL,GAAmB0B,KAAK,CAAC4B,aAAN,CAAoBG,OAAvC;EACD,OAFD,MAEO,IAAI,CAAC,MAAI,CAAC7C,aAAV,EAAyB;EAC9B,QAAA,MAAI,CAACZ,WAAL,GAAmB0B,KAAK,CAAC4B,aAAN,CAAoBI,OAApB,CAA4B,CAA5B,EAA+BD,OAAlD;EACD;EACF,KAND;;EAQA,QAAME,IAAI,GAAG,SAAPA,IAAO,CAAAjC,KAAK,EAAI;EACpB;EACA,UAAIA,KAAK,CAAC4B,aAAN,CAAoBI,OAApB,IAA+BhC,KAAK,CAAC4B,aAAN,CAAoBI,OAApB,CAA4BnB,MAA5B,GAAqC,CAAxE,EAA2E;EACzE,QAAA,MAAI,CAACtC,WAAL,GAAmB,CAAnB;EACD,OAFD,MAEO;EACL,QAAA,MAAI,CAACA,WAAL,GAAmByB,KAAK,CAAC4B,aAAN,CAAoBI,OAApB,CAA4B,CAA5B,EAA+BD,OAA/B,GAAyC,MAAI,CAACzD,WAAjE;EACD;EACF,KAPD;;EASA,QAAM4D,GAAG,GAAG,SAANA,GAAM,CAAAlC,KAAK,EAAI;EACnB,UAAI,MAAI,CAACd,aAAL,IAAsBxB,WAAW,CAACsC,KAAK,CAAC4B,aAAN,CAAoBC,WAApB,CAAgCC,WAAhC,EAAD,CAArC,EAAsF;EACpF,QAAA,MAAI,CAACvD,WAAL,GAAmByB,KAAK,CAAC4B,aAAN,CAAoBG,OAApB,GAA8B,MAAI,CAACzD,WAAtD;EACD;;EAED,MAAA,MAAI,CAAC8C,YAAL;;EACA,UAAI,MAAI,CAAC5C,OAAL,CAAanD,KAAb,KAAuB,OAA3B,EAAoC;EAClC;EACA;EACA;EACA;EACA;EACA;EACA;EAEA,QAAA,MAAI,CAACA,KAAL;;EACA,YAAI,MAAI,CAACgD,YAAT,EAAuB;EACrB8D,UAAAA,YAAY,CAAC,MAAI,CAAC9D,YAAN,CAAZ;EACD;;EAED,QAAA,MAAI,CAACA,YAAL,GAAoB+D,UAAU,CAAC,UAAApC,KAAK;EAAA,iBAAI,MAAI,CAACG,KAAL,CAAWH,KAAX,CAAJ;EAAA,SAAN,EAA6BjF,sBAAsB,GAAG,MAAI,CAACyD,OAAL,CAAatD,QAAnE,CAA9B;EACD;EACF,KAtBD;;EAwBAP,IAAAA,qBAAC,CAAC,KAAK+D,QAAL,CAAc2D,gBAAd,CAA+BhF,iBAA/B,CAAD,CAAD,CACGmE,EADH,CACMjF,gBADN,EACwB,UAAA+F,CAAC;EAAA,aAAIA,CAAC,CAACC,cAAF,EAAJ;EAAA,KADzB;;EAGA,QAAI,KAAKrD,aAAT,EAAwB;EACtBvE,MAAAA,qBAAC,CAAC,KAAK+D,QAAN,CAAD,CAAiB8C,EAAjB,CAAoBnF,iBAApB,EAAuC,UAAA2D,KAAK;EAAA,eAAI2B,KAAK,CAAC3B,KAAD,CAAT;EAAA,OAA5C;EACArF,MAAAA,qBAAC,CAAC,KAAK+D,QAAN,CAAD,CAAiB8C,EAAjB,CAAoBlF,eAApB,EAAqC,UAAA0D,KAAK;EAAA,eAAIkC,GAAG,CAAClC,KAAD,CAAP;EAAA,OAA1C;;EAEA,WAAKtB,QAAL,CAAc8D,SAAd,CAAwBC,GAAxB,CAA4BxF,wBAA5B;EACD,KALD,MAKO;EACLtC,MAAAA,qBAAC,CAAC,KAAK+D,QAAN,CAAD,CAAiB8C,EAAjB,CAAoBtF,gBAApB,EAAsC,UAAA8D,KAAK;EAAA,eAAI2B,KAAK,CAAC3B,KAAD,CAAT;EAAA,OAA3C;EACArF,MAAAA,qBAAC,CAAC,KAAK+D,QAAN,CAAD,CAAiB8C,EAAjB,CAAoBrF,eAApB,EAAqC,UAAA6D,KAAK;EAAA,eAAIiC,IAAI,CAACjC,KAAD,CAAR;EAAA,OAA1C;EACArF,MAAAA,qBAAC,CAAC,KAAK+D,QAAN,CAAD,CAAiB8C,EAAjB,CAAoBpF,cAApB,EAAoC,UAAA4D,KAAK;EAAA,eAAIkC,GAAG,CAAClC,KAAD,CAAP;EAAA,OAAzC;EACD;EACF;;WAEDyB,WAAA,kBAASzB,KAAT,EAAgB;EACd,QAAI,kBAAkB0C,IAAlB,CAAuB1C,KAAK,CAAC2C,MAAN,CAAaC,OAApC,CAAJ,EAAkD;EAChD;EACD;;EAED,YAAQ5C,KAAK,CAAC6C,KAAd;EACE,WAAKhI,kBAAL;EACEmF,QAAAA,KAAK,CAACuC,cAAN;EACA,aAAKxC,IAAL;EACA;;EACF,WAAKjF,mBAAL;EACEkF,QAAAA,KAAK,CAACuC,cAAN;EACA,aAAK/C,IAAL;EACA;EARJ;EAWD;;WAEDoB,gBAAA,uBAAc9C,OAAd,EAAuB;EACrB,SAAKE,MAAL,GAAcF,OAAO,IAAIA,OAAO,CAACgF,UAAnB,GACZ,GAAGC,KAAH,CAASC,IAAT,CAAclF,OAAO,CAACgF,UAAR,CAAmBT,gBAAnB,CAAoCjF,aAApC,CAAd,CADY,GAEZ,EAFF;EAGA,WAAO,KAAKY,MAAL,CAAYiF,OAAZ,CAAoBnF,OAApB,CAAP;EACD;;WAEDoF,sBAAA,6BAAoBnC,SAApB,EAA+BoC,aAA/B,EAA8C;EAC5C,QAAMC,eAAe,GAAGrC,SAAS,KAAKtF,cAAtC;EACA,QAAM4H,eAAe,GAAGtC,SAAS,KAAKrF,cAAtC;;EACA,QAAMiF,WAAW,GAAG,KAAKC,aAAL,CAAmBuC,aAAnB,CAApB;;EACA,QAAMG,aAAa,GAAG,KAAKtF,MAAL,CAAY6C,MAAZ,GAAqB,CAA3C;EACA,QAAM0C,aAAa,GAAGF,eAAe,IAAI1C,WAAW,KAAK,CAAnC,IACEyC,eAAe,IAAIzC,WAAW,KAAK2C,aAD3D;;EAGA,QAAIC,aAAa,IAAI,CAAC,KAAK/E,OAAL,CAAalD,IAAnC,EAAyC;EACvC,aAAO6H,aAAP;EACD;;EAED,QAAMK,KAAK,GAAGzC,SAAS,KAAKrF,cAAd,GAA+B,CAAC,CAAhC,GAAoC,CAAlD;EACA,QAAM+H,SAAS,GAAG,CAAC9C,WAAW,GAAG6C,KAAf,IAAwB,KAAKxF,MAAL,CAAY6C,MAAtD;EAEA,WAAO4C,SAAS,KAAK,CAAC,CAAf,GACL,KAAKzF,MAAL,CAAY,KAAKA,MAAL,CAAY6C,MAAZ,GAAqB,CAAjC,CADK,GACiC,KAAK7C,MAAL,CAAYyF,SAAZ,CADxC;EAED;;WAEDC,qBAAA,4BAAmBC,aAAnB,EAAkCC,kBAAlC,EAAsD;EACpD,QAAMC,WAAW,GAAG,KAAKjD,aAAL,CAAmB+C,aAAnB,CAApB;;EACA,QAAMG,SAAS,GAAG,KAAKlD,aAAL,CAAmB,KAAKlC,QAAL,CAAcE,aAAd,CAA4BzB,oBAA5B,CAAnB,CAAlB;;EACA,QAAM4G,UAAU,GAAGpJ,qBAAC,CAACqJ,KAAF,CAAQnI,WAAR,EAAqB;EACtC8H,MAAAA,aAAa,EAAbA,aADsC;EAEtC5C,MAAAA,SAAS,EAAE6C,kBAF2B;EAGtCK,MAAAA,IAAI,EAAEH,SAHgC;EAItCrD,MAAAA,EAAE,EAAEoD;EAJkC,KAArB,CAAnB;EAOAlJ,IAAAA,qBAAC,CAAC,KAAK+D,QAAN,CAAD,CAAiBwF,OAAjB,CAAyBH,UAAzB;EAEA,WAAOA,UAAP;EACD;;WAEDI,6BAAA,oCAA2BrG,OAA3B,EAAoC;EAClC,QAAI,KAAKa,kBAAT,EAA6B;EAC3B,UAAMyF,UAAU,GAAG,GAAGrB,KAAH,CAASC,IAAT,CAAc,KAAKrE,kBAAL,CAAwB0D,gBAAxB,CAAyCnF,eAAzC,CAAd,CAAnB;EACAvC,MAAAA,qBAAC,CAACyJ,UAAD,CAAD,CAAcC,WAAd,CAA0B1H,iBAA1B;;EAEA,UAAM2H,aAAa,GAAG,KAAK3F,kBAAL,CAAwB4F,QAAxB,CACpB,KAAK3D,aAAL,CAAmB9C,OAAnB,CADoB,CAAtB;;EAIA,UAAIwG,aAAJ,EAAmB;EACjB3J,QAAAA,qBAAC,CAAC2J,aAAD,CAAD,CAAiBE,QAAjB,CAA0B7H,iBAA1B;EACD;EACF;EACF;;WAED0D,kBAAA,2BAAkB;EAChB,QAAMvC,OAAO,GAAG,KAAKI,cAAL,IAAuB,KAAKQ,QAAL,CAAcE,aAAd,CAA4BzB,oBAA5B,CAAvC;;EAEA,QAAI,CAACW,OAAL,EAAc;EACZ;EACD;;EAED,QAAM2G,eAAe,GAAGC,QAAQ,CAAC5G,OAAO,CAAC6G,YAAR,CAAqB,eAArB,CAAD,EAAwC,EAAxC,CAAhC;;EAEA,QAAIF,eAAJ,EAAqB;EACnB,WAAKjG,OAAL,CAAaoG,eAAb,GAA+B,KAAKpG,OAAL,CAAaoG,eAAb,IAAgC,KAAKpG,OAAL,CAAatD,QAA5E;EACA,WAAKsD,OAAL,CAAatD,QAAb,GAAwBuJ,eAAxB;EACD,KAHD,MAGO;EACL,WAAKjG,OAAL,CAAatD,QAAb,GAAwB,KAAKsD,OAAL,CAAaoG,eAAb,IAAgC,KAAKpG,OAAL,CAAatD,QAArE;EACD;EACF;;WAEDuE,SAAA,gBAAOsB,SAAP,EAAkBjD,OAAlB,EAA2B;EAAA;;EACzB,QAAMqF,aAAa,GAAG,KAAKzE,QAAL,CAAcE,aAAd,CAA4BzB,oBAA5B,CAAtB;;EACA,QAAM0H,kBAAkB,GAAG,KAAKjE,aAAL,CAAmBuC,aAAnB,CAA3B;;EACA,QAAM2B,WAAW,GAAGhH,OAAO,IAAIqF,aAAa,IAC1C,KAAKD,mBAAL,CAAyBnC,SAAzB,EAAoCoC,aAApC,CADF;;EAEA,QAAM4B,gBAAgB,GAAG,KAAKnE,aAAL,CAAmBkE,WAAnB,CAAzB;;EACA,QAAME,SAAS,GAAG7F,OAAO,CAAC,KAAKlB,SAAN,CAAzB;EAEA,QAAIgH,oBAAJ;EACA,QAAIC,cAAJ;EACA,QAAItB,kBAAJ;;EAEA,QAAI7C,SAAS,KAAKtF,cAAlB,EAAkC;EAChCwJ,MAAAA,oBAAoB,GAAGnI,eAAvB;EACAoI,MAAAA,cAAc,GAAGnI,eAAjB;EACA6G,MAAAA,kBAAkB,GAAGjI,cAArB;EACD,KAJD,MAIO;EACLsJ,MAAAA,oBAAoB,GAAGpI,gBAAvB;EACAqI,MAAAA,cAAc,GAAGlI,eAAjB;EACA4G,MAAAA,kBAAkB,GAAGhI,eAArB;EACD;;EAED,QAAIkJ,WAAW,IAAInK,qBAAC,CAACmK,WAAD,CAAD,CAAeK,QAAf,CAAwBxI,iBAAxB,CAAnB,EAA+D;EAC7D,WAAKyB,UAAL,GAAkB,KAAlB;EACA;EACD;;EAED,QAAM2F,UAAU,GAAG,KAAKL,kBAAL,CAAwBoB,WAAxB,EAAqClB,kBAArC,CAAnB;;EACA,QAAIG,UAAU,CAACqB,kBAAX,EAAJ,EAAqC;EACnC;EACD;;EAED,QAAI,CAACjC,aAAD,IAAkB,CAAC2B,WAAvB,EAAoC;EAClC;EACA;EACD;;EAED,SAAK1G,UAAL,GAAkB,IAAlB;;EAEA,QAAI4G,SAAJ,EAAe;EACb,WAAK3J,KAAL;EACD;;EAED,SAAK8I,0BAAL,CAAgCW,WAAhC;;EACA,SAAK5G,cAAL,GAAsB4G,WAAtB;EAEA,QAAMO,SAAS,GAAG1K,qBAAC,CAACqJ,KAAF,CAAQlI,UAAR,EAAoB;EACpC6H,MAAAA,aAAa,EAAEmB,WADqB;EAEpC/D,MAAAA,SAAS,EAAE6C,kBAFyB;EAGpCK,MAAAA,IAAI,EAAEY,kBAH8B;EAIpCpE,MAAAA,EAAE,EAAEsE;EAJgC,KAApB,CAAlB;;EAOA,QAAIpK,qBAAC,CAAC,KAAK+D,QAAN,CAAD,CAAiByG,QAAjB,CAA0BvI,gBAA1B,CAAJ,EAAiD;EAC/CjC,MAAAA,qBAAC,CAACmK,WAAD,CAAD,CAAeN,QAAf,CAAwBU,cAAxB;EAEAjF,MAAAA,wBAAI,CAACqF,MAAL,CAAYR,WAAZ;EAEAnK,MAAAA,qBAAC,CAACwI,aAAD,CAAD,CAAiBqB,QAAjB,CAA0BS,oBAA1B;EACAtK,MAAAA,qBAAC,CAACmK,WAAD,CAAD,CAAeN,QAAf,CAAwBS,oBAAxB;EAEA,UAAMM,kBAAkB,GAAGtF,wBAAI,CAACuF,gCAAL,CAAsCrC,aAAtC,CAA3B;EAEAxI,MAAAA,qBAAC,CAACwI,aAAD,CAAD,CACGrC,GADH,CACOb,wBAAI,CAACwF,cADZ,EAC4B,YAAM;EAC9B9K,QAAAA,qBAAC,CAACmK,WAAD,CAAD,CACGT,WADH,CACkBY,oBADlB,SAC0CC,cAD1C,EAEGV,QAFH,CAEY7H,iBAFZ;EAIAhC,QAAAA,qBAAC,CAACwI,aAAD,CAAD,CAAiBkB,WAAjB,CAAgC1H,iBAAhC,SAAqDuI,cAArD,SAAuED,oBAAvE;EAEA,QAAA,MAAI,CAAC7G,UAAL,GAAkB,KAAlB;EAEAgE,QAAAA,UAAU,CAAC;EAAA,iBAAMzH,qBAAC,CAAC,MAAI,CAAC+D,QAAN,CAAD,CAAiBwF,OAAjB,CAAyBmB,SAAzB,CAAN;EAAA,SAAD,EAA4C,CAA5C,CAAV;EACD,OAXH,EAYGK,oBAZH,CAYwBH,kBAZxB;EAaD,KAvBD,MAuBO;EACL5K,MAAAA,qBAAC,CAACwI,aAAD,CAAD,CAAiBkB,WAAjB,CAA6B1H,iBAA7B;EACAhC,MAAAA,qBAAC,CAACmK,WAAD,CAAD,CAAeN,QAAf,CAAwB7H,iBAAxB;EAEA,WAAKyB,UAAL,GAAkB,KAAlB;EACAzD,MAAAA,qBAAC,CAAC,KAAK+D,QAAN,CAAD,CAAiBwF,OAAjB,CAAyBmB,SAAzB;EACD;;EAED,QAAIL,SAAJ,EAAe;EACb,WAAK7E,KAAL;EACD;EACF;;;aAIMwF,mBAAP,0BAAwB5H,MAAxB,EAAgC;EAC9B,WAAO,KAAK6H,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGlL,qBAAC,CAAC,IAAD,CAAD,CAAQkL,IAAR,CAAatL,QAAb,CAAX;;EACA,UAAIiE,OAAO,gBACNvD,OADM,EAENN,qBAAC,CAAC,IAAD,CAAD,CAAQkL,IAAR,EAFM,CAAX;;EAKA,UAAI,OAAO9H,MAAP,KAAkB,QAAtB,EAAgC;EAC9BS,QAAAA,OAAO,gBACFA,OADE,EAEFT,MAFE,CAAP;EAID;;EAED,UAAM+H,MAAM,GAAG,OAAO/H,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsCS,OAAO,CAACpD,KAA7D;;EAEA,UAAI,CAACyK,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIhI,QAAJ,CAAa,IAAb,EAAmBW,OAAnB,CAAP;EACA7D,QAAAA,qBAAC,CAAC,IAAD,CAAD,CAAQkL,IAAR,CAAatL,QAAb,EAAuBsL,IAAvB;EACD;;EAED,UAAI,OAAO9H,MAAP,KAAkB,QAAtB,EAAgC;EAC9B8H,QAAAA,IAAI,CAACpF,EAAL,CAAQ1C,MAAR;EACD,OAFD,MAEO,IAAI,OAAO+H,MAAP,KAAkB,QAAtB,EAAgC;EACrC,YAAI,OAAOD,IAAI,CAACC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIC,SAAJ,wBAAkCD,MAAlC,QAAN;EACD;;EAEDD,QAAAA,IAAI,CAACC,MAAD,CAAJ;EACD,OANM,MAMA,IAAItH,OAAO,CAACtD,QAAR,IAAoBsD,OAAO,CAACwH,IAAhC,EAAsC;EAC3CH,QAAAA,IAAI,CAACxK,KAAL;EACAwK,QAAAA,IAAI,CAAC1F,KAAL;EACD;EACF,KAjCM,CAAP;EAkCD;;aAEM8F,uBAAP,8BAA4BjG,KAA5B,EAAmC;EACjC,QAAMkG,QAAQ,GAAGjG,wBAAI,CAACkG,sBAAL,CAA4B,IAA5B,CAAjB;;EAEA,QAAI,CAACD,QAAL,EAAe;EACb;EACD;;EAED,QAAMvD,MAAM,GAAGhI,qBAAC,CAACuL,QAAD,CAAD,CAAY,CAAZ,CAAf;;EAEA,QAAI,CAACvD,MAAD,IAAW,CAAChI,qBAAC,CAACgI,MAAD,CAAD,CAAUwC,QAAV,CAAmBzI,mBAAnB,CAAhB,EAAyD;EACvD;EACD;;EAED,QAAMqB,MAAM,gBACPpD,qBAAC,CAACgI,MAAD,CAAD,CAAUkD,IAAV,EADO,EAEPlL,qBAAC,CAAC,IAAD,CAAD,CAAQkL,IAAR,EAFO,CAAZ;;EAIA,QAAMO,UAAU,GAAG,KAAKzB,YAAL,CAAkB,eAAlB,CAAnB;;EAEA,QAAIyB,UAAJ,EAAgB;EACdrI,MAAAA,MAAM,CAAC7C,QAAP,GAAkB,KAAlB;EACD;;EAED2C,IAAAA,QAAQ,CAAC8H,gBAAT,CAA0B3C,IAA1B,CAA+BrI,qBAAC,CAACgI,MAAD,CAAhC,EAA0C5E,MAA1C;;EAEA,QAAIqI,UAAJ,EAAgB;EACdzL,MAAAA,qBAAC,CAACgI,MAAD,CAAD,CAAUkD,IAAV,CAAetL,QAAf,EAAyBkG,EAAzB,CAA4B2F,UAA5B;EACD;;EAEDpG,IAAAA,KAAK,CAACuC,cAAN;EACD;;;;0BAldoB;EACnB,aAAOjI,OAAP;EACD;;;0BAEoB;EACnB,aAAOW,OAAP;EACD;;;;;EA+cH;EACA;EACA;EACA;EACA;;;AAEAN,uBAAC,CAACmE,QAAD,CAAD,CAAY0C,EAAZ,CAAe/E,oBAAf,EAAqCe,mBAArC,EAA0DK,QAAQ,CAACoI,oBAAnE;AAEAtL,uBAAC,CAACyE,MAAD,CAAD,CAAUoC,EAAV,CAAahF,mBAAb,EAAkC,YAAM;EACtC,MAAM6J,SAAS,GAAG,GAAGtD,KAAH,CAASC,IAAT,CAAclE,QAAQ,CAACuD,gBAAT,CAA0B5E,kBAA1B,CAAd,CAAlB;;EACA,OAAK,IAAI6I,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGF,SAAS,CAACxF,MAAhC,EAAwCyF,CAAC,GAAGC,GAA5C,EAAiDD,CAAC,EAAlD,EAAsD;EACpD,QAAME,SAAS,GAAG7L,qBAAC,CAAC0L,SAAS,CAACC,CAAD,CAAV,CAAnB;;EACAzI,IAAAA,QAAQ,CAAC8H,gBAAT,CAA0B3C,IAA1B,CAA+BwD,SAA/B,EAA0CA,SAAS,CAACX,IAAV,EAA1C;EACD;EACF,CAND;EAQA;EACA;EACA;EACA;EACA;;AAEAlL,uBAAC,CAACC,EAAF,CAAKP,IAAL,IAAawD,QAAQ,CAAC8H,gBAAtB;AACAhL,uBAAC,CAACC,EAAF,CAAKP,IAAL,EAAWoM,WAAX,GAAyB5I,QAAzB;;AACAlD,uBAAC,CAACC,EAAF,CAAKP,IAAL,EAAWqM,UAAX,GAAwB,YAAM;EAC5B/L,EAAAA,qBAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAOmD,QAAQ,CAAC8H,gBAAhB;EACD,CAHD;;;;;;;;"}