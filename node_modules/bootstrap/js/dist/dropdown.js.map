{"version": 3, "file": "dropdown.js", "sources": ["../src/dropdown.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.0): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst VERSION = '4.6.0'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst ESCAPE_KEYCODE = 27 // KeyboardEvent.which value for Escape (Esc) key\nconst SPACE_KEYCODE = 32 // KeyboardEvent.which value for space key\nconst TAB_KEYCODE = 9 // KeyboardEvent.which value for tab key\nconst ARROW_UP_KEYCODE = 38 // KeyboardEvent.which value for up arrow key\nconst ARROW_DOWN_KEYCODE = 40 // KeyboardEvent.which value for down arrow key\nconst RIGHT_MOUSE_BUTTON_WHICH = 3 // MouseEvent.which value for the right button (assuming a right-handed mouse)\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEYCODE}|${ARROW_DOWN_KEYCODE}|${ESCAPE_KEYCODE}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPRIGHT = 'dropright'\nconst CLASS_NAME_DROPLEFT = 'dropleft'\nconst CLASS_NAME_MENURIGHT = 'dropdown-menu-right'\nconst CLASS_NAME_POSITION_STATIC = 'position-static'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = 'top-start'\nconst PLACEMENT_TOPEND = 'top-end'\nconst PLACEMENT_BOTTOM = 'bottom-start'\nconst PLACEMENT_BOTTOMEND = 'bottom-end'\nconst PLACEMENT_RIGHT = 'right-start'\nconst PLACEMENT_LEFT = 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'scrollParent',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element = element\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || $(this._element).hasClass(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = $(this._menu).hasClass(CLASS_NAME_SHOW)\n\n    Dropdown._clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show(true)\n  }\n\n  show(usePopper = false) {\n    if (this._element.disabled || $(this._element).hasClass(CLASS_NAME_DISABLED) || $(this._menu).hasClass(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n    const showEvent = $.Event(EVENT_SHOW, relatedTarget)\n    const parent = Dropdown._getParentFromElement(this._element)\n\n    $(parent).trigger(showEvent)\n\n    if (showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (!this._inNavbar && usePopper) {\n      /**\n       * Check for Popper dependency\n       * Popper - https://popper.js.org\n       */\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (Util.isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        $(parent).addClass(CLASS_NAME_POSITION_STATIC)\n      }\n\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n        $(parent).closest(SELECTOR_NAVBAR_NAV).length === 0) {\n      $(document.body).children().on('mouseover', null, $.noop)\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    $(this._menu).toggleClass(CLASS_NAME_SHOW)\n    $(parent)\n      .toggleClass(CLASS_NAME_SHOW)\n      .trigger($.Event(EVENT_SHOWN, relatedTarget))\n  }\n\n  hide() {\n    if (this._element.disabled || $(this._element).hasClass(CLASS_NAME_DISABLED) || !$(this._menu).hasClass(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n    const hideEvent = $.Event(EVENT_HIDE, relatedTarget)\n    const parent = Dropdown._getParentFromElement(this._element)\n\n    $(parent).trigger(hideEvent)\n\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    $(this._menu).toggleClass(CLASS_NAME_SHOW)\n    $(parent)\n      .toggleClass(CLASS_NAME_SHOW)\n      .trigger($.Event(EVENT_HIDDEN, relatedTarget))\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    $(this._element).off(EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper !== null) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    $(this._element).on(EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...$(this._element).data(),\n      ...config\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _getMenuElement() {\n    if (!this._menu) {\n      const parent = Dropdown._getParentFromElement(this._element)\n\n      if (parent) {\n        this._menu = parent.querySelector(SELECTOR_MENU)\n      }\n    }\n\n    return this._menu\n  }\n\n  _getPlacement() {\n    const $parentDropdown = $(this._element.parentNode)\n    let placement = PLACEMENT_BOTTOM\n\n    // Handle dropup\n    if ($parentDropdown.hasClass(CLASS_NAME_DROPUP)) {\n      placement = $(this._menu).hasClass(CLASS_NAME_MENURIGHT) ?\n        PLACEMENT_TOPEND :\n        PLACEMENT_TOP\n    } else if ($parentDropdown.hasClass(CLASS_NAME_DROPRIGHT)) {\n      placement = PLACEMENT_RIGHT\n    } else if ($parentDropdown.hasClass(CLASS_NAME_DROPLEFT)) {\n      placement = PLACEMENT_LEFT\n    } else if ($(this._menu).hasClass(CLASS_NAME_MENURIGHT)) {\n      placement = PLACEMENT_BOTTOMEND\n    }\n\n    return placement\n  }\n\n  _detectNavbar() {\n    return $(this._element).closest('.navbar').length > 0\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...(this._config.offset(data.offsets, this._element) || {})\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data) {\n        data = new Dropdown(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static _clearMenus(event) {\n    if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH ||\n      event.type === 'keyup' && event.which !== TAB_KEYCODE)) {\n      return\n    }\n\n    const toggles = [].slice.call(document.querySelectorAll(SELECTOR_DATA_TOGGLE))\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown._getParentFromElement(toggles[i])\n      const context = $(toggles[i]).data(DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!$(parent).hasClass(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && (event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName) || event.type === 'keyup' && event.which === TAB_KEYCODE) &&\n          $.contains(parent, event.target)) {\n        continue\n      }\n\n      const hideEvent = $.Event(EVENT_HIDE, relatedTarget)\n      $(parent).trigger(hideEvent)\n      if (hideEvent.isDefaultPrevented()) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        $(document.body).children().off('mouseover', null, $.noop)\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      $(dropdownMenu).removeClass(CLASS_NAME_SHOW)\n      $(parent)\n        .removeClass(CLASS_NAME_SHOW)\n        .trigger($.Event(EVENT_HIDDEN, relatedTarget))\n    }\n  }\n\n  static _getParentFromElement(element) {\n    let parent\n    const selector = Util.getSelectorFromElement(element)\n\n    if (selector) {\n      parent = document.querySelector(selector)\n    }\n\n    return parent || element.parentNode\n  }\n\n  // eslint-disable-next-line complexity\n  static _dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE &&\n      (event.which !== ARROW_DOWN_KEYCODE && event.which !== ARROW_UP_KEYCODE ||\n        $(event.target).closest(SELECTOR_MENU).length) : !REGEXP_KEYDOWN.test(event.which)) {\n      return\n    }\n\n    if (this.disabled || $(this).hasClass(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown._getParentFromElement(this)\n    const isActive = $(parent).hasClass(CLASS_NAME_SHOW)\n\n    if (!isActive && event.which === ESCAPE_KEYCODE) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (!isActive || (event.which === ESCAPE_KEYCODE || event.which === SPACE_KEYCODE)) {\n      if (event.which === ESCAPE_KEYCODE) {\n        $(parent.querySelector(SELECTOR_DATA_TOGGLE)).trigger('focus')\n      }\n\n      $(this).trigger('click')\n      return\n    }\n\n    const items = [].slice.call(parent.querySelectorAll(SELECTOR_VISIBLE_ITEMS))\n      .filter(item => $(item).is(':visible'))\n\n    if (items.length === 0) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.which === ARROW_UP_KEYCODE && index > 0) { // Up\n      index--\n    }\n\n    if (event.which === ARROW_DOWN_KEYCODE && index < items.length - 1) { // Down\n      index++\n    }\n\n    if (index < 0) {\n      index = 0\n    }\n\n    items[index].focus()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document)\n  .on(EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown._dataApiKeydownHandler)\n  .on(EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown._dataApiKeydownHandler)\n  .on(`${EVENT_CLICK_DATA_API} ${EVENT_KEYUP_DATA_API}`, Dropdown._clearMenus)\n  .on(EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n    event.preventDefault()\n    event.stopPropagation()\n    Dropdown._jQueryInterface.call($(this), 'toggle')\n  })\n  .on(EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => {\n    e.stopPropagation()\n  })\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Dropdown._jQueryInterface\n$.fn[NAME].Constructor = Dropdown\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Dropdown._jQueryInterface\n}\n\nexport default Dropdown\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "ESCAPE_KEYCODE", "SPACE_KEYCODE", "TAB_KEYCODE", "ARROW_UP_KEYCODE", "ARROW_DOWN_KEYCODE", "RIGHT_MOUSE_BUTTON_WHICH", "REGEXP_KEYDOWN", "RegExp", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_CLICK", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_DISABLED", "CLASS_NAME_SHOW", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPRIGHT", "CLASS_NAME_DROPLEFT", "CLASS_NAME_MENURIGHT", "CLASS_NAME_POSITION_STATIC", "SELECTOR_DATA_TOGGLE", "SELECTOR_FORM_CHILD", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "<PERSON><PERSON><PERSON>", "offset", "flip", "boundary", "reference", "display", "popperConfig", "DefaultType", "Dropdown", "element", "config", "_element", "_popper", "_config", "_getConfig", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "_addEventListeners", "toggle", "disabled", "hasClass", "isActive", "_clearMenus", "show", "usePopper", "relatedTarget", "showEvent", "Event", "parent", "_getParentFromElement", "trigger", "isDefaultPrevented", "<PERSON><PERSON>", "TypeError", "referenceElement", "<PERSON><PERSON>", "isElement", "j<PERSON>y", "addClass", "_getPopperConfig", "document", "documentElement", "closest", "length", "body", "children", "on", "noop", "focus", "setAttribute", "toggleClass", "hide", "hideEvent", "destroy", "dispose", "removeData", "off", "update", "scheduleUpdate", "event", "preventDefault", "stopPropagation", "constructor", "data", "typeCheckConfig", "querySelector", "_getPlacement", "$parentDropdown", "parentNode", "placement", "_getOffset", "offsets", "modifiers", "enabled", "preventOverflow", "boundariesElement", "applyStyle", "_jQueryInterface", "each", "which", "type", "toggles", "slice", "call", "querySelectorAll", "i", "len", "context", "clickEvent", "dropdownMenu", "test", "target", "tagName", "contains", "removeClass", "selector", "getSelectorFromElement", "_dataApiKeydownHandler", "items", "filter", "item", "is", "index", "indexOf", "e", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAWA;EACA;EACA;EACA;EACA;;EAEA,IAAMA,IAAI,GAAG,UAAb;EACA,IAAMC,OAAO,GAAG,OAAhB;EACA,IAAMC,QAAQ,GAAG,aAAjB;EACA,IAAMC,SAAS,SAAOD,QAAtB;EACA,IAAME,YAAY,GAAG,WAArB;EACA,IAAMC,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B;EACA,IAAMQ,cAAc,GAAG,EAAvB;;EACA,IAAMC,aAAa,GAAG,EAAtB;;EACA,IAAMC,WAAW,GAAG,CAApB;;EACA,IAAMC,gBAAgB,GAAG,EAAzB;;EACA,IAAMC,kBAAkB,GAAG,EAA3B;;EACA,IAAMC,wBAAwB,GAAG,CAAjC;;EACA,IAAMC,cAAc,GAAG,IAAIC,MAAJ,CAAcJ,gBAAd,SAAkCC,kBAAlC,SAAwDJ,cAAxD,CAAvB;EAEA,IAAMQ,UAAU,YAAUb,SAA1B;EACA,IAAMc,YAAY,cAAYd,SAA9B;EACA,IAAMe,UAAU,YAAUf,SAA1B;EACA,IAAMgB,WAAW,aAAWhB,SAA5B;EACA,IAAMiB,WAAW,aAAWjB,SAA5B;EACA,IAAMkB,oBAAoB,aAAWlB,SAAX,GAAuBC,YAAjD;EACA,IAAMkB,sBAAsB,eAAanB,SAAb,GAAyBC,YAArD;EACA,IAAMmB,oBAAoB,aAAWpB,SAAX,GAAuBC,YAAjD;EAEA,IAAMoB,mBAAmB,GAAG,UAA5B;EACA,IAAMC,eAAe,GAAG,MAAxB;EACA,IAAMC,iBAAiB,GAAG,QAA1B;EACA,IAAMC,oBAAoB,GAAG,WAA7B;EACA,IAAMC,mBAAmB,GAAG,UAA5B;EACA,IAAMC,oBAAoB,GAAG,qBAA7B;EACA,IAAMC,0BAA0B,GAAG,iBAAnC;EAEA,IAAMC,oBAAoB,GAAG,0BAA7B;EACA,IAAMC,mBAAmB,GAAG,gBAA5B;EACA,IAAMC,aAAa,GAAG,gBAAtB;EACA,IAAMC,mBAAmB,GAAG,aAA5B;EACA,IAAMC,sBAAsB,GAAG,6DAA/B;EAEA,IAAMC,aAAa,GAAG,WAAtB;EACA,IAAMC,gBAAgB,GAAG,SAAzB;EACA,IAAMC,gBAAgB,GAAG,cAAzB;EACA,IAAMC,mBAAmB,GAAG,YAA5B;EACA,IAAMC,eAAe,GAAG,aAAxB;EACA,IAAMC,cAAc,GAAG,YAAvB;EAEA,IAAMC,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAE,CADM;EAEdC,EAAAA,IAAI,EAAE,IAFQ;EAGdC,EAAAA,QAAQ,EAAE,cAHI;EAIdC,EAAAA,SAAS,EAAE,QAJG;EAKdC,EAAAA,OAAO,EAAE,SALK;EAMdC,EAAAA,YAAY,EAAE;EANA,CAAhB;EASA,IAAMC,WAAW,GAAG;EAClBN,EAAAA,MAAM,EAAE,0BADU;EAElBC,EAAAA,IAAI,EAAE,SAFY;EAGlBC,EAAAA,QAAQ,EAAE,kBAHQ;EAIlBC,EAAAA,SAAS,EAAE,kBAJO;EAKlBC,EAAAA,OAAO,EAAE,QALS;EAMlBC,EAAAA,YAAY,EAAE;EANI,CAApB;EASA;EACA;EACA;EACA;EACA;;MAEME;EACJ,oBAAYC,OAAZ,EAAqBC,MAArB,EAA6B;EAC3B,SAAKC,QAAL,GAAgBF,OAAhB;EACA,SAAKG,OAAL,GAAe,IAAf;EACA,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgBJ,MAAhB,CAAf;EACA,SAAKK,KAAL,GAAa,KAAKC,eAAL,EAAb;EACA,SAAKC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EAEA,SAAKC,kBAAL;EACD;;;;;EAgBD;WAEAC,SAAA,kBAAS;EACP,QAAI,KAAKT,QAAL,CAAcU,QAAd,IAA0BzD,qBAAC,CAAC,KAAK+C,QAAN,CAAD,CAAiBW,QAAjB,CAA0BxC,mBAA1B,CAA9B,EAA8E;EAC5E;EACD;;EAED,QAAMyC,QAAQ,GAAG3D,qBAAC,CAAC,KAAKmD,KAAN,CAAD,CAAcO,QAAd,CAAuBvC,eAAvB,CAAjB;;EAEAyB,IAAAA,QAAQ,CAACgB,WAAT;;EAEA,QAAID,QAAJ,EAAc;EACZ;EACD;;EAED,SAAKE,IAAL,CAAU,IAAV;EACD;;WAEDA,OAAA,cAAKC,SAAL,EAAwB;EAAA,QAAnBA,SAAmB;EAAnBA,MAAAA,SAAmB,GAAP,KAAO;EAAA;;EACtB,QAAI,KAAKf,QAAL,CAAcU,QAAd,IAA0BzD,qBAAC,CAAC,KAAK+C,QAAN,CAAD,CAAiBW,QAAjB,CAA0BxC,mBAA1B,CAA1B,IAA4ElB,qBAAC,CAAC,KAAKmD,KAAN,CAAD,CAAcO,QAAd,CAAuBvC,eAAvB,CAAhF,EAAyH;EACvH;EACD;;EAED,QAAM4C,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAKhB;EADA,KAAtB;EAGA,QAAMiB,SAAS,GAAGhE,qBAAC,CAACiE,KAAF,CAAQrD,UAAR,EAAoBmD,aAApB,CAAlB;;EACA,QAAMG,MAAM,GAAGtB,QAAQ,CAACuB,qBAAT,CAA+B,KAAKpB,QAApC,CAAf;;EAEA/C,IAAAA,qBAAC,CAACkE,MAAD,CAAD,CAAUE,OAAV,CAAkBJ,SAAlB;;EAEA,QAAIA,SAAS,CAACK,kBAAV,EAAJ,EAAoC;EAClC;EACD,KAfqB;;;EAkBtB,QAAI,CAAC,KAAKhB,SAAN,IAAmBS,SAAvB,EAAkC;EAChC;EACN;EACA;EACA;EACM,UAAI,OAAOQ,0BAAP,KAAkB,WAAtB,EAAmC;EACjC,cAAM,IAAIC,SAAJ,CAAc,+DAAd,CAAN;EACD;;EAED,UAAIC,gBAAgB,GAAG,KAAKzB,QAA5B;;EAEA,UAAI,KAAKE,OAAL,CAAaT,SAAb,KAA2B,QAA/B,EAAyC;EACvCgC,QAAAA,gBAAgB,GAAGN,MAAnB;EACD,OAFD,MAEO,IAAIO,wBAAI,CAACC,SAAL,CAAe,KAAKzB,OAAL,CAAaT,SAA5B,CAAJ,EAA4C;EACjDgC,QAAAA,gBAAgB,GAAG,KAAKvB,OAAL,CAAaT,SAAhC,CADiD;;EAIjD,YAAI,OAAO,KAAKS,OAAL,CAAaT,SAAb,CAAuBmC,MAA9B,KAAyC,WAA7C,EAA0D;EACxDH,UAAAA,gBAAgB,GAAG,KAAKvB,OAAL,CAAaT,SAAb,CAAuB,CAAvB,CAAnB;EACD;EACF,OApB+B;EAuBhC;EACA;;;EACA,UAAI,KAAKS,OAAL,CAAaV,QAAb,KAA0B,cAA9B,EAA8C;EAC5CvC,QAAAA,qBAAC,CAACkE,MAAD,CAAD,CAAUU,QAAV,CAAmBpD,0BAAnB;EACD;;EAED,WAAKwB,OAAL,GAAe,IAAIsB,0BAAJ,CAAWE,gBAAX,EAA6B,KAAKrB,KAAlC,EAAyC,KAAK0B,gBAAL,EAAzC,CAAf;EACD,KAhDqB;EAmDtB;EACA;EACA;;;EACA,QAAI,kBAAkBC,QAAQ,CAACC,eAA3B,IACA/E,qBAAC,CAACkE,MAAD,CAAD,CAAUc,OAAV,CAAkBpD,mBAAlB,EAAuCqD,MAAvC,KAAkD,CADtD,EACyD;EACvDjF,MAAAA,qBAAC,CAAC8E,QAAQ,CAACI,IAAV,CAAD,CAAiBC,QAAjB,GAA4BC,EAA5B,CAA+B,WAA/B,EAA4C,IAA5C,EAAkDpF,qBAAC,CAACqF,IAApD;EACD;;EAED,SAAKtC,QAAL,CAAcuC,KAAd;;EACA,SAAKvC,QAAL,CAAcwC,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;EAEAvF,IAAAA,qBAAC,CAAC,KAAKmD,KAAN,CAAD,CAAcqC,WAAd,CAA0BrE,eAA1B;EACAnB,IAAAA,qBAAC,CAACkE,MAAD,CAAD,CACGsB,WADH,CACerE,eADf,EAEGiD,OAFH,CAEWpE,qBAAC,CAACiE,KAAF,CAAQpD,WAAR,EAAqBkD,aAArB,CAFX;EAGD;;WAED0B,OAAA,gBAAO;EACL,QAAI,KAAK1C,QAAL,CAAcU,QAAd,IAA0BzD,qBAAC,CAAC,KAAK+C,QAAN,CAAD,CAAiBW,QAAjB,CAA0BxC,mBAA1B,CAA1B,IAA4E,CAAClB,qBAAC,CAAC,KAAKmD,KAAN,CAAD,CAAcO,QAAd,CAAuBvC,eAAvB,CAAjF,EAA0H;EACxH;EACD;;EAED,QAAM4C,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAKhB;EADA,KAAtB;EAGA,QAAM2C,SAAS,GAAG1F,qBAAC,CAACiE,KAAF,CAAQvD,UAAR,EAAoBqD,aAApB,CAAlB;;EACA,QAAMG,MAAM,GAAGtB,QAAQ,CAACuB,qBAAT,CAA+B,KAAKpB,QAApC,CAAf;;EAEA/C,IAAAA,qBAAC,CAACkE,MAAD,CAAD,CAAUE,OAAV,CAAkBsB,SAAlB;;EAEA,QAAIA,SAAS,CAACrB,kBAAV,EAAJ,EAAoC;EAClC;EACD;;EAED,QAAI,KAAKrB,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAa2C,OAAb;EACD;;EAED3F,IAAAA,qBAAC,CAAC,KAAKmD,KAAN,CAAD,CAAcqC,WAAd,CAA0BrE,eAA1B;EACAnB,IAAAA,qBAAC,CAACkE,MAAD,CAAD,CACGsB,WADH,CACerE,eADf,EAEGiD,OAFH,CAEWpE,qBAAC,CAACiE,KAAF,CAAQtD,YAAR,EAAsBoD,aAAtB,CAFX;EAGD;;WAED6B,UAAA,mBAAU;EACR5F,IAAAA,qBAAC,CAAC6F,UAAF,CAAa,KAAK9C,QAAlB,EAA4BnD,QAA5B;EACAI,IAAAA,qBAAC,CAAC,KAAK+C,QAAN,CAAD,CAAiB+C,GAAjB,CAAqBjG,SAArB;EACA,SAAKkD,QAAL,GAAgB,IAAhB;EACA,SAAKI,KAAL,GAAa,IAAb;;EACA,QAAI,KAAKH,OAAL,KAAiB,IAArB,EAA2B;EACzB,WAAKA,OAAL,CAAa2C,OAAb;;EACA,WAAK3C,OAAL,GAAe,IAAf;EACD;EACF;;WAED+C,SAAA,kBAAS;EACP,SAAK1C,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EACA,QAAI,KAAKN,OAAL,KAAiB,IAArB,EAA2B;EACzB,WAAKA,OAAL,CAAagD,cAAb;EACD;EACF;;;WAIDzC,qBAAA,8BAAqB;EAAA;;EACnBvD,IAAAA,qBAAC,CAAC,KAAK+C,QAAN,CAAD,CAAiBqC,EAAjB,CAAoBtE,WAApB,EAAiC,UAAAmF,KAAK,EAAI;EACxCA,MAAAA,KAAK,CAACC,cAAN;EACAD,MAAAA,KAAK,CAACE,eAAN;;EACA,MAAA,KAAI,CAAC3C,MAAL;EACD,KAJD;EAKD;;WAEDN,aAAA,oBAAWJ,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACD,KAAKsD,WAAL,CAAiBhE,OADhB,EAEDpC,qBAAC,CAAC,KAAK+C,QAAN,CAAD,CAAiBsD,IAAjB,EAFC,EAGDvD,MAHC,CAAN;EAMA2B,IAAAA,wBAAI,CAAC6B,eAAL,CACE5G,IADF,EAEEoD,MAFF,EAGE,KAAKsD,WAAL,CAAiBzD,WAHnB;EAMA,WAAOG,MAAP;EACD;;WAEDM,kBAAA,2BAAkB;EAChB,QAAI,CAAC,KAAKD,KAAV,EAAiB;EACf,UAAMe,MAAM,GAAGtB,QAAQ,CAACuB,qBAAT,CAA+B,KAAKpB,QAApC,CAAf;;EAEA,UAAImB,MAAJ,EAAY;EACV,aAAKf,KAAL,GAAae,MAAM,CAACqC,aAAP,CAAqB5E,aAArB,CAAb;EACD;EACF;;EAED,WAAO,KAAKwB,KAAZ;EACD;;WAEDqD,gBAAA,yBAAgB;EACd,QAAMC,eAAe,GAAGzG,qBAAC,CAAC,KAAK+C,QAAL,CAAc2D,UAAf,CAAzB;EACA,QAAIC,SAAS,GAAG3E,gBAAhB,CAFc;;EAKd,QAAIyE,eAAe,CAAC/C,QAAhB,CAAyBtC,iBAAzB,CAAJ,EAAiD;EAC/CuF,MAAAA,SAAS,GAAG3G,qBAAC,CAAC,KAAKmD,KAAN,CAAD,CAAcO,QAAd,CAAuBnC,oBAAvB,IACVQ,gBADU,GAEVD,aAFF;EAGD,KAJD,MAIO,IAAI2E,eAAe,CAAC/C,QAAhB,CAAyBrC,oBAAzB,CAAJ,EAAoD;EACzDsF,MAAAA,SAAS,GAAGzE,eAAZ;EACD,KAFM,MAEA,IAAIuE,eAAe,CAAC/C,QAAhB,CAAyBpC,mBAAzB,CAAJ,EAAmD;EACxDqF,MAAAA,SAAS,GAAGxE,cAAZ;EACD,KAFM,MAEA,IAAInC,qBAAC,CAAC,KAAKmD,KAAN,CAAD,CAAcO,QAAd,CAAuBnC,oBAAvB,CAAJ,EAAkD;EACvDoF,MAAAA,SAAS,GAAG1E,mBAAZ;EACD;;EAED,WAAO0E,SAAP;EACD;;WAEDrD,gBAAA,yBAAgB;EACd,WAAOtD,qBAAC,CAAC,KAAK+C,QAAN,CAAD,CAAiBiC,OAAjB,CAAyB,SAAzB,EAAoCC,MAApC,GAA6C,CAApD;EACD;;WAED2B,aAAA,sBAAa;EAAA;;EACX,QAAMvE,MAAM,GAAG,EAAf;;EAEA,QAAI,OAAO,KAAKY,OAAL,CAAaZ,MAApB,KAA+B,UAAnC,EAA+C;EAC7CA,MAAAA,MAAM,CAACpC,EAAP,GAAY,UAAAoG,IAAI,EAAI;EAClBA,QAAAA,IAAI,CAACQ,OAAL,gBACKR,IAAI,CAACQ,OADV,EAEM,MAAI,CAAC5D,OAAL,CAAaZ,MAAb,CAAoBgE,IAAI,CAACQ,OAAzB,EAAkC,MAAI,CAAC9D,QAAvC,KAAoD,EAF1D;EAKA,eAAOsD,IAAP;EACD,OAPD;EAQD,KATD,MASO;EACLhE,MAAAA,MAAM,CAACA,MAAP,GAAgB,KAAKY,OAAL,CAAaZ,MAA7B;EACD;;EAED,WAAOA,MAAP;EACD;;WAEDwC,mBAAA,4BAAmB;EACjB,QAAMnC,YAAY,GAAG;EACnBiE,MAAAA,SAAS,EAAE,KAAKH,aAAL,EADQ;EAEnBM,MAAAA,SAAS,EAAE;EACTzE,QAAAA,MAAM,EAAE,KAAKuE,UAAL,EADC;EAETtE,QAAAA,IAAI,EAAE;EACJyE,UAAAA,OAAO,EAAE,KAAK9D,OAAL,CAAaX;EADlB,SAFG;EAKT0E,QAAAA,eAAe,EAAE;EACfC,UAAAA,iBAAiB,EAAE,KAAKhE,OAAL,CAAaV;EADjB;EALR;EAFQ,KAArB,CADiB;;EAejB,QAAI,KAAKU,OAAL,CAAaR,OAAb,KAAyB,QAA7B,EAAuC;EACrCC,MAAAA,YAAY,CAACoE,SAAb,CAAuBI,UAAvB,GAAoC;EAClCH,QAAAA,OAAO,EAAE;EADyB,OAApC;EAGD;;EAED,wBACKrE,YADL,EAEK,KAAKO,OAAL,CAAaP,YAFlB;EAID;;;aAIMyE,mBAAP,0BAAwBrE,MAAxB,EAAgC;EAC9B,WAAO,KAAKsE,IAAL,CAAU,YAAY;EAC3B,UAAIf,IAAI,GAAGrG,qBAAC,CAAC,IAAD,CAAD,CAAQqG,IAAR,CAAazG,QAAb,CAAX;;EACA,UAAMqD,OAAO,GAAG,OAAOH,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;EAEA,UAAI,CAACuD,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIzD,QAAJ,CAAa,IAAb,EAAmBK,OAAnB,CAAP;EACAjD,QAAAA,qBAAC,CAAC,IAAD,CAAD,CAAQqG,IAAR,CAAazG,QAAb,EAAuByG,IAAvB;EACD;;EAED,UAAI,OAAOvD,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOuD,IAAI,CAACvD,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIyB,SAAJ,wBAAkCzB,MAAlC,QAAN;EACD;;EAEDuD,QAAAA,IAAI,CAACvD,MAAD,CAAJ;EACD;EACF,KAhBM,CAAP;EAiBD;;aAEMc,cAAP,qBAAmBqC,KAAnB,EAA0B;EACxB,QAAIA,KAAK,KAAKA,KAAK,CAACoB,KAAN,KAAgB9G,wBAAhB,IACZ0F,KAAK,CAACqB,IAAN,KAAe,OAAf,IAA0BrB,KAAK,CAACoB,KAAN,KAAgBjH,WADnC,CAAT,EAC0D;EACxD;EACD;;EAED,QAAMmH,OAAO,GAAG,GAAGC,KAAH,CAASC,IAAT,CAAc3C,QAAQ,CAAC4C,gBAAT,CAA0BjG,oBAA1B,CAAd,CAAhB;;EAEA,SAAK,IAAIkG,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGL,OAAO,CAACtC,MAA9B,EAAsC0C,CAAC,GAAGC,GAA1C,EAA+CD,CAAC,EAAhD,EAAoD;EAClD,UAAMzD,MAAM,GAAGtB,QAAQ,CAACuB,qBAAT,CAA+BoD,OAAO,CAACI,CAAD,CAAtC,CAAf;;EACA,UAAME,OAAO,GAAG7H,qBAAC,CAACuH,OAAO,CAACI,CAAD,CAAR,CAAD,CAActB,IAAd,CAAmBzG,QAAnB,CAAhB;EACA,UAAMmE,aAAa,GAAG;EACpBA,QAAAA,aAAa,EAAEwD,OAAO,CAACI,CAAD;EADF,OAAtB;;EAIA,UAAI1B,KAAK,IAAIA,KAAK,CAACqB,IAAN,KAAe,OAA5B,EAAqC;EACnCvD,QAAAA,aAAa,CAAC+D,UAAd,GAA2B7B,KAA3B;EACD;;EAED,UAAI,CAAC4B,OAAL,EAAc;EACZ;EACD;;EAED,UAAME,YAAY,GAAGF,OAAO,CAAC1E,KAA7B;;EACA,UAAI,CAACnD,qBAAC,CAACkE,MAAD,CAAD,CAAUR,QAAV,CAAmBvC,eAAnB,CAAL,EAA0C;EACxC;EACD;;EAED,UAAI8E,KAAK,KAAKA,KAAK,CAACqB,IAAN,KAAe,OAAf,IACV,kBAAkBU,IAAlB,CAAuB/B,KAAK,CAACgC,MAAN,CAAaC,OAApC,CADU,IACsCjC,KAAK,CAACqB,IAAN,KAAe,OAAf,IAA0BrB,KAAK,CAACoB,KAAN,KAAgBjH,WADrF,CAAL,IAEAJ,qBAAC,CAACmI,QAAF,CAAWjE,MAAX,EAAmB+B,KAAK,CAACgC,MAAzB,CAFJ,EAEsC;EACpC;EACD;;EAED,UAAMvC,SAAS,GAAG1F,qBAAC,CAACiE,KAAF,CAAQvD,UAAR,EAAoBqD,aAApB,CAAlB;EACA/D,MAAAA,qBAAC,CAACkE,MAAD,CAAD,CAAUE,OAAV,CAAkBsB,SAAlB;;EACA,UAAIA,SAAS,CAACrB,kBAAV,EAAJ,EAAoC;EAClC;EACD,OA9BiD;EAiClD;;;EACA,UAAI,kBAAkBS,QAAQ,CAACC,eAA/B,EAAgD;EAC9C/E,QAAAA,qBAAC,CAAC8E,QAAQ,CAACI,IAAV,CAAD,CAAiBC,QAAjB,GAA4BW,GAA5B,CAAgC,WAAhC,EAA6C,IAA7C,EAAmD9F,qBAAC,CAACqF,IAArD;EACD;;EAEDkC,MAAAA,OAAO,CAACI,CAAD,CAAP,CAAWpC,YAAX,CAAwB,eAAxB,EAAyC,OAAzC;;EAEA,UAAIsC,OAAO,CAAC7E,OAAZ,EAAqB;EACnB6E,QAAAA,OAAO,CAAC7E,OAAR,CAAgB2C,OAAhB;EACD;;EAED3F,MAAAA,qBAAC,CAAC+H,YAAD,CAAD,CAAgBK,WAAhB,CAA4BjH,eAA5B;EACAnB,MAAAA,qBAAC,CAACkE,MAAD,CAAD,CACGkE,WADH,CACejH,eADf,EAEGiD,OAFH,CAEWpE,qBAAC,CAACiE,KAAF,CAAQtD,YAAR,EAAsBoD,aAAtB,CAFX;EAGD;EACF;;aAEMI,wBAAP,+BAA6BtB,OAA7B,EAAsC;EACpC,QAAIqB,MAAJ;EACA,QAAMmE,QAAQ,GAAG5D,wBAAI,CAAC6D,sBAAL,CAA4BzF,OAA5B,CAAjB;;EAEA,QAAIwF,QAAJ,EAAc;EACZnE,MAAAA,MAAM,GAAGY,QAAQ,CAACyB,aAAT,CAAuB8B,QAAvB,CAAT;EACD;;EAED,WAAOnE,MAAM,IAAIrB,OAAO,CAAC6D,UAAzB;EACD;;;aAGM6B,yBAAP,gCAA8BtC,KAA9B,EAAqC;EACnC;EACA;EACA;EACA;EACA;EACA;EACA;EACA,QAAI,kBAAkB+B,IAAlB,CAAuB/B,KAAK,CAACgC,MAAN,CAAaC,OAApC,IACFjC,KAAK,CAACoB,KAAN,KAAgBlH,aAAhB,IAAiC8F,KAAK,CAACoB,KAAN,KAAgBnH,cAAhB,KAChC+F,KAAK,CAACoB,KAAN,KAAgB/G,kBAAhB,IAAsC2F,KAAK,CAACoB,KAAN,KAAgBhH,gBAAtD,IACCL,qBAAC,CAACiG,KAAK,CAACgC,MAAP,CAAD,CAAgBjD,OAAhB,CAAwBrD,aAAxB,EAAuCsD,MAFR,CAD/B,GAGiD,CAACzE,cAAc,CAACwH,IAAf,CAAoB/B,KAAK,CAACoB,KAA1B,CAHtD,EAGwF;EACtF;EACD;;EAED,QAAI,KAAK5D,QAAL,IAAiBzD,qBAAC,CAAC,IAAD,CAAD,CAAQ0D,QAAR,CAAiBxC,mBAAjB,CAArB,EAA4D;EAC1D;EACD;;EAED,QAAMgD,MAAM,GAAGtB,QAAQ,CAACuB,qBAAT,CAA+B,IAA/B,CAAf;;EACA,QAAMR,QAAQ,GAAG3D,qBAAC,CAACkE,MAAD,CAAD,CAAUR,QAAV,CAAmBvC,eAAnB,CAAjB;;EAEA,QAAI,CAACwC,QAAD,IAAasC,KAAK,CAACoB,KAAN,KAAgBnH,cAAjC,EAAiD;EAC/C;EACD;;EAED+F,IAAAA,KAAK,CAACC,cAAN;EACAD,IAAAA,KAAK,CAACE,eAAN;;EAEA,QAAI,CAACxC,QAAD,IAAcsC,KAAK,CAACoB,KAAN,KAAgBnH,cAAhB,IAAkC+F,KAAK,CAACoB,KAAN,KAAgBlH,aAApE,EAAoF;EAClF,UAAI8F,KAAK,CAACoB,KAAN,KAAgBnH,cAApB,EAAoC;EAClCF,QAAAA,qBAAC,CAACkE,MAAM,CAACqC,aAAP,CAAqB9E,oBAArB,CAAD,CAAD,CAA8C2C,OAA9C,CAAsD,OAAtD;EACD;;EAEDpE,MAAAA,qBAAC,CAAC,IAAD,CAAD,CAAQoE,OAAR,CAAgB,OAAhB;EACA;EACD;;EAED,QAAMoE,KAAK,GAAG,GAAGhB,KAAH,CAASC,IAAT,CAAcvD,MAAM,CAACwD,gBAAP,CAAwB7F,sBAAxB,CAAd,EACX4G,MADW,CACJ,UAAAC,IAAI;EAAA,aAAI1I,qBAAC,CAAC0I,IAAD,CAAD,CAAQC,EAAR,CAAW,UAAX,CAAJ;EAAA,KADA,CAAd;;EAGA,QAAIH,KAAK,CAACvD,MAAN,KAAiB,CAArB,EAAwB;EACtB;EACD;;EAED,QAAI2D,KAAK,GAAGJ,KAAK,CAACK,OAAN,CAAc5C,KAAK,CAACgC,MAApB,CAAZ;;EAEA,QAAIhC,KAAK,CAACoB,KAAN,KAAgBhH,gBAAhB,IAAoCuI,KAAK,GAAG,CAAhD,EAAmD;EAAE;EACnDA,MAAAA,KAAK;EACN;;EAED,QAAI3C,KAAK,CAACoB,KAAN,KAAgB/G,kBAAhB,IAAsCsI,KAAK,GAAGJ,KAAK,CAACvD,MAAN,GAAe,CAAjE,EAAoE;EAAE;EACpE2D,MAAAA,KAAK;EACN;;EAED,QAAIA,KAAK,GAAG,CAAZ,EAAe;EACbA,MAAAA,KAAK,GAAG,CAAR;EACD;;EAEDJ,IAAAA,KAAK,CAACI,KAAD,CAAL,CAAatD,KAAb;EACD;;;;0BApZoB;EACnB,aAAO3F,OAAP;EACD;;;0BAEoB;EACnB,aAAOyC,OAAP;EACD;;;0BAEwB;EACvB,aAAOO,WAAP;EACD;;;;;EA6YH;EACA;EACA;EACA;EACA;;;AAEA3C,uBAAC,CAAC8E,QAAD,CAAD,CACGM,EADH,CACMpE,sBADN,EAC8BS,oBAD9B,EACoDmB,QAAQ,CAAC2F,sBAD7D,EAEGnD,EAFH,CAEMpE,sBAFN,EAE8BW,aAF9B,EAE6CiB,QAAQ,CAAC2F,sBAFtD,EAGGnD,EAHH,CAGSrE,oBAHT,SAGiCE,oBAHjC,EAGyD2B,QAAQ,CAACgB,WAHlE,EAIGwB,EAJH,CAIMrE,oBAJN,EAI4BU,oBAJ5B,EAIkD,UAAUwE,KAAV,EAAiB;EAC/DA,EAAAA,KAAK,CAACC,cAAN;EACAD,EAAAA,KAAK,CAACE,eAAN;;EACAvD,EAAAA,QAAQ,CAACuE,gBAAT,CAA0BM,IAA1B,CAA+BzH,qBAAC,CAAC,IAAD,CAAhC,EAAwC,QAAxC;EACD,CARH,EASGoF,EATH,CASMrE,oBATN,EAS4BW,mBAT5B,EASiD,UAAAoH,CAAC,EAAI;EAClDA,EAAAA,CAAC,CAAC3C,eAAF;EACD,CAXH;EAaA;EACA;EACA;EACA;EACA;;AAEAnG,uBAAC,CAACC,EAAF,CAAKP,IAAL,IAAakD,QAAQ,CAACuE,gBAAtB;AACAnH,uBAAC,CAACC,EAAF,CAAKP,IAAL,EAAWqJ,WAAX,GAAyBnG,QAAzB;;AACA5C,uBAAC,CAACC,EAAF,CAAKP,IAAL,EAAWsJ,UAAX,GAAwB,YAAM;EAC5BhJ,EAAAA,qBAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAO6C,QAAQ,CAACuE,gBAAhB;EACD,CAHD;;;;;;;;"}