{"version": 3, "file": "collapse.js", "sources": ["../src/collapse.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.0): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst VERSION = '4.6.0'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst DIMENSION_WIDTH = 'width'\nconst DIMENSION_HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse {\n  constructor(element, config) {\n    this._isTransitioning = false\n    this._element = element\n    this._config = this._getConfig(config)\n    this._triggerArray = [].slice.call(document.querySelectorAll(\n      `[data-toggle=\"collapse\"][href=\"#${element.id}\"],` +\n      `[data-toggle=\"collapse\"][data-target=\"#${element.id}\"]`\n    ))\n\n    const toggleList = [].slice.call(document.querySelectorAll(SELECTOR_DATA_TOGGLE))\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = Util.getSelectorFromElement(elem)\n      const filterElement = [].slice.call(document.querySelectorAll(selector))\n        .filter(foundElem => foundElem === element)\n\n      if (selector !== null && filterElement.length > 0) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle() {\n    if ($(this._element).hasClass(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning ||\n      $(this._element).hasClass(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = [].slice.call(this._parent.querySelectorAll(SELECTOR_ACTIVES))\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    if (actives) {\n      activesData = $(actives).not(this._selector).data(DATA_KEY)\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = $.Event(EVENT_SHOW)\n    $(this._element).trigger(startEvent)\n    if (startEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (actives) {\n      Collapse._jQueryInterface.call($(actives).not(this._selector), 'hide')\n      if (!activesData) {\n        $(actives).data(DATA_KEY, null)\n      }\n    }\n\n    const dimension = this._getDimension()\n\n    $(this._element)\n      .removeClass(CLASS_NAME_COLLAPSE)\n      .addClass(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      $(this._triggerArray)\n        .removeClass(CLASS_NAME_COLLAPSED)\n        .attr('aria-expanded', true)\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      $(this._element)\n        .removeClass(CLASS_NAME_COLLAPSING)\n        .addClass(`${CLASS_NAME_COLLAPSE} ${CLASS_NAME_SHOW}`)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      $(this._element).trigger(EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n    $(this._element)\n      .one(Util.TRANSITION_END, complete)\n      .emulateTransitionEnd(transitionDuration)\n\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning ||\n      !$(this._element).hasClass(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = $.Event(EVENT_HIDE)\n    $(this._element).trigger(startEvent)\n    if (startEvent.isDefaultPrevented()) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    Util.reflow(this._element)\n\n    $(this._element)\n      .addClass(CLASS_NAME_COLLAPSING)\n      .removeClass(`${CLASS_NAME_COLLAPSE} ${CLASS_NAME_SHOW}`)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const selector = Util.getSelectorFromElement(trigger)\n\n        if (selector !== null) {\n          const $elem = $([].slice.call(document.querySelectorAll(selector)))\n          if (!$elem.hasClass(CLASS_NAME_SHOW)) {\n            $(trigger).addClass(CLASS_NAME_COLLAPSED)\n              .attr('aria-expanded', false)\n          }\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      $(this._element)\n        .removeClass(CLASS_NAME_COLLAPSING)\n        .addClass(CLASS_NAME_COLLAPSE)\n        .trigger(EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n    $(this._element)\n      .one(Util.TRANSITION_END, complete)\n      .emulateTransitionEnd(transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._parent = null\n    this._element = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    Util.typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    const hasWidth = $(this._element).hasClass(DIMENSION_WIDTH)\n    return hasWidth ? DIMENSION_WIDTH : DIMENSION_HEIGHT\n  }\n\n  _getParent() {\n    let parent\n\n    if (Util.isElement(this._config.parent)) {\n      parent = this._config.parent\n\n      // It's a jQuery object\n      if (typeof this._config.parent.jquery !== 'undefined') {\n        parent = this._config.parent[0]\n      }\n    } else {\n      parent = document.querySelector(this._config.parent)\n    }\n\n    const selector = `[data-toggle=\"collapse\"][data-parent=\"${this._config.parent}\"]`\n    const children = [].slice.call(parent.querySelectorAll(selector))\n\n    $(children).each((i, element) => {\n      this._addAriaAndCollapsedClass(\n        Collapse._getTargetFromElement(element),\n        [element]\n      )\n    })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    const isOpen = $(element).hasClass(CLASS_NAME_SHOW)\n\n    if (triggerArray.length) {\n      $(triggerArray)\n        .toggleClass(CLASS_NAME_COLLAPSED, !isOpen)\n        .attr('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n\n  static _getTargetFromElement(element) {\n    const selector = Util.getSelectorFromElement(element)\n    return selector ? document.querySelector(selector) : null\n  }\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data = $element.data(DATA_KEY)\n      const _config = {\n        ...Default,\n        ...$element.data(),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n        _config.toggle = false\n      }\n\n      if (!data) {\n        data = new Collapse(this, _config)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document).on(EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.currentTarget.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const $trigger = $(this)\n  const selector = Util.getSelectorFromElement(this)\n  const selectors = [].slice.call(document.querySelectorAll(selector))\n\n  $(selectors).each(function () {\n    const $target = $(this)\n    const data = $target.data(DATA_KEY)\n    const config = data ? 'toggle' : $trigger.data()\n    Collapse._jQueryInterface.call($target, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Collapse._jQueryInterface\n$.fn[NAME].Constructor = Collapse\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Collapse._jQueryInterface\n}\n\nexport default Collapse\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "<PERSON><PERSON><PERSON>", "toggle", "parent", "DefaultType", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_CLICK_DATA_API", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "DIMENSION_WIDTH", "DIMENSION_HEIGHT", "SELECTOR_ACTIVES", "SELECTOR_DATA_TOGGLE", "Collapse", "element", "config", "_isTransitioning", "_element", "_config", "_getConfig", "_triggerArray", "slice", "call", "document", "querySelectorAll", "id", "toggleList", "i", "len", "length", "elem", "selector", "<PERSON><PERSON>", "getSelectorFromElement", "filterElement", "filter", "foundElem", "_selector", "push", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hasClass", "hide", "show", "actives", "activesData", "getAttribute", "classList", "contains", "not", "data", "startEvent", "Event", "trigger", "isDefaultPrevented", "_jQueryInterface", "dimension", "_getDimension", "removeClass", "addClass", "style", "attr", "setTransitioning", "complete", "capitalizedDimension", "toUpperCase", "scrollSize", "transitionDuration", "getTransitionDurationFromElement", "one", "TRANSITION_END", "emulateTransitionEnd", "getBoundingClientRect", "reflow", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "$elem", "isTransitioning", "dispose", "removeData", "Boolean", "typeCheckConfig", "<PERSON><PERSON><PERSON><PERSON>", "isElement", "j<PERSON>y", "querySelector", "children", "each", "_getTargetFromElement", "trigger<PERSON><PERSON>y", "isOpen", "toggleClass", "$element", "test", "TypeError", "on", "event", "currentTarget", "tagName", "preventDefault", "$trigger", "selectors", "$target", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;EACA;EACA;EACA;EACA;;EAEA,IAAMA,IAAI,GAAG,UAAb;EACA,IAAMC,OAAO,GAAG,OAAhB;EACA,IAAMC,QAAQ,GAAG,aAAjB;EACA,IAAMC,SAAS,SAAOD,QAAtB;EACA,IAAME,YAAY,GAAG,WAArB;EACA,IAAMC,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B;EAEA,IAAMQ,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAE,IADM;EAEdC,EAAAA,MAAM,EAAE;EAFM,CAAhB;EAKA,IAAMC,WAAW,GAAG;EAClBF,EAAAA,MAAM,EAAE,SADU;EAElBC,EAAAA,MAAM,EAAE;EAFU,CAApB;EAKA,IAAME,UAAU,YAAUT,SAA1B;EACA,IAAMU,WAAW,aAAWV,SAA5B;EACA,IAAMW,UAAU,YAAUX,SAA1B;EACA,IAAMY,YAAY,cAAYZ,SAA9B;EACA,IAAMa,oBAAoB,aAAWb,SAAX,GAAuBC,YAAjD;EAEA,IAAMa,eAAe,GAAG,MAAxB;EACA,IAAMC,mBAAmB,GAAG,UAA5B;EACA,IAAMC,qBAAqB,GAAG,YAA9B;EACA,IAAMC,oBAAoB,GAAG,WAA7B;EAEA,IAAMC,eAAe,GAAG,OAAxB;EACA,IAAMC,gBAAgB,GAAG,QAAzB;EAEA,IAAMC,gBAAgB,GAAG,oBAAzB;EACA,IAAMC,oBAAoB,GAAG,0BAA7B;EAEA;EACA;EACA;EACA;EACA;;MAEMC;EACJ,oBAAYC,OAAZ,EAAqBC,MAArB,EAA6B;EAC3B,SAAKC,gBAAL,GAAwB,KAAxB;EACA,SAAKC,QAAL,GAAgBH,OAAhB;EACA,SAAKI,OAAL,GAAe,KAAKC,UAAL,CAAgBJ,MAAhB,CAAf;EACA,SAAKK,aAAL,GAAqB,GAAGC,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CACjC,wCAAmCV,OAAO,CAACW,EAA3C,4DAC0CX,OAAO,CAACW,EADlD,SADiC,CAAd,CAArB;EAKA,QAAMC,UAAU,GAAG,GAAGL,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0BZ,oBAA1B,CAAd,CAAnB;;EACA,SAAK,IAAIe,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGF,UAAU,CAACG,MAAjC,EAAyCF,CAAC,GAAGC,GAA7C,EAAkDD,CAAC,EAAnD,EAAuD;EACrD,UAAMG,IAAI,GAAGJ,UAAU,CAACC,CAAD,CAAvB;EACA,UAAMI,QAAQ,GAAGC,wBAAI,CAACC,sBAAL,CAA4BH,IAA5B,CAAjB;EACA,UAAMI,aAAa,GAAG,GAAGb,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0BO,QAA1B,CAAd,EACnBI,MADmB,CACZ,UAAAC,SAAS;EAAA,eAAIA,SAAS,KAAKtB,OAAlB;EAAA,OADG,CAAtB;;EAGA,UAAIiB,QAAQ,KAAK,IAAb,IAAqBG,aAAa,CAACL,MAAd,GAAuB,CAAhD,EAAmD;EACjD,aAAKQ,SAAL,GAAiBN,QAAjB;;EACA,aAAKX,aAAL,CAAmBkB,IAAnB,CAAwBR,IAAxB;EACD;EACF;;EAED,SAAKS,OAAL,GAAe,KAAKrB,OAAL,CAAapB,MAAb,GAAsB,KAAK0C,UAAL,EAAtB,GAA0C,IAAzD;;EAEA,QAAI,CAAC,KAAKtB,OAAL,CAAapB,MAAlB,EAA0B;EACxB,WAAK2C,yBAAL,CAA+B,KAAKxB,QAApC,EAA8C,KAAKG,aAAnD;EACD;;EAED,QAAI,KAAKF,OAAL,CAAarB,MAAjB,EAAyB;EACvB,WAAKA,MAAL;EACD;EACF;;;;;EAYD;WAEAA,SAAA,kBAAS;EACP,QAAIH,qBAAC,CAAC,KAAKuB,QAAN,CAAD,CAAiByB,QAAjB,CAA0BrC,eAA1B,CAAJ,EAAgD;EAC9C,WAAKsC,IAAL;EACD,KAFD,MAEO;EACL,WAAKC,IAAL;EACD;EACF;;WAEDA,OAAA,gBAAO;EAAA;;EACL,QAAI,KAAK5B,gBAAL,IACFtB,qBAAC,CAAC,KAAKuB,QAAN,CAAD,CAAiByB,QAAjB,CAA0BrC,eAA1B,CADF,EAC8C;EAC5C;EACD;;EAED,QAAIwC,OAAJ;EACA,QAAIC,WAAJ;;EAEA,QAAI,KAAKP,OAAT,EAAkB;EAChBM,MAAAA,OAAO,GAAG,GAAGxB,KAAH,CAASC,IAAT,CAAc,KAAKiB,OAAL,CAAaf,gBAAb,CAA8Bb,gBAA9B,CAAd,EACPwB,MADO,CACA,UAAAL,IAAI,EAAI;EACd,YAAI,OAAO,KAAI,CAACZ,OAAL,CAAapB,MAApB,KAA+B,QAAnC,EAA6C;EAC3C,iBAAOgC,IAAI,CAACiB,YAAL,CAAkB,aAAlB,MAAqC,KAAI,CAAC7B,OAAL,CAAapB,MAAzD;EACD;;EAED,eAAOgC,IAAI,CAACkB,SAAL,CAAeC,QAAf,CAAwB3C,mBAAxB,CAAP;EACD,OAPO,CAAV;;EASA,UAAIuC,OAAO,CAAChB,MAAR,KAAmB,CAAvB,EAA0B;EACxBgB,QAAAA,OAAO,GAAG,IAAV;EACD;EACF;;EAED,QAAIA,OAAJ,EAAa;EACXC,MAAAA,WAAW,GAAGpD,qBAAC,CAACmD,OAAD,CAAD,CAAWK,GAAX,CAAe,KAAKb,SAApB,EAA+Bc,IAA/B,CAAoC7D,QAApC,CAAd;;EACA,UAAIwD,WAAW,IAAIA,WAAW,CAAC9B,gBAA/B,EAAiD;EAC/C;EACD;EACF;;EAED,QAAMoC,UAAU,GAAG1D,qBAAC,CAAC2D,KAAF,CAAQrD,UAAR,CAAnB;EACAN,IAAAA,qBAAC,CAAC,KAAKuB,QAAN,CAAD,CAAiBqC,OAAjB,CAAyBF,UAAzB;;EACA,QAAIA,UAAU,CAACG,kBAAX,EAAJ,EAAqC;EACnC;EACD;;EAED,QAAIV,OAAJ,EAAa;EACXhC,MAAAA,QAAQ,CAAC2C,gBAAT,CAA0BlC,IAA1B,CAA+B5B,qBAAC,CAACmD,OAAD,CAAD,CAAWK,GAAX,CAAe,KAAKb,SAApB,CAA/B,EAA+D,MAA/D;;EACA,UAAI,CAACS,WAAL,EAAkB;EAChBpD,QAAAA,qBAAC,CAACmD,OAAD,CAAD,CAAWM,IAAX,CAAgB7D,QAAhB,EAA0B,IAA1B;EACD;EACF;;EAED,QAAMmE,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEAhE,IAAAA,qBAAC,CAAC,KAAKuB,QAAN,CAAD,CACG0C,WADH,CACerD,mBADf,EAEGsD,QAFH,CAEYrD,qBAFZ;EAIA,SAAKU,QAAL,CAAc4C,KAAd,CAAoBJ,SAApB,IAAiC,CAAjC;;EAEA,QAAI,KAAKrC,aAAL,CAAmBS,MAAvB,EAA+B;EAC7BnC,MAAAA,qBAAC,CAAC,KAAK0B,aAAN,CAAD,CACGuC,WADH,CACenD,oBADf,EAEGsD,IAFH,CAEQ,eAFR,EAEyB,IAFzB;EAGD;;EAED,SAAKC,gBAAL,CAAsB,IAAtB;;EAEA,QAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrBtE,MAAAA,qBAAC,CAAC,KAAI,CAACuB,QAAN,CAAD,CACG0C,WADH,CACepD,qBADf,EAEGqD,QAFH,CAEetD,mBAFf,SAEsCD,eAFtC;EAIA,MAAA,KAAI,CAACY,QAAL,CAAc4C,KAAd,CAAoBJ,SAApB,IAAiC,EAAjC;;EAEA,MAAA,KAAI,CAACM,gBAAL,CAAsB,KAAtB;;EAEArE,MAAAA,qBAAC,CAAC,KAAI,CAACuB,QAAN,CAAD,CAAiBqC,OAAjB,CAAyBrD,WAAzB;EACD,KAVD;;EAYA,QAAMgE,oBAAoB,GAAGR,SAAS,CAAC,CAAD,CAAT,CAAaS,WAAb,KAA6BT,SAAS,CAACpC,KAAV,CAAgB,CAAhB,CAA1D;EACA,QAAM8C,UAAU,cAAYF,oBAA5B;EACA,QAAMG,kBAAkB,GAAGpC,wBAAI,CAACqC,gCAAL,CAAsC,KAAKpD,QAA3C,CAA3B;EAEAvB,IAAAA,qBAAC,CAAC,KAAKuB,QAAN,CAAD,CACGqD,GADH,CACOtC,wBAAI,CAACuC,cADZ,EAC4BP,QAD5B,EAEGQ,oBAFH,CAEwBJ,kBAFxB;EAIA,SAAKnD,QAAL,CAAc4C,KAAd,CAAoBJ,SAApB,IAAoC,KAAKxC,QAAL,CAAckD,UAAd,CAApC;EACD;;WAEDxB,OAAA,gBAAO;EAAA;;EACL,QAAI,KAAK3B,gBAAL,IACF,CAACtB,qBAAC,CAAC,KAAKuB,QAAN,CAAD,CAAiByB,QAAjB,CAA0BrC,eAA1B,CADH,EAC+C;EAC7C;EACD;;EAED,QAAM+C,UAAU,GAAG1D,qBAAC,CAAC2D,KAAF,CAAQnD,UAAR,CAAnB;EACAR,IAAAA,qBAAC,CAAC,KAAKuB,QAAN,CAAD,CAAiBqC,OAAjB,CAAyBF,UAAzB;;EACA,QAAIA,UAAU,CAACG,kBAAX,EAAJ,EAAqC;EACnC;EACD;;EAED,QAAME,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEA,SAAKzC,QAAL,CAAc4C,KAAd,CAAoBJ,SAApB,IAAoC,KAAKxC,QAAL,CAAcwD,qBAAd,GAAsChB,SAAtC,CAApC;EAEAzB,IAAAA,wBAAI,CAAC0C,MAAL,CAAY,KAAKzD,QAAjB;EAEAvB,IAAAA,qBAAC,CAAC,KAAKuB,QAAN,CAAD,CACG2C,QADH,CACYrD,qBADZ,EAEGoD,WAFH,CAEkBrD,mBAFlB,SAEyCD,eAFzC;EAIA,QAAMsE,kBAAkB,GAAG,KAAKvD,aAAL,CAAmBS,MAA9C;;EACA,QAAI8C,kBAAkB,GAAG,CAAzB,EAA4B;EAC1B,WAAK,IAAIhD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgD,kBAApB,EAAwChD,CAAC,EAAzC,EAA6C;EAC3C,YAAM2B,OAAO,GAAG,KAAKlC,aAAL,CAAmBO,CAAnB,CAAhB;EACA,YAAMI,QAAQ,GAAGC,wBAAI,CAACC,sBAAL,CAA4BqB,OAA5B,CAAjB;;EAEA,YAAIvB,QAAQ,KAAK,IAAjB,EAAuB;EACrB,cAAM6C,KAAK,GAAGlF,qBAAC,CAAC,GAAG2B,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0BO,QAA1B,CAAd,CAAD,CAAf;;EACA,cAAI,CAAC6C,KAAK,CAAClC,QAAN,CAAerC,eAAf,CAAL,EAAsC;EACpCX,YAAAA,qBAAC,CAAC4D,OAAD,CAAD,CAAWM,QAAX,CAAoBpD,oBAApB,EACGsD,IADH,CACQ,eADR,EACyB,KADzB;EAED;EACF;EACF;EACF;;EAED,SAAKC,gBAAL,CAAsB,IAAtB;;EAEA,QAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,MAAI,CAACD,gBAAL,CAAsB,KAAtB;;EACArE,MAAAA,qBAAC,CAAC,MAAI,CAACuB,QAAN,CAAD,CACG0C,WADH,CACepD,qBADf,EAEGqD,QAFH,CAEYtD,mBAFZ,EAGGgD,OAHH,CAGWnD,YAHX;EAID,KAND;;EAQA,SAAKc,QAAL,CAAc4C,KAAd,CAAoBJ,SAApB,IAAiC,EAAjC;EACA,QAAMW,kBAAkB,GAAGpC,wBAAI,CAACqC,gCAAL,CAAsC,KAAKpD,QAA3C,CAA3B;EAEAvB,IAAAA,qBAAC,CAAC,KAAKuB,QAAN,CAAD,CACGqD,GADH,CACOtC,wBAAI,CAACuC,cADZ,EAC4BP,QAD5B,EAEGQ,oBAFH,CAEwBJ,kBAFxB;EAGD;;WAEDL,mBAAA,0BAAiBc,eAAjB,EAAkC;EAChC,SAAK7D,gBAAL,GAAwB6D,eAAxB;EACD;;WAEDC,UAAA,mBAAU;EACRpF,IAAAA,qBAAC,CAACqF,UAAF,CAAa,KAAK9D,QAAlB,EAA4B3B,QAA5B;EAEA,SAAK4B,OAAL,GAAe,IAAf;EACA,SAAKqB,OAAL,GAAe,IAAf;EACA,SAAKtB,QAAL,GAAgB,IAAhB;EACA,SAAKG,aAAL,GAAqB,IAArB;EACA,SAAKJ,gBAAL,GAAwB,IAAxB;EACD;;;WAIDG,aAAA,oBAAWJ,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACDnB,OADC,EAEDmB,MAFC,CAAN;EAIAA,IAAAA,MAAM,CAAClB,MAAP,GAAgBmF,OAAO,CAACjE,MAAM,CAAClB,MAAR,CAAvB,CALiB;;EAMjBmC,IAAAA,wBAAI,CAACiD,eAAL,CAAqB7F,IAArB,EAA2B2B,MAA3B,EAAmChB,WAAnC;EACA,WAAOgB,MAAP;EACD;;WAED2C,gBAAA,yBAAgB;EACd,QAAMwB,QAAQ,GAAGxF,qBAAC,CAAC,KAAKuB,QAAN,CAAD,CAAiByB,QAAjB,CAA0BjC,eAA1B,CAAjB;EACA,WAAOyE,QAAQ,GAAGzE,eAAH,GAAqBC,gBAApC;EACD;;WAED8B,aAAA,sBAAa;EAAA;;EACX,QAAI1C,MAAJ;;EAEA,QAAIkC,wBAAI,CAACmD,SAAL,CAAe,KAAKjE,OAAL,CAAapB,MAA5B,CAAJ,EAAyC;EACvCA,MAAAA,MAAM,GAAG,KAAKoB,OAAL,CAAapB,MAAtB,CADuC;;EAIvC,UAAI,OAAO,KAAKoB,OAAL,CAAapB,MAAb,CAAoBsF,MAA3B,KAAsC,WAA1C,EAAuD;EACrDtF,QAAAA,MAAM,GAAG,KAAKoB,OAAL,CAAapB,MAAb,CAAoB,CAApB,CAAT;EACD;EACF,KAPD,MAOO;EACLA,MAAAA,MAAM,GAAGyB,QAAQ,CAAC8D,aAAT,CAAuB,KAAKnE,OAAL,CAAapB,MAApC,CAAT;EACD;;EAED,QAAMiC,QAAQ,iDAA4C,KAAKb,OAAL,CAAapB,MAAzD,QAAd;EACA,QAAMwF,QAAQ,GAAG,GAAGjE,KAAH,CAASC,IAAT,CAAcxB,MAAM,CAAC0B,gBAAP,CAAwBO,QAAxB,CAAd,CAAjB;EAEArC,IAAAA,qBAAC,CAAC4F,QAAD,CAAD,CAAYC,IAAZ,CAAiB,UAAC5D,CAAD,EAAIb,OAAJ,EAAgB;EAC/B,MAAA,MAAI,CAAC2B,yBAAL,CACE5B,QAAQ,CAAC2E,qBAAT,CAA+B1E,OAA/B,CADF,EAEE,CAACA,OAAD,CAFF;EAID,KALD;EAOA,WAAOhB,MAAP;EACD;;WAED2C,4BAAA,mCAA0B3B,OAA1B,EAAmC2E,YAAnC,EAAiD;EAC/C,QAAMC,MAAM,GAAGhG,qBAAC,CAACoB,OAAD,CAAD,CAAW4B,QAAX,CAAoBrC,eAApB,CAAf;;EAEA,QAAIoF,YAAY,CAAC5D,MAAjB,EAAyB;EACvBnC,MAAAA,qBAAC,CAAC+F,YAAD,CAAD,CACGE,WADH,CACenF,oBADf,EACqC,CAACkF,MADtC,EAEG5B,IAFH,CAEQ,eAFR,EAEyB4B,MAFzB;EAGD;EACF;;;aAIMF,wBAAP,+BAA6B1E,OAA7B,EAAsC;EACpC,QAAMiB,QAAQ,GAAGC,wBAAI,CAACC,sBAAL,CAA4BnB,OAA5B,CAAjB;EACA,WAAOiB,QAAQ,GAAGR,QAAQ,CAAC8D,aAAT,CAAuBtD,QAAvB,CAAH,GAAsC,IAArD;EACD;;aAEMyB,mBAAP,0BAAwBzC,MAAxB,EAAgC;EAC9B,WAAO,KAAKwE,IAAL,CAAU,YAAY;EAC3B,UAAMK,QAAQ,GAAGlG,qBAAC,CAAC,IAAD,CAAlB;EACA,UAAIyD,IAAI,GAAGyC,QAAQ,CAACzC,IAAT,CAAc7D,QAAd,CAAX;;EACA,UAAM4B,OAAO,gBACRtB,OADQ,EAERgG,QAAQ,CAACzC,IAAT,EAFQ,EAGP,OAAOpC,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHzC,CAAb;;EAMA,UAAI,CAACoC,IAAD,IAASjC,OAAO,CAACrB,MAAjB,IAA2B,OAAOkB,MAAP,KAAkB,QAA7C,IAAyD,YAAY8E,IAAZ,CAAiB9E,MAAjB,CAA7D,EAAuF;EACrFG,QAAAA,OAAO,CAACrB,MAAR,GAAiB,KAAjB;EACD;;EAED,UAAI,CAACsD,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAItC,QAAJ,CAAa,IAAb,EAAmBK,OAAnB,CAAP;EACA0E,QAAAA,QAAQ,CAACzC,IAAT,CAAc7D,QAAd,EAAwB6D,IAAxB;EACD;;EAED,UAAI,OAAOpC,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOoC,IAAI,CAACpC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAI+E,SAAJ,wBAAkC/E,MAAlC,QAAN;EACD;;EAEDoC,QAAAA,IAAI,CAACpC,MAAD,CAAJ;EACD;EACF,KAzBM,CAAP;EA0BD;;;;0BAnQoB;EACnB,aAAO1B,OAAP;EACD;;;0BAEoB;EACnB,aAAOO,OAAP;EACD;;;;;EAgQH;EACA;EACA;EACA;EACA;;;AAEAF,uBAAC,CAAC6B,QAAD,CAAD,CAAYwE,EAAZ,CAAe3F,oBAAf,EAAqCQ,oBAArC,EAA2D,UAAUoF,KAAV,EAAiB;EAC1E;EACA,MAAIA,KAAK,CAACC,aAAN,CAAoBC,OAApB,KAAgC,GAApC,EAAyC;EACvCF,IAAAA,KAAK,CAACG,cAAN;EACD;;EAED,MAAMC,QAAQ,GAAG1G,qBAAC,CAAC,IAAD,CAAlB;EACA,MAAMqC,QAAQ,GAAGC,wBAAI,CAACC,sBAAL,CAA4B,IAA5B,CAAjB;EACA,MAAMoE,SAAS,GAAG,GAAGhF,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0BO,QAA1B,CAAd,CAAlB;EAEArC,EAAAA,qBAAC,CAAC2G,SAAD,CAAD,CAAad,IAAb,CAAkB,YAAY;EAC5B,QAAMe,OAAO,GAAG5G,qBAAC,CAAC,IAAD,CAAjB;EACA,QAAMyD,IAAI,GAAGmD,OAAO,CAACnD,IAAR,CAAa7D,QAAb,CAAb;EACA,QAAMyB,MAAM,GAAGoC,IAAI,GAAG,QAAH,GAAciD,QAAQ,CAACjD,IAAT,EAAjC;;EACAtC,IAAAA,QAAQ,CAAC2C,gBAAT,CAA0BlC,IAA1B,CAA+BgF,OAA/B,EAAwCvF,MAAxC;EACD,GALD;EAMD,CAhBD;EAkBA;EACA;EACA;EACA;EACA;;AAEArB,uBAAC,CAACC,EAAF,CAAKP,IAAL,IAAayB,QAAQ,CAAC2C,gBAAtB;AACA9D,uBAAC,CAACC,EAAF,CAAKP,IAAL,EAAWmH,WAAX,GAAyB1F,QAAzB;;AACAnB,uBAAC,CAACC,EAAF,CAAKP,IAAL,EAAWoH,UAAX,GAAwB,YAAM;EAC5B9G,EAAAA,qBAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAOoB,QAAQ,CAAC2C,gBAAhB;EACD,CAHD;;;;;;;;"}