{"version": 3, "file": "toast.js", "sources": ["../src/toast.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.0): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst VERSION = '4.6.0'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 500\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  show() {\n    const showEvent = $.Event(EVENT_SHOW)\n\n    $(this._element).trigger(showEvent)\n    if (showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      $(this._element).trigger(EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    Util.reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = $.Event(EVENT_HIDE)\n\n    $(this._element).trigger(hideEvent)\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._close()\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    $(this._element).off(EVENT_CLICK_DISMISS)\n\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...$(this._element).data(),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _setListeners() {\n    $(this._element).on(EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _close() {\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      $(this._element).trigger(EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data = $element.data(DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Toast._jQueryInterface\n$.fn[NAME].Constructor = Toast\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Toast._jQueryInterface\n}\n\nexport default Toast\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "EVENT_CLICK_DISMISS", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "CLASS_NAME_FADE", "CLASS_NAME_HIDE", "CLASS_NAME_SHOW", "CLASS_NAME_SHOWING", "DefaultType", "animation", "autohide", "delay", "<PERSON><PERSON><PERSON>", "SELECTOR_DATA_DISMISS", "Toast", "element", "config", "_element", "_config", "_getConfig", "_timeout", "_setListeners", "show", "showEvent", "Event", "trigger", "isDefaultPrevented", "_clearTimeout", "classList", "add", "complete", "remove", "setTimeout", "hide", "<PERSON><PERSON>", "reflow", "transitionDuration", "getTransitionDurationFromElement", "one", "TRANSITION_END", "emulateTransitionEnd", "contains", "hideEvent", "_close", "dispose", "off", "removeData", "data", "typeCheckConfig", "constructor", "on", "clearTimeout", "_jQueryInterface", "each", "$element", "TypeError", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;EACA;EACA;EACA;EACA;;EAEA,IAAMA,IAAI,GAAG,OAAb;EACA,IAAMC,OAAO,GAAG,OAAhB;EACA,IAAMC,QAAQ,GAAG,UAAjB;EACA,IAAMC,SAAS,SAAOD,QAAtB;EACA,IAAME,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKN,IAAL,CAA3B;EAEA,IAAMO,mBAAmB,qBAAmBJ,SAA5C;EACA,IAAMK,UAAU,YAAUL,SAA1B;EACA,IAAMM,YAAY,cAAYN,SAA9B;EACA,IAAMO,UAAU,YAAUP,SAA1B;EACA,IAAMQ,WAAW,aAAWR,SAA5B;EAEA,IAAMS,eAAe,GAAG,MAAxB;EACA,IAAMC,eAAe,GAAG,MAAxB;EACA,IAAMC,eAAe,GAAG,MAAxB;EACA,IAAMC,kBAAkB,GAAG,SAA3B;EAEA,IAAMC,WAAW,GAAG;EAClBC,EAAAA,SAAS,EAAE,SADO;EAElBC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,KAAK,EAAE;EAHW,CAApB;EAMA,IAAMC,OAAO,GAAG;EACdH,EAAAA,SAAS,EAAE,IADG;EAEdC,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,KAAK,EAAE;EAHO,CAAhB;EAMA,IAAME,qBAAqB,GAAG,wBAA9B;EAEA;EACA;EACA;EACA;EACA;;MAEMC;EACJ,iBAAYC,OAAZ,EAAqBC,MAArB,EAA6B;EAC3B,SAAKC,QAAL,GAAgBF,OAAhB;EACA,SAAKG,OAAL,GAAe,KAAKC,UAAL,CAAgBH,MAAhB,CAAf;EACA,SAAKI,QAAL,GAAgB,IAAhB;;EACA,SAAKC,aAAL;EACD;;;;;EAgBD;WAEAC,OAAA,gBAAO;EAAA;;EACL,QAAMC,SAAS,GAAG1B,qBAAC,CAAC2B,KAAF,CAAQtB,UAAR,CAAlB;EAEAL,IAAAA,qBAAC,CAAC,KAAKoB,QAAN,CAAD,CAAiBQ,OAAjB,CAAyBF,SAAzB;;EACA,QAAIA,SAAS,CAACG,kBAAV,EAAJ,EAAoC;EAClC;EACD;;EAED,SAAKC,aAAL;;EAEA,QAAI,KAAKT,OAAL,CAAaT,SAAjB,EAA4B;EAC1B,WAAKQ,QAAL,CAAcW,SAAd,CAAwBC,GAAxB,CAA4BzB,eAA5B;EACD;;EAED,QAAM0B,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,KAAI,CAACb,QAAL,CAAcW,SAAd,CAAwBG,MAAxB,CAA+BxB,kBAA/B;;EACA,MAAA,KAAI,CAACU,QAAL,CAAcW,SAAd,CAAwBC,GAAxB,CAA4BvB,eAA5B;;EAEAT,MAAAA,qBAAC,CAAC,KAAI,CAACoB,QAAN,CAAD,CAAiBQ,OAAjB,CAAyBtB,WAAzB;;EAEA,UAAI,KAAI,CAACe,OAAL,CAAaR,QAAjB,EAA2B;EACzB,QAAA,KAAI,CAACU,QAAL,GAAgBY,UAAU,CAAC,YAAM;EAC/B,UAAA,KAAI,CAACC,IAAL;EACD,SAFyB,EAEvB,KAAI,CAACf,OAAL,CAAaP,KAFU,CAA1B;EAGD;EACF,KAXD;;EAaA,SAAKM,QAAL,CAAcW,SAAd,CAAwBG,MAAxB,CAA+B1B,eAA/B;;EACA6B,IAAAA,wBAAI,CAACC,MAAL,CAAY,KAAKlB,QAAjB;;EACA,SAAKA,QAAL,CAAcW,SAAd,CAAwBC,GAAxB,CAA4BtB,kBAA5B;;EACA,QAAI,KAAKW,OAAL,CAAaT,SAAjB,EAA4B;EAC1B,UAAM2B,kBAAkB,GAAGF,wBAAI,CAACG,gCAAL,CAAsC,KAAKpB,QAA3C,CAA3B;EAEApB,MAAAA,qBAAC,CAAC,KAAKoB,QAAN,CAAD,CACGqB,GADH,CACOJ,wBAAI,CAACK,cADZ,EAC4BT,QAD5B,EAEGU,oBAFH,CAEwBJ,kBAFxB;EAGD,KAND,MAMO;EACLN,MAAAA,QAAQ;EACT;EACF;;WAEDG,OAAA,gBAAO;EACL,QAAI,CAAC,KAAKhB,QAAL,CAAcW,SAAd,CAAwBa,QAAxB,CAAiCnC,eAAjC,CAAL,EAAwD;EACtD;EACD;;EAED,QAAMoC,SAAS,GAAG7C,qBAAC,CAAC2B,KAAF,CAAQxB,UAAR,CAAlB;EAEAH,IAAAA,qBAAC,CAAC,KAAKoB,QAAN,CAAD,CAAiBQ,OAAjB,CAAyBiB,SAAzB;;EACA,QAAIA,SAAS,CAAChB,kBAAV,EAAJ,EAAoC;EAClC;EACD;;EAED,SAAKiB,MAAL;EACD;;WAEDC,UAAA,mBAAU;EACR,SAAKjB,aAAL;;EAEA,QAAI,KAAKV,QAAL,CAAcW,SAAd,CAAwBa,QAAxB,CAAiCnC,eAAjC,CAAJ,EAAuD;EACrD,WAAKW,QAAL,CAAcW,SAAd,CAAwBG,MAAxB,CAA+BzB,eAA/B;EACD;;EAEDT,IAAAA,qBAAC,CAAC,KAAKoB,QAAN,CAAD,CAAiB4B,GAAjB,CAAqB9C,mBAArB;EAEAF,IAAAA,qBAAC,CAACiD,UAAF,CAAa,KAAK7B,QAAlB,EAA4BvB,QAA5B;EACA,SAAKuB,QAAL,GAAgB,IAAhB;EACA,SAAKC,OAAL,GAAe,IAAf;EACD;;;WAIDC,aAAA,oBAAWH,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACDJ,OADC,EAEDf,qBAAC,CAAC,KAAKoB,QAAN,CAAD,CAAiB8B,IAAjB,EAFC,EAGA,OAAO/B,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHhD,CAAN;EAMAkB,IAAAA,wBAAI,CAACc,eAAL,CACExD,IADF,EAEEwB,MAFF,EAGE,KAAKiC,WAAL,CAAiBzC,WAHnB;EAMA,WAAOQ,MAAP;EACD;;WAEDK,gBAAA,yBAAgB;EAAA;;EACdxB,IAAAA,qBAAC,CAAC,KAAKoB,QAAN,CAAD,CAAiBiC,EAAjB,CAAoBnD,mBAApB,EAAyCc,qBAAzC,EAAgE;EAAA,aAAM,MAAI,CAACoB,IAAL,EAAN;EAAA,KAAhE;EACD;;WAEDU,SAAA,kBAAS;EAAA;;EACP,QAAMb,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,MAAI,CAACb,QAAL,CAAcW,SAAd,CAAwBC,GAAxB,CAA4BxB,eAA5B;;EACAR,MAAAA,qBAAC,CAAC,MAAI,CAACoB,QAAN,CAAD,CAAiBQ,OAAjB,CAAyBxB,YAAzB;EACD,KAHD;;EAKA,SAAKgB,QAAL,CAAcW,SAAd,CAAwBG,MAAxB,CAA+BzB,eAA/B;;EACA,QAAI,KAAKY,OAAL,CAAaT,SAAjB,EAA4B;EAC1B,UAAM2B,kBAAkB,GAAGF,wBAAI,CAACG,gCAAL,CAAsC,KAAKpB,QAA3C,CAA3B;EAEApB,MAAAA,qBAAC,CAAC,KAAKoB,QAAN,CAAD,CACGqB,GADH,CACOJ,wBAAI,CAACK,cADZ,EAC4BT,QAD5B,EAEGU,oBAFH,CAEwBJ,kBAFxB;EAGD,KAND,MAMO;EACLN,MAAAA,QAAQ;EACT;EACF;;WAEDH,gBAAA,yBAAgB;EACdwB,IAAAA,YAAY,CAAC,KAAK/B,QAAN,CAAZ;EACA,SAAKA,QAAL,GAAgB,IAAhB;EACD;;;UAIMgC,mBAAP,0BAAwBpC,MAAxB,EAAgC;EAC9B,WAAO,KAAKqC,IAAL,CAAU,YAAY;EAC3B,UAAMC,QAAQ,GAAGzD,qBAAC,CAAC,IAAD,CAAlB;EACA,UAAIkD,IAAI,GAAGO,QAAQ,CAACP,IAAT,CAAcrD,QAAd,CAAX;;EACA,UAAMwB,OAAO,GAAG,OAAOF,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAAC+B,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIjC,KAAJ,CAAU,IAAV,EAAgBI,OAAhB,CAAP;EACAoC,QAAAA,QAAQ,CAACP,IAAT,CAAcrD,QAAd,EAAwBqD,IAAxB;EACD;;EAED,UAAI,OAAO/B,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO+B,IAAI,CAAC/B,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIuC,SAAJ,wBAAkCvC,MAAlC,QAAN;EACD;;EAED+B,QAAAA,IAAI,CAAC/B,MAAD,CAAJ,CAAa,IAAb;EACD;EACF,KAjBM,CAAP;EAkBD;;;;0BAtJoB;EACnB,aAAOvB,OAAP;EACD;;;0BAEwB;EACvB,aAAOe,WAAP;EACD;;;0BAEoB;EACnB,aAAOI,OAAP;EACD;;;;;EA+IH;EACA;EACA;EACA;EACA;;;AAEAf,uBAAC,CAACC,EAAF,CAAKN,IAAL,IAAasB,KAAK,CAACsC,gBAAnB;AACAvD,uBAAC,CAACC,EAAF,CAAKN,IAAL,EAAWgE,WAAX,GAAyB1C,KAAzB;;AACAjB,uBAAC,CAACC,EAAF,CAAKN,IAAL,EAAWiE,UAAX,GAAwB,YAAM;EAC5B5D,EAAAA,qBAAC,CAACC,EAAF,CAAKN,IAAL,IAAaI,kBAAb;EACA,SAAOkB,KAAK,CAACsC,gBAAb;EACD,CAHD;;;;;;;;"}