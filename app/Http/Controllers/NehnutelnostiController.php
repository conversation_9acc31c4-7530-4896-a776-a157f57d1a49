<?php

namespace App\Http\Controllers;

use App\Exports\NehnutelnostiExport;
use App\File;
use App\Nehnutelnost;
use App\Organization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class NehnutelnostiController extends Controller
{
    public $rules = [
        'kataster' => 'required',
        'cislo_lv' => 'required'
    ];

    public function __construct()
    {
        $this->authorizeResource(Nehnutelnost::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
//        $najmi = Najom::all();
//        foreach ($najmi as $najom) {
//            $najom->nehnutelnosti()->syncWithoutDetaching($najom->nehnutelnost_id);
//        }
//
//        $najmi = Najom::whereNotNull('evidovane_katastrom')->get();
//        foreach($najmi as $najom) {
//            $najom->notes .= "\nEvidovane katastrom: " . $najom->evidovane_katastrom;
//            $najom->evidovane_katastrom = 1222;
//            $najom->save();
//        }
//
//        $najmi = Najom::whereNotNull('kto_podava_dp_a_plati_dan')->get();
//        foreach($najmi as $najom) {
//            $najom->notes .= "\nKto podava DP a plati dan: " . $najom->kto_podava_dp_a_plati_dan;
//            $najom->kto_podava_dp_a_plati_dan = null;
//            $najom->save();
//        }

        $key = 'nehnutelnosti_list';
        $filter = $this->getFilter($key);
        $order = $this->getOrder($key);

        Session::put('NEHNUTELNOST_LAST_PARENT', 'list');
        $nehnutelnosti = Nehnutelnost::order($order)->filter($filter['data'])->paginate(200);
        return view('nehnutelnosti.list', compact('nehnutelnosti', 'key', 'filter', 'order'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $nehnutelnost = new Nehnutelnost();
        $nehnutelnost->organization_id = Request()->get('organization_id');
        $form = [
            'name' => 'Pridať nehnuteľnosť',
            'url' => route('nehnutelnosti.store'),
            'method' => 'POST',
            'enctype' => 'multipart/form-data'
        ];
        return view('nehnutelnosti.form', ['form' => $form, 'nehnutelnost' => $nehnutelnost]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return Redirect::to(route('nehnutelnosti.create'))->withInput()->withErrors($validator);
        } else {
            $nehnutelnost = new Nehnutelnost();
            $nehnutelnost->fill($request->all());
            $nehnutelnost->save();
            if ($request->has('files')) {
                $files = $request->file('files');
                foreach ($files as $index => $f) {
                    $file = new File(['nehnutelnost_id' => $nehnutelnost->id, 'position' => $index + 1]);
                    $file->upload($f);
                    $file->save();
                }
            }
            $nehnutelnost->folders()->sync($request->get('folders') ?? []);
            Session::flash('message', 'Nehnuteľnosť bola úspešne uložená.');
            return Redirect::to($nehnutelnost->redirect_url());
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Nehnutelnost $nehnutelnost
     * @return \Illuminate\Http\Response
     */
    public function show(Nehnutelnost $nehnutelnost)
    {
        Session::put('NEHNUTELNOST_LAST_PARENT', 'detail');
        Session::put('NAJOM_LAST_PARENT', $nehnutelnost);
        return view('nehnutelnosti.detail', ['nehnutelnost' => $nehnutelnost]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Nehnutelnost $nehnutelnost
     * @return \Illuminate\Http\Response
     */
    public function edit(Nehnutelnost $nehnutelnost)
    {
        $form = [
            'name' => 'Upraviť nehnuteľnosť',
            'url' => route('nehnutelnosti.update', $nehnutelnost->id),
            'method' => 'PUT',
            'enctype' => 'multipart/form-data'
        ];
        return view('nehnutelnosti.form', ['nehnutelnost' => $nehnutelnost, 'form' => $form]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Nehnutelnost $nehnutelnost
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Nehnutelnost $nehnutelnost)
    {
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        } else {
            $nehnutelnost->fill($request->all());
            $nehnutelnost->save();

            $index = 1;
            foreach ($request->get('items_position') ?? [] as $file_id => $p) {
                $file = File::find($file_id);
                if ($file) {
                    $file->position = $index;
                    $file->save();
                    ++$index;
                }
            }

            if ($request->has('files')) {
                $files = $request->file('files');
                foreach ($files as $f) {
                    $file = new File(['nehnutelnost_id' => $nehnutelnost->id]);
                    $file->position = $index;
                    $file->upload($f);
                    $file->save();
                    ++$index;
                }
            }
            $files_to_delete = $request->get('files_to_delete') ?? [];
            foreach ($files_to_delete as $file_id) {
                $file = File::find($file_id);
                if (!empty($file)) {
                    $file->delete();
                }
            }

            $nehnutelnost->folders()->sync($request->get('folders') ?? []);
            Session::flash('message', 'Nehnuteľnosť bola úspešne uložená.');
            return Redirect::to($nehnutelnost->redirect_url());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Nehnutelnost $nehnutelnost
     * @return \Illuminate\Http\Response
     */
    public function destroy(Nehnutelnost $nehnutelnost)
    {
        $nehnutelnost->delete();
        return Redirect::to($nehnutelnost->redirect_url());
    }

    public function find(Request $request)
    {
        $term = trim($request->q);
        $organization = Organization::find(intval($request->organization_id));
        if (empty($term)) {
            return Response::json([]);
        }
        if ($organization) {
            $nehnutelnosti = Nehnutelnost::search($term)->where('organization_id', $organization->id)->limit(10)->get();
        } else {
            $nehnutelnosti = Nehnutelnost::search($term)->limit(10)->get();
        }
        $rows = [];
        foreach ($nehnutelnosti as $nehnutelnost) {
            $rows[] = ['id' => $nehnutelnost->id, 'text' => $nehnutelnost->fullname()];
        }
        return Response::json($rows);
    }

    public function export()
    {
        $filter = request()->get('filter') ?? [];
        $order = request()->get('order') ?? [];
        $nehnutelnosti = Nehnutelnost::filter($filter)->order($order)->get();
        return Excel::download(new NehnutelnostiExport($nehnutelnosti), 'nehnutelnosti-' . date('YmdHis') . '.xlsx');
    }
    
    public function storageViewer($nehnutelnostId, $fileId = null)
    {
        $nehnutelnost = Nehnutelnost::findOrFail($nehnutelnostId);
        if (!empty($fileId)) {
            $file = $nehnutelnost->files()->findOrFail($fileId);
        } else {
            $file = $nehnutelnost->files->first();
        }

        if ($file) {
            $data = [
                'url' => $file->url(),
                //'content_type' => 'application/pdf',
                'content_disposition' => 'filename="'.$file->name.'"'
            ];
            
            return view('viewer.storage-show', $data);
        }
        return 'Žiadny obsah na zobrazenie.';
    }
}
