<?php

namespace App\Http\Controllers;

use App\File;
use App\Official;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class OfficialController extends Controller
{
    protected $rules = ['name' => 'required'];

    public function __construct()
    {
        $this->authorizeResource(Official::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('official.list');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $official = new Official();
        $form = [
            'name' => 'Pridať položku',
            'method' => 'POST',
            'url' => route('officials.store'),
            'enctype' => 'multipart/form-data'
        ];
        return view('official.form', ['official' => $official, 'form' => $form]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        } else {
            $official = new Official();
            $official->fill($request->all());
            $official->save();
            if ($request->has('files')) {
                $files = $request->file('files');
                foreach ($files as $f) {
                    $file = new File(['official_id' => $official->id]);
                    $file->upload($f);
                    $file->save();
                }
            }
            Session::flash('message', 'Dokument bol úspešne uložný.');
            return Redirect::to('officials');
        }
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Official $official
     * @return \Illuminate\Http\Response
     */
    public function show(Official $official)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Official $official
     * @return \Illuminate\Http\Response
     */
    public function edit(Official $official)
    {
        $form = [
            'name' => 'Upraviť položku',
            'method' => 'put',
            'url' => route('officials.update', $official),
            'enctype' => 'multipart/form-data'
        ];
        return view('official.form', ['official' => $official, 'form' => $form]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Official $official
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Official $official)
    {
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        } else {
            $official->fill($request->all());
            $official->save();
            if ($request->has('files')) {
                $files = $request->file('files');
                foreach ($files as $f) {
                    $file = new File(['official_id' => $official->id]);
                    $file->upload($f);
                    $file->save();
                }
            }
            $files_to_delete = $request->get('files_to_delete') ?? [];
            foreach ($files_to_delete as $file_id) {
                $file = File::find($file_id);
                if (!empty($file)) {
                    $file->delete();
                }
            }
            Session::flash('message', 'Dokument bol úspešne uložný.');
            return Redirect::to('officials');
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Official $official
     * @return \Illuminate\Http\Response
     */
    public function destroy(Official $official)
    {
        $official->delete();
        Session::flash('message', 'Dokument bol vymazaný.');
        return Redirect::to('officials');
    }
    
    public function storageViewer($officialId, $fileId = null)
    {
        $official = Official::findOrFail($officialId);
        if (!empty($fileId)) {
            $file = $official->files()->findOrFail($fileId);
        } else {
            $file = $official->files->first();
        }

        if ($file) {
            $data = [
                'url' => $file->url(),
                //'content_type' => 'application/pdf',
                'content_disposition' => 'filename="'.$file->name.'"'
            ];
            
            return view('viewer.storage-show', $data);
        }
        return 'Žiadny obsah na zobrazenie.';
    }
}
