<?php

namespace App\Http\Controllers;

use App\File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;

class FileController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
//        $rows = File::onlyTrashed()->get();
//        foreach ($rows as $file) {
//            $file->delete();
//        }
        $start = microtime(true);
        $rows = File::withTrashed()->where('in_s3', 0)->orderByDesc('id')->limit(50)->get();
        foreach ($rows as $file) {
            $disk = Storage::disk('s3');
            if (is_file($file->get_dir() . '/' . $file->name)) {
                $disk->put(
                    $file->get_dir(true) . '/' . $file->name, fopen($file->get_dir() . '/' . $file->name, 'r+'), 'public'
                );
                if (is_file($file->get_dir() . '/thumb_' . $file->name)) {
                    $disk->put(
                        $file->get_dir(true) . '/thumb_' . $file->name, fopen($file->get_dir() . '/thumb_' . $file->name,'r+'), 'public'
                    );
                }
                $file->in_s3 = 1;
                $file->size = $file->get_size();
                $file->save();
            } else {
                $file->in_s3 = 1;
                $file->save();
                $file->delete(true);
            }
//            echo $file->id . '<br/>';
        }
        $stop = microtime(true);
        echo $stop - $start;
        echo '<script>location.reload();</script>';
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param \App\File $file
     * @return \Illuminate\Http\Response
     */
    public function show(File $file)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\File $file
     * @return \Illuminate\Http\Response
     */
    public function edit(File $file)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\File $file
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, File $file)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\File $file
     * @return \Illuminate\Http\Response
     */
    public function destroy(File $file)
    {
        $file->delete();
        Session::flash('message', 'Súbor bol vymazaný.');
        if ($file->document_id) {
            return Redirect::to(route('documents.show', $file->document_id));
        }
        if ($file->notice_id) {
            return Redirect::to(route('notices.edit', $file->notice_id));
        }
        if ($file->mail_id) {
            return Redirect::to(route('mails.show', $file->mail_id));
        }
    }
}
