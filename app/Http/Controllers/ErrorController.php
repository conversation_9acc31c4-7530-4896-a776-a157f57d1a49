<?php

namespace App\Http\Controllers;

use App\Mail\ExceptionOccured;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class ErrorController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        Mail::to(explode(';',env('ERROR_REPORTING_EMAIL')))->send(new ExceptionOccured('JAVASCRIPT_ERROR: URL: ' . PHP_EOL . $request->url . ' NAVIGATOR: ' . $request->navigator . PHP_EOL . ' MESSAGE: ' . $request->message, Auth::user()));
        return response()->noContent();
    }

}
