<?php

namespace App\Http\Controllers;

use App\Organization;
use App\User;

class SearchController extends Controller
{

    public function index()
    {
        $q = '%' . (str_replace(' ', '%', Request()->get('q'))) . '%';
        $users = User::where('meno', 'like', $q)
            ->orwhere('username', 'like', $q)
            ->orwhere('priezvisko', 'like', $q)
            ->orwhere('reholne_meno', 'like', $q)
            ->orwhere('rodne_priezvisko', 'like', $q)
            ->get();
        $organizations = Organization::where('name', 'like', $q)->get();
        return view('search.index', ['users' => $users, 'organizations' => $organizations]);
    }
}
