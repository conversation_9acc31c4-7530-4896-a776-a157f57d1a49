<?php

namespace App\Http\Controllers;

use App\OrganizationUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class OrganizationUserController extends Controller
{

    private $rules = [
        'user_id' => 'required',
        'organization_id' => 'required',
    ];

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $ou = new OrganizationUser();
        $ou->organization_id = Request()->get('organization_id');
        $ou->user_id = Request()->get('user_id');
        $form = [
            'name' => 'Pridať používateľa k organizácií',
            'url' => route('organization-user.store'),
            'method' => 'POST'
        ];
        return view('organization-user.form', ['form' => $form, 'ou' => $ou]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $ou = new OrganizationUser();
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        } else {
            $ou->fill($request->all());
            $ou->save();
            Session::flash('message', 'Nový používateľ úspešne pridaný na organizáciu.');
            return $ou->redirect();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $ou = OrganizationUser::findOrFail($id);
        $form = [
            'name' => 'Upraviť údaje používateľa v organizácií',
            'url' => route('organization-user.update', $ou),
            'method' => 'PUT'
        ];
        return view('organization-user.form', ['form' => $form, 'ou' => $ou]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $ou = OrganizationUser::findOrFail($id);
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return Redirect::to(url('organization-user/' . $ou->id . '/edit'))->withInput()->withErrors($validator);
        } else {
            $ou->fill($request->all());
            $ou->save();
            Session::flash('message', 'Údaje používateľa v organizácií boli úspešne uložené.');
            return $ou->redirect();
        }

    }


    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $ou = OrganizationUser::findOrFail($id);
        $ou->delete();
        Session::flash('message', 'Údaje používateľa v organizácií boli odstránené.');
        return $ou->redirect();
    }
}
