<?php

namespace App\Http\Controllers;

use App\OrganizationOrganization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use App\Rules\OrganizationChildNotParent;

class OrganizationOrganizationController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $oo = new OrganizationOrganization();
        $oo->parent_id = Request()->get('parent_id');
        $oo->child_id = Request()->get('child_id');
        $form = [
            'name' => 'Pridať organizáciu k organizácií',
            'url' => route('organization-organization.store'),
            'method' => 'POST'
        ];
        return view('organization-organization.form', ['form' => $form, 'oo' => $oo]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $oo = new OrganizationOrganization();
        $validator = Validator::make($request->all(), [
            'parent_id' => 'required',
            'child_id' => ['required', new OrganizationChildNotParent($request->get('parent_id'))]
        ]);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        }
        $oo->fill($request->all());
        $oo->save();
        return Redirect::to(route('organizations.tab', [$oo->parent_id, 'organizations']));
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $oo = OrganizationOrganization::findOrFail($id);
        $form = [
            'name' => 'Upraviť údaje organizácie v organizácií',
            'url' => route('organization-organization.update', $oo),
            'method' => 'PUT'
        ];
        return view('organization-organization.form', ['form' => $form, 'oo' => $oo]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $oo = OrganizationOrganization::findOrFail($id);
        $validator = Validator::make($request->all(), [
            'parent_id' => 'required',
            'child_id' => ['required', new OrganizationChildNotParent($request->get('parent_id'))]
        ]);
        if ($validator->fails()) {
            return Redirect::to(url('organization-organization/' . $oo->id . '/edit'))->withInput()->withErrors($validator);
        }
        $oo->fill($request->all());
        $oo->save();
        return Redirect::to(route('organizations.tab', [$oo->parent_id, 'organizations']));
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $oo = OrganizationOrganization::findOrFail($id);
        $oo->delete();
        Session::flash('message', 'Údaje organizácie v organizácií boli odstránené.');
        return Redirect::to(route('organizations.tab', [$oo->parent_id, 'organizations']));
    }
}
