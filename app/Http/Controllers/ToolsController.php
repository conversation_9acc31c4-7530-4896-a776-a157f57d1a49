<?php

namespace App\Http\Controllers;

use App\Setting;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ToolsController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (Auth::check() && Auth::user()->sysadmin) {
                return $next($request);
            }
            return abort(403, 'Unauthorized action.');
        });
    }

    /**
     * Handle various tool actions.
     *
     * @param string $action
     * @return \Illuminate\Http\Response
     */
    public function handleAction($action)
    {
        if (method_exists($this, $action)) {
            return $this->{$action}();
        } else {
            abort(404);
        }
    }

    /**
     * Synchronize configuration settings to the database.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateSettings()
    {
        try {
            $configSettings = config('settings');
            $existingSettings = Setting::all()->keyBy('pid');

            $updates = [];
            foreach ($configSettings as $pid => $setting) {
                // Check if setting exists in the database
                if (isset($existingSettings[$pid])) {
                    $existingSetting = $existingSettings[$pid];

                    // Compare current database values with config values
                    if ($existingSetting->label !== ($setting['label'] ?? null) ||
                        $existingSetting->description !== ($setting['description'] ?? null) ||
                        $existingSetting->value !== ($setting['value'] ?? null)
                    ) {
                        // Update the setting in the updates array
                        $updates[] = [
                            'id' => $existingSetting->id,
                            'label' => $setting['label'] ?? null,
                            'description' => $setting['description'] ?? null,
                            'value' => $setting['value'] ?? null,
                            'updated_at' => now(),
                        ];
                    }
                } else {
                    // Create a new setting if it doesn't exist
                    $updates[] = [
                        'pid' => $pid,
                        'label' => $setting['label'] ?? null,
                        'description' => $setting['description'] ?? null,
                        'value' => $setting['value'] ?? null,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
            }

            DB::transaction(function () use ($updates) {
                foreach ($updates as $update) {
                    if (isset($update['id'])) {
                        // Update existing setting
                        Setting::where('id', $update['id'])->update($update);
                    } else {
                        // Create new setting
                        Setting::create($update);
                    }
                }
            });

            return view('tools.message', ['message' => 'Settings have been synchronized with the database.']);
        } 
        catch (\Exception $e) {
            DB::rollBack();

            return view('tools.message', ['message' => 'Failed to synchronize settings. Please try again.']);
        }
    }
}
