<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Setting;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class SettingsController extends Controller
{
    private $rules;

    public function __construct()
    {
        $this->rules = [
            'File/presignedUrlExpiration' => 'required|numeric',
        ];

        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (Auth::check() && Auth::user()->hasRole('administrator')) {
                return $next($request);
            }
            return abort(403, 'Unauthorized action.');
        });
    }

    public function index()
    {
        $settings = Setting::all(['pid', 'label', 'description', 'value'])->keyBy('pid')->toArray();
        return view('settings.index', compact('settings'));
    }

    public function store(Request $request)
    {
        // normalize input names
        $normalizedSettings = [];
        foreach ($request->all() as $pid => $value) {
            $normalizedPid = Str::replace('_', '/', $pid);
            $normalizedSettings[$normalizedPid] = $value;
        }

        // validate settings
        $validator = Validator::make($normalizedSettings, $this->rules);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        }
        
        // update settings
        foreach ($normalizedSettings as $pid => $value) {
            Setting::where('pid', $pid)->update(['value' => $value]);
        }

        return redirect()->route('settings.index')->with('success', 'Nastavenia boli aktualizované');
    }
}
