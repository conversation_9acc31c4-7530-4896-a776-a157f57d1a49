<?php

namespace App\Http\Controllers;

use App\Classifier;
use App\File;
use App\Notice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class NoticeController extends Controller
{
    private $rules = [];

    public function __construct()
    {
        $this->authorizeResource(Notice::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $notice = new Notice();
        $form = [
            'name' => 'Pridať oznam',
            'method' => 'POST',
            'url' => route('notices.store'),
            'enctype' => 'multipart/form-data'
        ];
        return view('notices.form', ['form' => $form, 'notice' => $notice]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return Redirect::to(route('notices.create'))->withInput()->withErrors($validator);
        } else {
            $notice = new Notice();
            $notice->user_id = Auth::user()->id;
            $notice->fill($request->all());
            $notice->save();
            if ($request->has('files')) {
                $files = $request->file('files');
                foreach ($files as $f) {
                    $file = new File(['notice_id' => $notice->id]);
                    $file->upload($f);
                    $file->save();
                }
            }
            Session::flash('message', 'Oznam bol úspešne uložný.');
            return Redirect::to('/');
        }
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Notice $notice
     * @return \Illuminate\Http\Response
     */
    public function show(Notice $notice)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Notice $notice
     * @return \Illuminate\Http\Response
     */
    public function edit(Notice $notice)
    {
        $form = [
            'name' => 'Upraviť oznam',
            'method' => 'PUT',
            'url' => route('notices.update', $notice->id),
            'enctype' => 'multipart/form-data'
        ];
        return view('notices.form', ['form' => $form, 'notice' => $notice]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Notice $notice
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Notice $notice)
    {
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return Redirect::to(route('notices.edit', $notice->id))->withInput()->withErrors($validator);
        } else {
            $notice->fill($request->all());
            $notice->save();
            if ($request->has('files')) {
                $files = $request->file('files');
                foreach ($files as $f) {
                    $file = new File(['notice_id' => $notice->id]);
                    $file->upload($f);
                    $file->save();
                }
            }
            Session::flash('message', 'Oznam bol úspešne uložený.');
            return Redirect::to('/');
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Notice $notice
     * @return \Illuminate\Http\Response
     */
    public function destroy(Notice $notice)
    {
        $notice->delete();
        foreach ($notice->files as $file) {
            $file->delete();
        }
        Session::flash('message', 'Oznam bol vymazaný.');
        return Redirect::to('/');
    }
}
