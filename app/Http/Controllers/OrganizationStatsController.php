<?php

namespace App\Http\Controllers;

use App\Helpers\Utils;
use App\Organization;
use App\OrganizationStat;
use App\OrganizationStatMeta;
use Barryvdh\DomPDF\Facade;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class OrganizationStatsController extends Controller
{

    public function __construct()
    {
        $this->authorizeResource(OrganizationStat::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $years = OrganizationStat::getYears();
        return view('organization-stats.list', ['years' => $years]);
    }

    public function year($year)
    {
        $dekanaty = Utils::getBbdieceza()->childs('DEKANAT')->activeByYear($year)->getChilds(['by' => 'name']);
        return view('organization-stats.year', ['year' => $year, 'dekanaty' => $dekanaty]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $organization_id = Request()->get('organization_id');
        $organization = Organization::findOrFail($organization_id);
        $type_id = $organization->get_stat_type_id();
        $organizationStat = new OrganizationStat(['organization_id' => $organization_id, 'type_id' => $type_id]);
        $form = [
            'name' => 'Štatistika ' . mb_strtoupper($organizationStat->organization->type->name),
            'method' => 'POST',
            'url' => route('organization-stats.store')
        ];
        return view('organization-stats.form-year', ['stat' => $organizationStat, 'form' => $form, 'metas' => []]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $organizationStat = new OrganizationStat();
        $organizationStat->fill(Request()->all());

        Validator::make($request->all(), ['year' => [
            'required',
            'integer',
            'between:1900,2100',
            Rule::unique('organization_stats')->where(function ($query) {
                return $query->where('organization_id', request()->get('organization_id'))->whereNull('deleted_at')->where('year', request()->get('year'));
            }),
        ]], ['unique' => 'Záznam pre tento rok už existuje.'])->validate();

        $organizationStat->save();
        $organizationStat->checkDekanatStats();
        $organizationStat->checkDiecezaStats();

        foreach ((Request()->get('meta') ?? []) as $key => $value) {
            $meta = OrganizationStatMeta::updateOrCreate(
                ['organization_stat_id' => $organizationStat->id, 'meta_key' => $key],
                ['meta_value' => $value]
            );
        }

        return Redirect::to(route('organization-stats.update', $organizationStat));
//        return Redirect::to('organizations/' . $organizationStat->organization->id);
    }

    /**
     * Display the specified resource.
     *
     * @param \App\OrganizationStat $organizationStat
     * @return \Illuminate\Http\Response
     */
    public function show(OrganizationStat $organizationStat)
    {
        // je to len blaf zobrazit rovnake data vo forme koli policies
        $metas = $this->prepareMeta($organizationStat);
        $form = [
            'name' => 'Štatistika ' . mb_strtoupper($organizationStat->organization->type->name),
            'method' => 'PUT',
            'url' => route('organization-stats.update', $organizationStat)
        ];

        return view('organization-stats.form', ['stat' => $organizationStat, 'form' => $form, 'metas' => $metas]);
    }

    private function prepareDekanatMeta($metas, $dekanat, $year)
    {
        if (!isset($metas['meta']['muzsky_sekularny_institut_pocet_clenov'])) {
            $metas['meta']['muzsky_sekularny_institut_pocet_clenov'] = 0;
        }
        if (!isset($metas['meta']['zensky_sekularny_institut_pocet_clenov'])) {
            $metas['meta']['zensky_sekularny_institut_pocet_clenov'] = 0;
        }

        $stats = OrganizationStat::select('id')->whereIn('organization_id', $dekanat->childs('FARNOST')->activeByYear($year)->select('child_id')->get())->where('year', $year)->whereNotNull('closed_at')->get();

        $mts = OrganizationStatMeta::whereIn('organization_stat_id', $stats)->select(['meta_key', DB::raw('sum(meta_value) as meta_sum')])->groupBy('meta_key')->get();

        foreach ($mts as $met) {
            if (is_numeric($met->meta_sum)) {
                if (!isset($metas['meta'][$met->meta_key])) {
                    $metas['meta'][$met->meta_key] = 0;
                }
                $metas['meta'][$met->meta_key] += $met->meta_sum;
                if ($met->meta_key == 'muzsky_sekularny_institut_1_pocet' || $met->meta_key == 'muzsky_sekularny_institut_2_pocet') {
                    $metas['meta']['muzsky_sekularny_institut_pocet_clenov'] += intval($met->meta_sum);
                }
                if ($met->meta_key == 'zensky_sekularny_institut_1_pocet' || $met->meta_key == 'zensky_sekularny_institut_2_pocet') {
                    $metas['meta']['zensky_sekularny_institut_pocet_clenov'] += intval($met->meta_sum);
                }
            }
        }

        return $metas;
    }

    private function prepareMeta($organizationStat)
    {
        $metas = [];
        foreach ($organizationStat->metas as $meta) {
            $metas['meta'][$meta->meta_key] = $meta->meta_value;
        }
        if ($organizationStat->organization->type->tag == 'DEKANAT') {
            $metas = $this->prepareDekanatMeta($metas, $organizationStat->organization, $organizationStat->year);
        }
        if ($organizationStat->organization->type->tag == 'DIECEZA') {
            $m = ['meta' => []];
            if (isset($metas['meta'])) {
                foreach ($metas['meta'] as $key => $value) {
                    if (strstr($key, 'diecezna_stat_')) {
                        $m['meta'][$key] = $value;
                    }
                }
            }
            $metas = $m;
            $dekanaty = $organizationStat->organization->childs('DEKANAT')->activeByYear($organizationStat->year)->get();
            foreach ($dekanaty as $dekanat) {
                $metas = $this->prepareDekanatMeta($metas, $dekanat->child, $organizationStat->year);
            }
        }
        return $metas;
    }

    public function download(OrganizationStat $organizationStat)
    {
        $debug = false;

        if ($debug) {
            return view('organization-stats.pdf', ['stat' => $organizationStat]);
        }

        $pdf = Facade::loadView('organization-stats.pdf', ['stat' => $organizationStat])->setPaper('a4', 'landscape');
        return $pdf->download('statistika-' . $organizationStat->organization->type->tag . '-' . $organizationStat->year . '-' . Str::slug($organizationStat->organization->name) . '.pdf');

    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\OrganizationStat $organizationStat
     * @return \Illuminate\Http\Response
     */
    public function edit(OrganizationStat $organizationStat)
    {
        $metas = $this->prepareMeta($organizationStat);
        $form = [
            'name' => 'Štatistika ' . mb_strtoupper($organizationStat->organization->type->name),
            'method' => 'PUT',
            'url' => route('organization-stats.update', $organizationStat)
        ];
        $viewData = ['stat' => $organizationStat, 'form' => $form, 'metas' => $metas];
        if (view()->exists('organization-stats.form-' . $organizationStat->year)) {
            return view('organization-stats.form-' . $organizationStat->year, $viewData);
        }
        return view('organization-stats.form', $viewData);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\OrganizationStat $organizationStat
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, OrganizationStat $organizationStat)
    {
        $organizationStat->fill($request->all());
        Validator::make($request->all(), ['year' => [
            'required',
            'integer',
            'between:1900,2100',
            Rule::unique('organization_stats')->where(function ($query) use ($organizationStat) {
                return $query->where('id', '!=', $organizationStat->id)->where('organization_id', request()->get('organization_id'))->whereNull('deleted_at')->where('year', request()->get('year'));
            }),
        ]], ['unique' => 'Záznam pre tento rok už existuje.'])->validate();

        $organizationStat->save();
        $organizationStat->checkDekanatStats();
        $organizationStat->checkDiecezaStats();
        foreach (Request()->get('meta') as $key => $value) {
            $meta = OrganizationStatMeta::updateOrCreate(['organization_stat_id' => $organizationStat->id, 'meta_key' => $key]);
            $meta->meta_value = $value;
            $meta->save();
        }
        if (!empty($request->get('close_stats')) || !empty($request->get('close_stats_as_dekanat')) || !empty($request->get('close_stats_as_ordinariat'))) {
            $validator = Validator::make(request()->all(), OrganizationStat::$allRules, OrganizationStat::$allMessages);
            if ($validator->fails()) {
                return redirect()->back()->withInput()->withErrors($validator);
            }
            if (!empty($request->get('close_stats'))) {
                $organizationStat->closed_at = date('Y-m-d H:i:s');
                $organizationStat->closed_by = Auth::user()->id;
            }
            if (!empty($request->get('close_stats_as_dekanat'))) {
                $organizationStat->closed_at_dekanat = date('Y-m-d H:i:s');
                $organizationStat->closed_by_dekanat = Auth::user()->id;
            }
            if (!empty($request->get('close_stats_as_ordinariat'))) {
                $organizationStat->closed_at_ordinariat = date('Y-m-d H:i:s');
                $organizationStat->closed_by_ordinariat = Auth::user()->id;
            }
            $organizationStat->save();
        }
        return Redirect::to('organizations/' . $organizationStat->organization->id);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\OrganizationStat $organizationStat
     * @return \Illuminate\Http\Response
     */
    public function destroy(OrganizationStat $organizationStat)
    {
        $organizationStat->delete();
        return redirect()->to(route('organizations.show', $organizationStat->organization));
    }
}
