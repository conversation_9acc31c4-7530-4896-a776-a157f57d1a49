<?php

namespace App\Http\Controllers;

use App\Classifier;
use App\DonationOrganization;
use App\Organization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class DonationOrganizationController extends Controller
{
    protected $rules = [
        'amount' => 'required',
        'date' => 'nullable|date'
    ];

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $farnosti = $this->generate_farnosti();
        $do = new DonationOrganization();
        $do->organization_id = Request()->get('organization_id');
        $do->donation_id = Request()->get('donation_id');
        $form = [
            'name' => 'Pridať príspevok',
            'method' => 'POST',
            'url' => route('donation-organization.store')
        ];
        return view('donation-organization.form', ['do' => $do, 'form' => $form, 'farnosti' => $farnosti]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        } else {
            $do = new DonationOrganization();
            $do->fill($request->all());
            $do->save();
            Session::flash('message', 'Príspevok bol úspešne uložný.');
            return redirect()->to(route('donations.show', $do->donation->id));
        }
    }

    /**
     * Display the specified resource.
     *
     * @param \App\DonationOrganization $donationOrganization
     * @return \Illuminate\Http\Response
     */
    public function show(DonationOrganization $donationOrganization)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\DonationOrganization $donationOrganization
     * @return \Illuminate\Http\Response
     */
    public function edit(DonationOrganization $donationOrganization)
    {
        $form = [
            'name' => 'Upraviť príspevok',
            'method' => 'PUT',
            'url' => route('donation-organization.update', $donationOrganization)
        ];
        return view('donation-organization.form', ['do' => $donationOrganization, 'form' => $form, 'farnosti' => $this->generate_farnosti()]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\DonationOrganization $donationOrganization
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, DonationOrganization $donationOrganization)
    {
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        } else {
            $donationOrganization->fill($request->all());
            $donationOrganization->save();
            Session::flash('message', 'Príspevok bol úspešne uložený.');
            return redirect()->to(route('donations.show', $donationOrganization->donation));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\DonationOrganization $donationOrganization
     * @return \Illuminate\Http\Response
     */
    public function destroy(DonationOrganization $donationOrganization)
    {
        $donationOrganization->delete();
        return redirect()->to(route('donations.show', $donationOrganization->donation));
    }

    private function generate_farnosti()
    {
        $farnosti = [''];
        $rows = Organization::where('type_id', Classifier::get_by_tag('ORGANIZATION_TYPE', 'DEKANAT')->id)->orderBy('name')->get();
        foreach ($rows as $row) {
            $fows = $row->childs('FARNOST', [], ['by' => 'name'])->get();
            foreach ($fows as $fow) {
                $farnosti[$row->name][$fow->child->id] = $fow->child->name . ' ' . (!empty($fow->child->ico) ? '(IČO: ' . $fow->child->ico . ')' : '');

                $dows = $fow->child->childs('DUCHOVNA_SPRAVA', [], ['by' => 'name'])->get();
                foreach ($dows as $dow) {
                    $farnosti[$row->name][$dow->child->id] = ' &nbsp; &nbsp; &nbsp; ' . $dow->child->name . ' ' . (!empty($dow->child->ico) ? '(IČO: ' . $dow->child->ico . ')' : '');
                }
            }
        }
        return $farnosti;
    }
}
