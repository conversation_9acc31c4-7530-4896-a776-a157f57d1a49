<?php

namespace App\Http\Controllers;

use App\Archive;
use App\Building;
use App\Entity;
use App\Entry;
use App\Folder;
use App\Mail;
use App\Task;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class TaskController extends Controller
{

    public function __construct()
    {
        $this->authorizeResource(Task::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        Session::put('TASK_LAST_PARENT', ['list']);
        return view('tasks.list');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $form = ['name' => 'Pridať úlohu', 'method' => 'post', 'url' => route('tasks.store')];
        $task = new Task();
        if (request()->get('user_id') && ($r = User::find(request()->get('user_id')))) {
            $task->users[] = $r;
        }
        if (request()->get('mail_id') && ($r = Mail::find(request()->get('mail_id')))) {
            $task->mails[] = $r;
            $task->organization_id = $r->organization_id;
            if ($r->vybavovac) {
                $task->users[] = $r->vybavovac;
            }
            foreach ($r->folders as $folder) {
                $task->folders[] = $folder;
            }
            foreach ($r->entities as $entity) {
                $task->entities[] = $entity;
            }

        }
        if (request()->get('folder_id') && ($r = Folder::find(request()->get('folder_id')))) {
            $task->folders[] = $r;
            $task->organization_id = $r->organization_id;
        }
        if (request()->get('archive_id') && ($r = Archive::find(request()->get('archive_id')))) {
            $task->archives[] = $r;
        }
        foreach (\request()->get('entities') ?? [] as $ent) {
            $task->entities[] = Entity::find($ent);
        }
        return view('tasks.form', compact('form', 'task'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), Task::$rules);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        } else {
            $task = new Task();
            $task->organization_id = 998;
            $task->created_by = Auth::user()->id;
            $task->fill($request->all());
            $task->save();
            $task->folders()->sync($request->get('folders'));
            $task->mails()->sync($request->get('mails'));
            $task->users()->sync($request->get('users'));
            $task->entities()->sync(Entity::sanitize_ids($request->entities));
            $task->archives()->sync(Archive::sanitize_ids($request->archives));
            $task->buildings()->sync(Building::sanitize_ids($request->buildings));
            Session::flash('message', 'Úloha bola úspešne uložná.');
            return $task->redirect();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Task $task
     * @return \Illuminate\Http\Response
     */
    public function show(Task $task)
    {
        $filter = $this->getFilter('task_entries_filter', ['task_id' => $task->id]);
        $order = $this->getOrder('task_entries_order');
        Session::put('TASK_LAST_PARENT', ['task', $task]);
        return view('tasks.detail', compact('task', 'filter', 'order'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Task $task
     * @return \Illuminate\Http\Response
     */
    public function edit(Task $task)
    {
        $form = ['name' => 'Upraviť úlohu', 'method' => 'put', 'url' => route('tasks.update', $task)];
        return view('tasks.form', compact('form', 'task'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Task $task
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Task $task)
    {
        $validator = Validator::make($request->all(), Task::$rules);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        } else {
            $task->fill($request->all());
            $task->save();
            $task->folders()->sync($request->get('folders'));
            $task->mails()->sync($request->get('mails'));
            $task->users()->sync($request->get('users'));
            $task->entities()->sync(Entity::sanitize_ids($request->entities));
            $task->archives()->sync(Archive::sanitize_ids($request->archives));
            $task->buildings()->sync(Building::sanitize_ids($request->buildings));
            Session::flash('message', 'Úloha bola úspešne uložná.');
            return $task->redirect();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Task $task
     * @return \Illuminate\Http\Response
     */
    public function destroy(Task $task)
    {
        $task->delete();
        return $task->redirect();
    }

    public function toggleTracker(Task $task)
    {
        $entry = $task->entries()->active(true)->first();
        Entry::stopAll();
        if (!$entry) {
            $entry = new Entry();
            $entry->task_id = $task->id;
            $entry->date_from = date('Y-m-d H:i:s');
            $entry->user_id = Auth::user()->id;
            $entry->save();
            return ['active' => true, 'task' => $task, 'entry' => $entry];
        } else {
            return ['active' => false];
        }
    }

    public function toggleClose(Task $task)
    {
        $this->authorize('toggleClose', $task);
        if ($task->closed_at) {
            $task->closed_at = null;
            $task->closed_by = null;
        } else {
            $task->closed_at = date('Y-m-d H:i:s');
            $task->closed_by = auth()->user()->id;
        }
        $task->save();

        return redirect()->back();
//        return ['closed' => $task->closed_at == null];
    }

    public function find(Request $request)
    {
        $term = trim($request->q);
        if (empty($term)) {
            return Response::json([]);
        }
        $tasks = Task::search($term)->orderby('title')->limit(100)->get();
        $rows = [];
        foreach ($tasks as $task) {
            $rows[] = ['id' => $task->id, 'text' => $task->title];
        }
        return Response::json($rows);
    }
}
