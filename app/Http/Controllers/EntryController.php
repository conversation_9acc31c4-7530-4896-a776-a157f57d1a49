<?php

namespace App\Http\Controllers;

use App\Entry;
use App\Exports\EntriesExport;
use App\Exports\MailsExport;
use App\Organization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class EntryController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(Entry::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $key = 'entries_filter';
        $filter = $this->getFilter($key, ['date_from'=>date('Y-m-01'), 'date_to' => date('Y-m-t')]);
        $order = $this->getOrder('entries_order');
        $entries = Entry::filter($filter['data'])->order($order)->get();
        return view('entries.list', compact('entries', 'filter', 'order', 'key'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $entry = new Entry();
        $entry->task_id = request()->get('task_id');
        $form = ['name' => 'Pridať záznam', 'method' => 'post', 'url' => route('entries.store')];
        return view('entries.form', compact('form', 'entry'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), Entry::$rules);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        } else {
            $entry = new Entry();
            $entry->user_id = Auth::user()->id;
            $entry->fill($request->all());
            $entry->save();
            Session::flash('message', 'Záznam bol úspešne uložný.');
            return redirect()->to(route('tasks.show', $entry->task));
        }
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Entry $entry
     * @return \Illuminate\Http\Response
     */
    public function show(Entry $entry)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Entry $entry
     * @return \Illuminate\Http\Response
     */
    public function edit(Entry $entry)
    {
        $form = ['name' => 'Upraviť záznam', 'method' => 'put', 'url' => route('entries.update', $entry)];
        return view('entries.form', compact('form', 'entry'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Entry $entry
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Entry $entry)
    {
        $validator = Validator::make($request->all(), Entry::$rules);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        } else {
            $entry->fill($request->all());
            $entry->save();
            Session::flash('message', 'Záznam bol úspešne uložný.');
            return redirect()->to(route('tasks.show', $entry->task));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Entry $entry
     * @return \Illuminate\Http\Response
     */
    public function destroy(Entry $entry)
    {
        $entry->delete();
        return $entry->task->redirect();
    }

    public function export()
    {
        $entries = Entry::filter(request()->get('filter') ?? [])->order(request()->get('order') ?? [])->get();
        return Excel::download(new EntriesExport($entries), 'vykaz-prac-' . date('YmdHis') . '.xlsx');
    }
}
