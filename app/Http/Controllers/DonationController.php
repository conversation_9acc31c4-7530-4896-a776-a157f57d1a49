<?php

namespace App\Http\Controllers;

use App\Classifier;
use App\Donation;
use App\DonationOrganization;
use App\Exports\DonationsExport;
use App\Organization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class DonationController extends Controller
{
    protected $rules = [
        'name' => 'required',
        'year' => 'required',
        'vs' => 'required',
        'bank_account' => 'required',
        'date' => 'required'
    ];

    public function __construct()
    {
        $this->authorizeResource(Donation::class);
    }

    private function importZbierky($year)
    {
        $names = [
            'BB.-mesto' => 'Banská Bystrica - mesto',
            'BB - mesto' => 'Banská Bystrica - mesto',
            'BB - Katedrála' => 'Banská Bystrica - Katedrála sv. Františka Xaverského',
            'B.B.-Katedrála' => 'Banská Bystrica - Katedrála sv. Františka Xaverského',
            'BB - Podlavice' => 'Banská Bystrica - Podlavice',
            'Podlavice' => 'Banská Bystrica - Podlavice',
            'BB - Sásová' => 'Banská Bystrica - Sásová',
            'N. Sásová' => 'Banská Bystrica - Sásová',
            'BB - Stará Sásová' => 'Banská Bystrica - Stará Sásová',
            'S. Sásová' => 'Banská Bystrica - Stará Sásová',
            'BB - Fončorda' => 'Banská Bystrica - Fončorda',
            'Fončorda' => 'Banská Bystrica - Fončorda',
            'DS Kalvária' => 'DS Kalvária - Banská Bystrica - Bosí karmelitáni',
            'BB - Radvaň' => 'Banská Bystrica - Radvaň',
            'B.B.-Radvaň' => 'Banská Bystrica - Radvaň',
            'DS Kostola sv. Alžbety' => 'DS Kostola sv. Alžbety - Banská Bystrica - Vincentíni',
            'Misijná spoločnosť' => 'DS Kostola sv. Alžbety - Banská Bystrica - Vincentíni',
            'Belveder' => 'Banská Bystrica - Belveder',
            'Belá' => 'Banská Belá',
            'B. Štiavnica' => 'Banská Štiavnica',
            'Sv. Anton' => 'Svätý Anton',
            'Š. Bane' => 'Štiavnické Bane',
            'Nedožery-Brezany' => 'Nedožery - Brezany',
            'Nedožery-Br.' => 'Nedožery - Brezany',
            'N. Pravno' => 'Nitrianske Pravno',
            'Opat. Nad N.' => 'Opatovce nad Nitrou',
            'Det. Huta' => 'Detvianska Huta',
            'DS Raticov Vrch' => 'DS Kostola  Panny Márie Fatimskej - Raticov Vrch - Kapucíni',
            'Raticov Vrch' => 'DS Kostola  Panny Márie Fatimskej - Raticov Vrch - Kapucíni',
            'Vígľašská Huta-Kalinka' => 'Vígľašská Huta - Kalinka',
            'V. Huta-Kalinka' => 'Vígľašská Huta - Kalinka',
            'Zv. Slatina' => 'Zvolenská Slatina',
            'D. Badín' => 'Dolný Badín',
            'H. Nemce+Rykyn' => 'Hontianske Nemce',
            'Bátovce+Peče' => 'Bátovce',
            'Hont.Trsťany' => 'Hontianske Trsťany',
            'FUČajkov' => 'Čajkov',
            'Hliník n/Hr.' => 'Hliník nad Hronom',
            'Levice-Rybníky' => 'Levice - Rybníky',
            'Partizánske mesto' => 'Partizánske - mesto',
            'Pt-Šimonovany' => 'Partizánske 3 - Šimonovany',
            'DS Piaristi' => 'DS Kostola Najsvätejšej Trojice - Prievidza - Piaristi',
            'Prievidza Zápotôčky' => 'Prievidza - Zapotôčky',
            'Veľká Lehôtka' => 'Prievidza - Veľká Lehôtka',
            'Vyškovce' => 'Vyškovce nad Ipľom',
            'Hor. Hámre' => 'Horné Hámre',
            'Rudno nad Hr.' => 'Rudno nad Hronom',
            'T. Breznica' => 'Tekovská Breznica',
            'Kamenec p. Vtáč.' => 'Kamenec pod Vtáčnikom',
            'M. Kršteňany' => 'Malé Kršteňany',
            'Partiz. - Šípok' => 'Partizánske - Šípok',
            'DS Mnísi z rehole sv.Benedikta' => 'DS Kostola a Kláštora premenenia Pána - Sampor - Benediktíni',
            'Kláštor Sampor' => 'DS Kostola a Kláštora premenenia Pána - Sampor - Benediktíni',
            'Lovčica-Trubín' => 'Lovčica - Trubín',
            'Žiar nad Hronom - PSK' => 'Žiar nad Hronom',
            'Žiar nad Hronom - SPM' => 'Žiar nad Hronom - Farnosť Sedembolestnej Panny Márie',
            'Horná Ves pri Kremnici' => 'Horná Ves - Farnosť bl.Jána Pavla II., pápeža',
            'Horná Ves - Farnosť bl.Jána Pavla II., pápeža' => 'Horná Ves - Farnosť sv. Jána Pavla II., pápeža',
            'H. Ves pri Kr.' => 'Horná Ves - Farnosť sv. Jána Pavla II., pápeža',
            'H. Ves' => 'Horná Ves - Farnosť sv. Jána Pavla II., pápeža',
            'Z. Kostolany' => 'Zemianske Kostolany',
            'Lehota pod Vt.' => 'Lehota pod Vtáčnikom',
            'FU- PD- mesto' => 'Prievidza - mesto',
            'PD Piaristi' => 'DS Kostola Najsvätejšej Trojice - Prievidza - Piaristi',
            'PD Zápotôčky' => 'Prievidza - Zapotôčky',
            'V. Lehôtka' => 'Prievidza - Veľká Lehôtka',
            'Priechod+Bal.' => 'Priechod',
            'S. Ľupča' => 'Slovenská Ľupča',
            'H. Turovce' => 'Horné Turovce',
            'I. Predmostie' => 'Ipeľské Predmostie',
            'Kláštor p.Zn.' => 'Kláštor pod Znievom',
            'S. Pravno' => 'Slovenské Pravno',
            'T. Teplice' => 'Turčianske Teplice',
            'T. Michal' => 'Turčiansky Michal',
            'T. Peter' => 'Turčiansky Peter',
            'Balog n/Ipľom' => 'Balog nad Ipľom',
            'D. Plachtince' => 'Dolné Plachtince',
            'M. Kameň' => 'Modrý Kameň',
            'Opat. N.Ves' => 'Opatovská Nová Ves',
            'V. Čalomija' => 'Veľká Čalomija',
            'V. Krtíš' => 'Veľký Krtíš',
            'Zv.-mesto' => 'Zvolen - mesto',
            'Zv. Sekier' => 'Zvolen - Sekier',
            'Zv. Západ' => 'Zvolen - Západ',
            'J. Lehota' => 'Janova Lehota',
            'Kr.Bane+Kop.' => 'Kremnické Bane',
            'Ladomer. Vieska' => 'Ladomerská Vieska',
            'l. Vieska' => 'Ladomerská Vieska',
            'ZH-Povýš. Sv.' => 'Žiar nad Hronom',
            'ZH-Sedmobol.' => 'Žiar nad Hronom - Farnosť Sedembolestnej Panny Márie',
            'Hor. Hámre-oslob.' => 'Horné Hámre',
            'Čerín-oslobod.' => 'Čerín',
            'FUT. Ďur+Socovce' => 'Socovce',
            'Bátovce+Pečen.' => 'Bátovce',
            'Hont. Trsťany' => 'Hontianske Trsťany',
            'Hr. Kosihy' => 'Hronské Kosihy',
            'Levice-mesto' => 'Levice - mesto',
            'N. Dedina' => 'Nová Dedina',
            'Žarnovica+B. Hod.' => 'Žarnovica',
            'k. Bane+Koper.' => 'Kremnické Bane',
            'KS Badín' => 'Kňazský seminár sv. Františka Xaverského v Badíne',
            'H. Nemce' => 'Hontianske Nemce',
            'Bzenica-oslob.' => 'Bzenica',
            'Chrenovec' => 'Chrenovec - Brusno',
            'Sl. Pravno' => 'Slovenské Pravno',
            'H. Hámre-oslob.' => 'Horné Hámre',
            'KláštorSampor' => 'DS Kostola a Kláštora premenenia Pána - Sampor - Benediktíni',
            'Ban. Hodruša' => 'Banská Hodruša',
        ];
        $zbierky = [];
        $is_firstline = true;
        $i = 0;
        $dons = Donation::where('year', $year)->get();
        foreach ($dons as $don) {
            $don->dos()->forceDelete();
            $don->forceDelete();
        }
        if (($handle = fopen(__DIR__ . '/zbierky-' . $year . '.csv', "r")) !== FALSE) {
            while (($line = fgetcsv($handle, 0, ";", '"')) !== FALSE) {
                if ($is_firstline) {
                    foreach ($line as $key => $value) {
                        if (!empty($value)) {
                            $donation = Donation::where('year', $year)->where('name', $value)->first();
                            if (!empty($donation)) {
                                dd('Error 5: Duplicated donation name ' . $year . ': ' . $value);
                            }

                            $donation = new Donation();
                            $donation->name = $value;
                            $donation->year = $year;
                            $donation->bank_account = 'SK46 0900 0000 0000 5018 0767';
                            $donation->save();

                            $zbierky[$key] = $donation->id;
                        }
                    }
                    $is_firstline = false;
                }
                if (!empty($line) && !empty($line[0])) {
                    $name = trim(str_replace(['FU ', 'Fu', 'FÚ', '   ', '  ',], ['', '', ' ', ' '], $line[0]));
                    if (isset($names[$name])) {
                        $name = $names[$name];
                    }
                    $organization = Organization::where('name', $name)->where('type_id', 7)->get();
                    if ($organization->count() == 0) {
                        $organization = Organization::where('name', $name)->get();
                    }
                    if ($organization->count() == 1) {
                        $organization = $organization[0];
                        foreach ($zbierky as $key => $zbierka_id) {
                            $amount = str_replace([',', ' '], ['.', ''], $line[$key]);
                            if (!is_numeric($amount) && !empty($amount)) {
                                dd('Error 3', $i, $line[0], $name, $organization, $amount);
                            }
                            if (empty($amount)) {
                                $amount = 0;
                            }
                            $don = DonationOrganization::where('donation_id', $zbierka_id)->where('organization_id', $organization->id)->first();
                            if (empty($don)) {
                                $don = new DonationOrganization();
                                $don->donation_id = $zbierka_id;
                                $don->organization_id = $organization->id;
                            }
                            $don->amount = $amount;
                            $don->save();
                        }
                    } else {
                        dd('Error 2', $i, $line[0], $name, $organization);
                    }
                }
                ++$i;
            }
        }
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (Auth::user()->id == 2) {
//            $this->importZbierky(2019);
//            $this->importZbierky(2018);
//            $this->importZbierky(2017);
//            $this->importZbierky(2016);
//            $this->importZbierky(2015);
//            $this->importZbierky(2014);
//            $this->importZbierky(2013);
//            $this->importZbierky(2012);
//            $this->importZbierky(2011);
//            $this->importZbierky(2010);
        }

        $rows = Donation::orderByDesc('year')->orderBy('id')->get();
        return view('donations.list', ['donations' => $rows]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $donation = new Donation();
        $donation->bank_account = 'SK46 0900 0000 0000 5018 0767';
        $donation->year = date('Y');
        $form = [
            'name' => 'Pridať zbierku',
            'method' => 'POST',
            'url' => route('donations.store')
        ];
        return view('donations.form', ['donation' => $donation, 'form' => $form]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        } else {
            $donation = new Donation();
            $donation->fill($request->all());
            $donation->save();
            Session::flash('message', 'Zbierka bol úspešne uložná.');
            return redirect()->to(route('donations.index'));
        }
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Donation $donation
     * @return \Illuminate\Http\Response
     */
    public function show(Donation $donation)
    {
        return view('donations.detail', ['donation' => $donation]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Donation $donation
     * @return \Illuminate\Http\Response
     */
    public function edit(Donation $donation)
    {
        $form = [
            'name' => 'Upraviť zbierku',
            'method' => 'PUT',
            'url' => route('donations.update', $donation)
        ];
        return view('donations.form', ['donation' => $donation, 'form' => $form]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Donation $donation
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Donation $donation)
    {
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        } else {
            $donation->fill($request->all());
            $donation->save();
            Session::flash('message', 'Zbierka bola úspešne uložená.');
            return redirect()->to(route('donations.index'));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Donation $donation
     * @return \Illuminate\Http\Response
     */
    public function destroy(Donation $donation)
    {
        $donation->delete();
        return redirect()->to(route('donations.index'));
    }

    public function export($year)
    {
        return Excel::download(new DonationsExport($year), 'zbierky-' . $year . '.xlsx');
    }
}
