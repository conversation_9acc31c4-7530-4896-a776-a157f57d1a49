<?php

namespace App\Http\Controllers;

use App\Helpers\Utils;
use App\Organization;
use App\OrganizationUser;
use App\OrganizationStat;
use App\User;
use Illuminate\Database\Eloquent\Collection;

class SchematizmusController extends Controller
{
    public function index()
    {
        $menu = 'index';
        return view('schematizmus.index', compact('menu'));
    }

    public function dieceznyBiskup()
    {
        $menu = 'diecezny-biskup';
        return view('schematizmus.diecezny-biskup', compact('menu'));
    }

    public function biskupskyUrad()
    {
        $menu = 'biskupsky-urad';
        $organization = Organization::findOrFail(998);
        $kancelariaBiskupa = $organization->childs()->active()->where('child_id', 1054)->first();
        $biskupskiVikari = $organization->childs()->active()->where('child_id', 1159)->first();
        $kancelariaUradu = $organization->childs()->active()->where('child_id', 1062)->first();
        $pravneOddelenie = $organization->childs()->active()->where('child_id', 1063)->first();
        $ekonomicke = $organization->childs()->active()->where('child_id', 1064)->first();
        $projektyGranty = $organization->childs()->active()->where('child_id', 1066)->first();
        $personalneMzdove = $organization->childs()->active()->where('child_id', 1065)->first();
        $archiv = $organization->childs()->active()->where('child_id', 677)->first();
        $cirkevneVyznacenieKnazov = $organization->childs()->active()->where('child_id', 670)->first();
        $zborKonzultorov = $organization->childs()->active()->where('child_id', 1061)->first();
        $knazskaRada = $organization->childs()->active()->where('child_id', 1060)->first();
        $ekonomickaRada = $organization->childs()->active()->where('child_id', 671)->first();
        $liturgickaKomisia = $organization->childs()->active()->where('child_id', 673)->first();
        $cenzori = $organization->childs()->active()->where('child_id', 1163)->first();
        return view('schematizmus.biskupsky-urad', compact(
                'menu',
                'organization',
                'kancelariaBiskupa',
                'biskupskiVikari',
                'kancelariaUradu',
                'pravneOddelenie',
                'ekonomicke',
                'projektyGranty',
                'personalneMzdove',
                'archiv',
                'cirkevneVyznacenieKnazov',
                'zborKonzultorov',
                'knazskaRada',
                'ekonomickaRada',
                'liturgickaKomisia',
                'cenzori',
            )
        );
    }

    public function dekanatyAFarnosti()
    {
        $menu = 'dekanaty-a-farnosti';
        $dekanaty = Utils::getBbdieceza()->childs('DEKANAT')->active()->isPublic()->getChilds(['by' => 'name_slovak']);
        return view('schematizmus.dekanaty-a-farnosti', compact('menu', 'dekanaty'));
    }

    public function dekanat($id, $slug)
    {
        $menu = 'dekanaty-a-farnosti';
        $organization = Organization::where('id', $id)->isPublic()->first();
        if (!$organization) {
            abort(404);
        }
        return view('schematizmus.dekanat', compact('menu', 'organization'));
    }

    public function farnost($id, $slug)
    {
        $menu = 'dekanaty-a-farnosti';
        $organization = Organization::where('id', $id)->isPublic()->first();
        $organization_stat = OrganizationStat::where('organization_id', $id)->whereNotNull('closed_at')->orderByDesc('year')->first();
        if (!$organization) {
            abort(404);
        }
        return view('schematizmus.farnost', compact('menu', 'organization', 'organization_stat'));
    }

    public function knazi()
    {
        $menu = 'knazi';
        $ous = OrganizationUser::whereHas('user', function ($q) {
            $q->isKnaz();
            $q->isPublic();
        })->groupBy('user_id')->order(['by' => 'priezvisko'])->get();
        $rows = [];
        foreach ($ous as $ou) {
            $rows[] = $ou->user;
        }
        $knazi = [];
        foreach ($rows as $knaz) {
            $letter = mb_substr($knaz->priezvisko, 0, 2) == 'Ch' ? 'ch' : (mb_substr($knaz->priezvisko, 0, 1));
            $knazi[mb_strtoupper(($letter))][] = $knaz;
        }
        return view('schematizmus.knazi', compact('menu', 'knazi'));
    }

    public function knaz($id, $slug)
    {
        $menu = 'knazi';
        $knaz = User::where('id', $id)->isKnaz()->isPublic()->first();
        if (!$knaz) {
            abort(404);
        }
        return view('schematizmus.knaz', compact('menu', 'knaz'));
    }

    public function rehole()
    {
        $menu = 'rehole';
        $reholeMuzske = Organization::findOrFail(1115)->childs()->isActive()->isPublic()->order(['by' => 'name'])->get();
        $reholeZenske = Organization::findOrFail(1168)->childs()->isActive()->isPublic()->order(['by' => 'name'])->get();
        return view('schematizmus.rehole', compact('menu', 'reholeMuzske', 'reholeZenske'));
    }

    public function rehola($id, $slug)
    {
        $menu = 'rehole';
        $organization = Organization::where('id', $id)->isPublic()->first();
        if (!$organization) {
            abort(404);
        }
        return view('schematizmus.rehola', compact('menu', 'organization'));
    }

    public function duchovnaSprava($id, $slug)
    {
        $menu = '';
        $organization = Organization::where('id', $id)->isPublic()->first();
        if (!$organization) {
            abort(404);
        }
        return view('schematizmus.duchovna-sprava', compact('menu', 'organization'));
    }

    public function cirkevnySud()
    {
        $menu = 'cirkevny-sud';
        $organization = Organization::findOrFail(675);
        return view('schematizmus.cirkevny-sud', compact('menu', 'organization'));
    }

    public function cirkevneSkolstvo()
    {
        $menu = 'cirkevne-skolstvo';
        $organization = Organization::findOrFail(676); // diecezny skolsky urad
        $skolyBiskupske = Organization::findOrFail(1181); // skoly biskupske
        $skolyIne = Organization::findOrFail(1185); // skoly biskupske
        return view('schematizmus.cirkevne-skolstvo', compact('menu', 'organization', 'skolyBiskupske', 'skolyIne'));
    }

    public function skola($id, $slug)
    {
        $menu = 'cirkevne-skolstvo';
        $organization = Organization::where('id', $id)->isPublic()->first();
        if (!$organization) {
            abort(404);
        }
        return view('schematizmus.skola', compact('menu', 'organization'));
    }

    public function hladaj()
    {
        $menu = '';
        $organizations = new Collection();
        $users = new Collection();
        $q = request()->get('q');
        if (!empty($q) && mb_strlen($q) >= 2) {
            $organizations = Organization::filter(['type_tag' => ['FARNOST', 'FILIALKA', 'DEKANAT'], 'name' => $q])->isPublic()->order(['by' => 'name'])->get();
            // if FILIALKA has no FARNOST parent do not show the result in search
            $organizations = $organizations->filter(function ($organization) {
                return !(($organization->type->tag ?? '') == 'FILIALKA') || $organization->parents('FARNOST')->first();
            });
            //echo "<pre>";print_r($organizations);echo "</pre>";
            $users = User::search($q)->isPublic()->isKnaz()->order(['by' => 'priezvisko'])->get();
        }
        return view('schematizmus.hladaj', compact('organizations', 'users', 'q', 'menu'));
    }
}
