<?php

namespace App\Http\Controllers;

use App\Entity;
use App\Folder;
use App\Organization;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;

class EntityController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
//        $mails = Mail::whereNotNull('protistrana_id')->withTrashed()->orderByDesc('id')->get();
//        foreach ($mails as $mail) {
//            if ($mail->protistrana) {
//                $entity_id = $mail->protistrana->entity->id;
//                $mail->protistrany()->sync([$entity_id]);
//                $mail->protistrana_id = null;
//                $mail->save();
//            } else {
//                dd($mail, $mail->protistrana);
//            }
//        }
//        $rows = Folder::with('partners2')->withTrashed()->get();
//        foreach ($rows as $row) {
//            $entities = [];
//            foreach ($row->partners2()->withTrashed()->get() as $partner) {
//                $entities[] = $partner->entity->id;
//            }
//            $row->partners()->sync($entities);
//            $row->save();
//        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Entity $entity
     * @return \Illuminate\Http\Response
     */
    public function show(Entity $entity)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Entity $entity
     * @return \Illuminate\Http\Response
     */
    public function edit(Entity $entity)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Entity $entity
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Entity $entity)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Entity $entity
     * @return \Illuminate\Http\Response
     */
    public function destroy(Entity $entity)
    {
        //
    }

    public function find(Request $request)
    {
        $term = trim($request->q);
        if (empty($term)) {
            return Response::json([]);
        }
        $organizations = Organization::search($term)->orderby('name')->limit(100)->get();
        $users = User::search($term)->orderby('priezvisko')->limit(100)->get();
        $rows = [];
        foreach ($organizations as $organization) {
            $rows[] = ['id' => $organization->entity->id, 'text' => $organization->fullname()];
        }
        foreach ($users as $user) {
            $rows[] = ['id' => $user->entity->id, 'text' => $user->fullname()];
        }
        return Response::json($rows);
    }
}
