<?php

namespace App\Http\Controllers;

use App\Mail;
use App\Organization;
use App\OrganizationStat;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class OrganizationController extends Controller
{
    private $rules = [
        'name' => 'required'
    ];

    public function __construct()
    {
        $this->authorizeResource(Organization::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
//        $stats = OrganizationStat::where('year', 2020)->get();
//        foreach ($stats as $stat) {
//            $metas = $stat->metas()->where(function ($q) {
//                $q->orWhere('meta_key', 'like', '%_pocet_obyvatelov%');
//                $q->orWhere('meta_key', 'like', '%_pocet_rimkat%');
//            })->get()->pluck('meta_value', 'meta_key')->toArray();
//
//            if (isset($metas['farnost_samostatne_pocet_obyvatelov'])) {
//                $stat->organization->pocet_obyvatelov = intval($metas['farnost_samostatne_pocet_obyvatelov']);
//            }
//            if (isset($metas['farnost_samostatne_pocet_rimkat'])) {
//                $stat->organization->pocet_rimkat = intval($metas['farnost_samostatne_pocet_rimkat']);
//            }
//            $stat->organization->save();
//
//            foreach ($stat->organization->childs as $child) {
//                $id = $child->child->id;
//                if (isset($metas['filialka_' . $id . '_pocet_obyvatelov'])) {
//                    $child->child->pocet_obyvatelov = intval($metas['filialka_' . $id . '_pocet_obyvatelov']);
//                }
//                if (isset($metas['filialka_' . $id . '_pocet_rimkat'])) {
//                    $child->child->pocet_rimkat = intval($metas['filialka_' . $id . '_pocet_rimkat']);
//                }
//                $child->child->save();
//            }
//        }

        $rows = Organization::order($_GET['order'] ?? [])->filter($_GET['filter'] ?? [])->paginate(200);
        return view('organization.list', ['organizations' => $rows]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $organization = new Organization();
        $form = [
            'name' => 'Vytvoriť organizáciu',
            'url' => route('organizations.store'),
            'method' => 'POST'
        ];
        return view('organization.form', ['organization' => $organization, 'form' => $form]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $organization = new Organization();
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return Redirect::to('organizations/create')->withInput()->withErrors($validator);
        } else {
            $organization->fill($request->all());
            $organization->save();
            Session::flash('message', 'Organizácia bola úspešne výtvorená.');
            return Redirect::to('organizations/' . $organization->id);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Organization $organization
     * @return \Illuminate\Http\Response
     */
    public function show(Organization $organization, $tab = 'contacts')
    {
        return view('organization.detail', ['organization' => $organization, 'tab' => $tab]);
    }

    public function tab($id, $tab = 'contacts')
    {
        $organization = Organization::findOrFail($id);

        if ($tab == 'mails') {
            $this->authorize('viewAny', new Mail(['organization_id' => $id]));
        }

        if ($tab == 'duplicity' && Request()->get('duplicate_organization_id')) {
            $duplicate = Organization::findOrFail(Request()->get('duplicate_organization_id'));
            $organization->absorb($duplicate);
        }
        Session::put('NEHNUTELNOST_LAST_PARENT', 'organization');
        Session::put('OU_LAST_DETAIL', 'organization');
        Session::put('OO_LAST_DETAIL', ['organization', $organization->id]);
        Session::put('MAIL_LAST_PARENT', ['organization', $organization->id]);
        Session::put('FOLDER_LAST_PARENT', ['organization', $organization->id]);
        Session::put('DOCUMENT_LAST_PARENT', 'organization');
        Session::put('ARCHIVE_LAST_PARENT', ['organization', $organization->id]);
        Session::put('NAJOM_LAST_PARENT', 'organization');

        $key = 'organization_' . $organization->id . '_' . $tab;

        $filter = $this->getFilter($key);
        $default = null;
        if ($tab == 'folders') {
            $default = ['by' => ['year', 'number'], 'dir' => 'desc'];
        }
        if ($tab == 'mails') {
            $default = ['by' => ['date_arrive', 'id'], 'dir' => 'desc'];
        }
        $order = $this->getOrder($key, $default);
        return view('organization.detail', [
            'organization' => $organization,
            'tab' => $tab,
            'show' => Request()->get('show'),
            'key' => $key,
            'order' => $order,
            'filter' => $filter
        ]);
    }

    public function vykazy($id)
    {
        $organization = Organization::findOrFail($id);
        $years = [];
        foreach ($organization->childs('FARNOST')->get() as $farnost) {
            foreach ($farnost->child->vykazy as $vykaz) {
                $years[$vykaz->year] = $vykaz->year;
            }
        }
        $years = array_reverse($years);
        return view('organization.vykazy', compact('organization', 'years'));
    }

    public function stats($id)
    {
        $organization = Organization::findOrFail($id);
        $years = [];
        foreach ($organization->childs('FARNOST')->get() as $farnost) {
            foreach ($farnost->child->stats as $stat) {
                $years[$stat->year] = $stat->year;
            }
        }
        $years = array_reverse($years);
        return view('organization.stats', compact('organization', 'years'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Organization $organization
     * @return \Illuminate\Http\Response
     */
    public function edit(Organization $organization)
    {
        $form = [
            'name' => 'Upraviť organizáciu',
            'url' => route('organizations.show', $organization->id),
            'method' => 'PUT'
        ];
        return view('organization.form', ['form' => $form, 'organization' => $organization]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Organization $organization
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Organization $organization)
    {
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return Redirect::to(route('organizations.edit', $organization->id))->withInput()->withErrors($validator);
        } else {
            $organization->fill($request->all());
            $organization->save();
            Session::flash('message', 'Organizácia bola úspešne uložená.');
            return Redirect::to(route('organizations.show', $organization->id));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Organization $organization
     * @return \Illuminate\Http\Response
     */
    public function destroy(Organization $organization)
    {
        $organization->delete();
        return Redirect::to(route('organizations.index'));
    }

    public function find(Request $request)
    {
        $term = trim($request->q);
        if (empty($term)) {
            return Response::json([]);
        }
        $organizations = Organization::search($term)->orderby('name')->limit(100)->get();
        $rows = [];
        foreach ($organizations as $organization) {
            $t = '';
            if ($organization->type) {
                $t = ' [' . $organization->type->name . ']';
            }
            $rows[] = ['id' => $organization->id, 'text' => $organization->name . $t];
        }
        return Response::json($rows);
    }
}
