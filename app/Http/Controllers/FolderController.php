<?php

namespace App\Http\Controllers;

use App\Entity;
use App\Exports\FoldersExport;
use App\File;
use App\Folder;
use App\Mail;
use App\Organization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class FolderController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
//        $rows = DB::table('folder_mail')->orderBy('id')->get();
//        foreach ($rows as $row) {
//            $row = DB::table('folder_mail')->where('id', $row->id)->first();
//            if ($row) {
//                DB::table('folder_mail')->where('id', '!=', $row->id)->where('mail_id', $row->mail_id)->where('folder_id', $row->folder_id)->delete();
//            }
//        }

//        foreach ($rows as $row) {
//            if($row->mails()->count() > 1) {
//                $mails = $row->mails->pluck('id');
//                dd($mails);
//            }
//        }
//        $rows = Folder::whereNotNull('mail_id')->get();
//        foreach ($rows as $row) {
//            $x = DB::select('select * from folder_mail where folder_id = ? and mail_id = ?', [$row->id, $row->mail_id]);
//            if (empty($x)) {
//                DB::table('folder_mail')->insert(['folder_id' => $row->id, 'mail_id' => $row->mail_id]);
//            }
//        }
//
//        $rows = Mail::whereNotNull('folder_id')->get();
//        foreach ($rows as $row) {
//            $x = DB::select('select * from folder_mail where folder_id = ? and mail_id = ?', [$row->folder_id, $row->id]);
//            if (empty($x)) {
//                DB::table('folder_mail')->insert(['folder_id' => $row->folder_id, 'mail_id' => $row->id]);
//            }
//        }
//
//        $rows = Folder::whereNotNull('folder_id')->get();
//        foreach ($rows as $row) {
//            $x = DB::select('select * from folder_folder where folder1_id = ? and folder2_id = ?', [$row->id, $row->folder_id]);
//            if (empty($x)) {
//                DB::table('folder_folder')->insert(['folder1_id' => $row->id, 'folder2_id' => $row->folder_id]);
//            }
//        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $folder = new Folder();
        $folder->fill(Request()->input());
        if (empty($folder->date_opened)) {
            $folder->date_opened = now();
        }
        if (empty($folder->year)) {
            $folder->year = date('Y');
        }
        if (empty($folder->number)) {
            $folder->number = $folder->getNextNumber($folder->year);
        }
        $form = [
            'name' => 'Vytvoriť spis',
            'method' => 'POST',
            'url' => route('folders.store'),
            'enctype' => 'multipart/form-data'
        ];
        return view('folders.form', ['form' => $form, 'folder' => $folder]);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), Folder::$rules);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        } else {
            $folder = new Folder();
            $folder->fill($request->all());
            $folder->save();
            if ($request->has('files')) {
                $files = $request->file('files');
                foreach ($files as $index => $f) {
                    $file = new File(['folder_id' => $folder->id, 'position' => $index + 1]);
                    $file->upload($f);
                    $file->save();
                }
            }

            $folder->sync_mails(Mail::sanitize_ids($request->get('mails')));
            $folder->folders()->sync(Folder::sanitize_ids($request->get('folders')));
            $folder->partners()->sync(Entity::sanitize_ids($request->get('partners')));
            Session::flash('message', 'Spis bol úspešne uložný.');
            return $folder->redirect();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Folder $folder
     * @return \Illuminate\Http\Response
     */
    public function show(Folder $folder)
    {
        Session::put('FOLDER_LAST_PARENT', ['folder', $folder->id]);
        Session::put('TASK_LAST_PARENT', ['folder', $folder]);
        return view('folders.detail', ['folder' => $folder]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Folder $folder
     * @return \Illuminate\Http\Response
     */
    public function edit(Folder $folder)
    {
        $form = [
            'name' => 'Upraviť spis',
            'method' => 'PUT',
            'url' => route('folders.update', $folder->id),
            'enctype' => 'multipart/form-data'
        ];
        return view('folders.form', ['form' => $form, 'folder' => $folder]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Folder $folder
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Folder $folder)
    {
        $validator = Validator::make($request->all(), Folder::$rules);
        if ($validator->fails()) {
            return Redirect::to(route('folders.edit', $folder->id))->withInput()->withErrors($validator);
        } else {
            $folder->fill($request->all());
            $folder->save();

            $index = 1;
            foreach ($request->get('items_position') ?? [] as $file_id => $p) {
                $file = File::find($file_id);
                if ($file) {
                    $file->position = $index;
                    $file->save();
                    ++$index;
                }
            }

            if ($request->has('files')) {
                $files = $request->file('files');
                foreach ($files as $f) {
                    $file = new File(['folder_id' => $folder->id]);
                    $file->position = $index;
                    $file->upload($f);
                    $file->save();
                    ++$index;
                }
            }
            $files_to_delete = $request->get('files_to_delete') ?? [];
            foreach ($files_to_delete as $file_id) {
                $file = File::find($file_id);
                if (!empty($file)) {
                    $file->delete();
                }
            }
            $folder->sync_mails(Mail::sanitize_ids($request->get('mails')));
            $folder->folders()->sync(Folder::sanitize_ids($request->get('folders')));
            $folder->partners()->sync(Entity::sanitize_ids($request->get('partners')));
            Session::flash('message', 'Spis bol uložený.');
            return $folder->redirect();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Folder $folder
     * @return \Illuminate\Http\Response
     */
    public function destroy(Folder $folder)
    {
        $folder->delete();
        Session::flash('message', 'Spis bol vymazaný.');
        return $folder->redirect();
    }

    public function find(Request $request)
    {
        $term = trim($request->q);
        $organization = Organization::find(intval($request->organization_id));
        if (empty($term)) {
            return Response::json([]);
        }
        if ($organization) {
            $folders = Folder::search($term)->where('organization_id', $organization->id)->get();
        } else {
            $folders = Folder::search($term)->get();
        }
        $rows = [];
        foreach ($folders as $folder) {
            $rows[] = ['id' => $folder->id, 'text' => $folder->number . '/' . $folder->year . ' - ' . $folder->name];
        }
        return Response::json($rows);
    }

    public function export(Organization $organization)
    {
        $folders = $organization->folders($_GET['filter'] ?? [], $_GET['order'] ?? [])->get();
        return Excel::download(new FoldersExport($folders), 'spisy-' . date('YmdHis') . '.xlsx');
    }


    public function viewer($folderId, $fileId = null)
    {
        $folder = Folder::findOrFail($folderId);
        if (!empty($fileId)) {
            $file = $folder->files()->findOrFail($fileId);
        } else {
            $file = $folder->files->first();
        }

        $numbers = $folder->files()->get()->map(function ($row) {
            return ['id' => $row->id];
        })->pluck('id')->toArray();

        if ($file) {
            $data = [
                'url' => $file->url(),
                'download_url' => $file->url(),
                'type' => $file->type(),
                'previous' => '',
                'next' => '',
                'index' => $file->index(),
                'count' => $folder->files->count(),
                'title' => $folder->name,
                'title_url' => '/folders/' . $folder->id,
                'filename' => $file->name,
                'numbers' => $numbers,
            ];
            if ($file->previous()) {
                $data['previous'] = '/folders/' . $folder->id . '/viewer/' . $file->previous()->id;
            }
            if ($file->next()) {
                $data['next'] = '/folders/' . $folder->id . '/viewer/' . $file->next()->id;
            }
            return view('viewer.show', $data);
        }
        return 'Žiadny obsah na zobrazenie.';
    }


    public function viewerMail($folderId, $mail_id = null, $file_id = null)
    {
        $folder = Folder::findOrFail($folderId);

        if (!empty($mail_id)) {
            $mail = $folder->mails()->has('files')->findOrFail($mail_id);
        } else {
            $mail = $folder->mails()->has('files')->first();
        }

        if (!empty($file_id)) {
            $file = $mail->files()->findOrFail($file_id);
        } else {
            $file = $mail->files()->first();
        }

        $numbers = [];
        $i = 0;
        foreach ($folder->mails()->has('files')->get() as $row) {
            $numbers[$row->id] = ['name' => $row->fullname() . ' ' . $row->date_arrive, 'rows' => []];
            foreach ($row->files as $f) {
                $numbers[$row->id]['rows'][$i] = $f->id;
                ++$i;
            }
        }

        if ($file) {
            $data = [
                'url' => '/folders/' . $folder->id . '/storage-viewer-mail/' . $mail->id . '/' . $file->id,
                'download_url' => '/folders/' . $folder->id . '/storage-viewer-mail/' . $mail->id . '/' . $file->id,
                'type' => $file->type(),
                'previous' => '',
                'next' => '',
                'index' => $file->id,
                'count' => $folder->mails_files_count(),
                'title' => $folder->name,
                'subtitle' => 'Pošta: ' . $mail->fullname(['date_arrive', 'description']),
                'subtitle_url' => route('mails.show', $mail),
                'title_url' => '/folders/' . $folder->id,
                'filename' => $file->name,
                'numbers_sub' => $numbers,
                'viewer_url' => '/folders/' . $folder->id . '/viewer-mail'
            ];
            if ($file->previous()) {
                $data['previous'] = '/folders/' . $folder->id . '/viewer-mail/' . $mail->id . '/' . $file->previous()->id;
            } else {
                if ($mail->previous($folder)) {
                    $data['previous'] = '/folders/' . $folder->id . '/viewer-mail/' . $mail->previous($folder)->id;
                }
            }
            if ($file->next()) {
                $data['next'] = '/folders/' . $folder->id . '/viewer-mail/' . $mail->id . '/' . $file->next()->id;
            } else {
                if ($mail->next($folder)) {
                    $data['next'] = '/folders/' . $folder->id . '/viewer-mail/' . $mail->next($folder)->id;
                }
            }
            return view('viewer.show', $data);
        }
        return 'Žiadny obsah na zobrazenie.';
    }
    
    public function storageViewerMail($folderId, $mail_id = null, $file_id = null)
    {
        $folder = Folder::findOrFail($folderId);

        if (!empty($mail_id)) {
            $mail = $folder->mails()->has('files')->findOrFail($mail_id);
        } else {
            $mail = $folder->mails()->has('files')->first();
        }

        if (!empty($file_id)) {
            $file = $mail->files()->findOrFail($file_id);
        } else {
            $file = $mail->files()->first();
        }

        if ($file) {
            $data = [
                'url' => $file->url(),
                //'content_type' => 'application/pdf',
                'content_disposition' => 'filename="'.$file->name.'"'
            ];
            
            return view('viewer.storage-show', $data);
        }
        return 'Žiadny obsah na zobrazenie.';
    }
    
    public function storageViewer($folderId, $fileId = null)
    {
        $folder = Folder::findOrFail($folderId);
        if (!empty($fileId)) {
            $file = $folder->files()->findOrFail($fileId);
        } else {
            $file = $folder->files->first();
        }

        if ($file) {
            $data = [
                'url' => $file->url(),
                //'content_type' => 'application/pdf',
                'content_disposition' => 'filename="'.$file->name.'"'
            ];
            
            return view('viewer.storage-show', $data);
        }
        return 'Žiadny obsah na zobrazenie.';
    }
}
