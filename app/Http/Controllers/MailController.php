<?php

namespace App\Http\Controllers;

use App\Entity;
use App\File;
use App\Mail;
use App\Organization;
use App\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Spatie\SimpleExcel\SimpleExcelWriter;

class MailController extends Controller
{

    public function __construct()
    {
        $this->authorizeResource(Mail::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $mail = new Mail();
        if (Request()->get('mail_id')) {
            $suvisiaci = Mail::findOrFail(Request()->get('mail_id'));
            $mail->organization_id = $suvisiaci->organization_id;
        }
        $mail->fill(Request()->input());
//        $this->authorize('custom-create', $mail);
        if (empty($mail->date_arrive)) {
            $mail->date_arrive = now();
        }
        if ($mail->mail_id) {
            if (!$mail->organization_id) {
                $mail->organization_id = $mail->mail->organization->id;
            }
        }
        $y = date('Y');
        if (empty($mail->year)) {
            $mail->year = $y;
        }
        if (empty($mail->number)) {
            $mail->number = $mail->getNextNumber($mail->year);
        }
        $this->authorize('createCustom', $mail);
        $form = [
            'name' => 'Pridať poštový záznam',
            'method' => 'POST',
            'url' => route('mails.store'),
            'enctype' => 'multipart/form-data'
        ];
        return view('mails.form', ['form' => $form, 'mail' => $mail]);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), Mail::$rules);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        } else {
            $mail = new Mail();
            $mail->fill($request->all());
            $mail->save();
            $mail->tags()->sync(Tag::sanitize_ids($request->tags, $mail));
            if ($request->has('files')) {
                $files = $request->file('files');
                foreach ($files as $index => $f) {
                    $file = new File(['mail_id' => $mail->id, 'position' => $index]);
                    $file->upload($f);
                    $file->save();
                }
            }
            $mail->vybavuje()->sync(Entity::sanitize_ids($request->vybavuje));
            $mail->protistrany()->sync(Entity::sanitize_ids($request->protistrany));
            $mail->sync_folders($request->get('folders'));
            Session::flash('message', 'Pošta bola úspešne uložná.');
            return $mail->redirect();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Mail $mail
     * @return \Illuminate\Http\Response
     */
    public function show(Mail $mail)
    {
        Session::put('MAIL_LAST_PARENT', ['mail', $mail->id]);
        Session::put('TASK_LAST_PARENT', ['mail', $mail]);
        $key = 'mail-' . $mail->id . '-detail';
        $filter = $this->getFilter($key);
        $order = $this->getOrder($key);
        return view('mails.detail', compact('mail', 'key', 'filter', 'order'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Mail $mail
     * @return \Illuminate\Http\Response
     */
    public function edit(Mail $mail)
    {
        $form = [
            'name' => 'Upraviť poštový záznam',
            'method' => 'PUT',
            'url' => route('mails.update', $mail->id),
            'enctype' => 'multipart/form-data'
        ];
        return view('mails.form', ['form' => $form, 'mail' => $mail]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Mail $mail
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Mail $mail)
    {
        $validator = Validator::make($request->all(), Mail::$rules);
        if ($validator->fails()) {
            return Redirect::to(route('mails.edit', $mail->id))->withInput()->withErrors($validator);
        } else {
            $mail->fill($request->all());
            $mail->save();
            $mail->tags()->sync(Tag::sanitize_ids($request->tags, $mail));
            $index = 1;
            foreach ($request->get('items_position') ?? [] as $file_id => $p) {
                $file = File::find($file_id);
                if ($file) {
                    $file->position = $index;
                    $file->save();
                    ++$index;
                }
            }
            if ($request->has('files')) {
                $files = $request->file('files');
                foreach ($files as $f) {
                    $file = new File(['mail_id' => $mail->id, 'position' => $index]);
                    $file->upload($f);
                    $file->save();
                    ++$index;
                }
            }
            $files_to_delete = $request->get('files_to_delete') ?? [];
            foreach ($files_to_delete as $file_id) {
                $file = File::find($file_id);
                if (!empty($file)) {
                    $file->delete();
                }
            }
            $mail->vybavuje()->sync(Entity::sanitize_ids($request->vybavuje));
            $mail->protistrany()->sync(Entity::sanitize_ids($request->protistrany));
            $mail->sync_folders($request->get('folders'));
            Session::flash('message', 'Poštový záznam bol úspešne uložený.');
            return $mail->redirect();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Mail $mail
     * @return \Illuminate\Http\Response
     */
    public function destroy(Mail $mail)
    {
        $mail->delete();
        return $mail->redirect();
    }

    public function find(Request $request)
    {
        $term = trim($request->q);
        $organization = Organization::find(intval($request->organization_id));
        $exclude = $request->get('exclude');
        if (empty($term)) {
            return Response::json([]);
        }
        $query = Mail::search($term);
        if ($organization) {
            $query->where('organization_id', $organization->id);
        }
        if ($exclude) {
            $query->whereNotIn('id', explode(',', $exclude));
        }
        $mails = $query->get();
        $rows = [];
        foreach ($mails as $mail) {
            $rows[] = ['id' => $mail->id, 'text' => $mail->number . '/' . $mail->year . ' [' . $mail->date_arrive . '] - ' . $mail->description];
        }
        return Response::json($rows);
    }
    
    public function export(Organization $organization)
    {
        $writer = SimpleExcelWriter::streamDownload('posta-' . date('YmdHis') . '.xlsx');
        $query = $organization->mails($_GET['filter'] ?? [], $_GET['order'] ?? []);
        $i = 0;
        foreach ($query->lazy(1000) as $mail) 
        {
            $mails = [];
            foreach ($mail->mails as $m) {
                $mails[] = $m->fullname();
            }
            $folders = [];
            foreach ($mail->folders as $f) {
                $folders[] = $f->fullname();
            }
            $tags = [];
            foreach ($mail->tags as $tag) {
                $tags[] = $tag->shortname;
            }
            $vybavuje = [];
            // $mail->person sa uz nepouziva, ale historicky ostava (je to samostatny stlpec v DB)
            if (!empty($mail->person)) {
                $vybavuje[] = $mail->person;
            }
            foreach($mail->vybavuje as $_vybavuje) {
                $vybavuje[] = $_vybavuje->fullname();
            }
            $protistrana = [];
            if (!empty($mail->protistrana_text)) {
                $protistrana[] = $mail->protistrana_text;
            }
            foreach ($mail->protistrany as $_protistrana) {
                if ($_protistrana->object) {
                    $protistrana[] = $_protistrana->object->fullname();
                }
            }
            
            $writer->addRow([
                'Dátum' => $mail->date_arrive,
                'Číslo' => $mail->number,
                'Rok' => $mail->year,
                'Súvisiace' => join(' ,', $mails),
                'Spis' => join(' ,', $folders),
                'Heslo' => join(' ,', $tags),
                'Protistrana' => join(' ,', $protistrana),
                'Vec' => $mail->description,
                'Vybavuje' => join(' ,', $vybavuje),
                'Typ' => ($mail->type ? $mail->type->name : ''),
                'Poznámka' => $mail->notes,
            ]);

            if ($i % 500 === 0) {
                flush(); // Flush the buffer every 500 rows
            }
            $i++;
        }
        return $writer->toBrowser();
    }

    public function viewer($mailId, $fileId = null)
    {
        $mail = Mail::findOrFail($mailId);
        if (!empty($fileId)) {
            $file = $mail->files()->findOrFail($fileId);
        } else {
            $file = $mail->files->first();
        }

        $numbers = $mail->files()->get()->map(function ($row) {
            return ['id' => $row->id];
        })->pluck('id')->toArray();

        if ($file) {
            $data = [
                'url' => '/mails/' . $mail->id . '/storage-viewer/' . $file->id,
                'download_url' => '/mails/' . $mail->id . '/storage-viewer/' . $file->id,
                'type' => $file->type(),
                'previous' => '',
                'next' => '',
                'index' => $file->index(),
                'count' => $mail->files->count(),
                'title' => $mail->description,
                'title_url' => '/mails/' . $mail->id,
                'subtitle' => $file->name,
                'numbers' => $numbers,
            ];
            if ($file->previous()) {
                $data['previous'] = '/mails/' . $mail->id . '/viewer/' . $file->previous()->id;
            }
            if ($file->next()) {
                $data['next'] = '/mails/' . $mail->id . '/viewer/' . $file->next()->id;
            }
            return view('viewer.show', $data);
        }
        return 'Žiadny obsah na zobrazenie.';
    }
    
    public function storageViewer($mailId, $fileId = null)
    {
        $mail = Mail::findOrFail($mailId);
        if (!empty($fileId)) {
            $file = $mail->files()->findOrFail($fileId);
        } else {
            $file = $mail->files->first();
        }

        if ($file) {
            $data = [
                'url' => $file->url(),
                //'content_type' => 'application/pdf',
                'content_disposition' => 'filename="'.$file->name.'"'
            ];
            
            return view('viewer.storage-show', $data);
        }
        return 'Žiadny obsah na zobrazenie.';
    }
}
