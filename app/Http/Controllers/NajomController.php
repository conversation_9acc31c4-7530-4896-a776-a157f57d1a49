<?php

namespace App\Http\Controllers;

use App\Najom;
use App\Nehnutelnost;
use App\Organization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class NajomController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    private $rules = [];

    public function index()
    {
//        $rows = Najom::all();
//        foreach ($rows as $row) {
//            $n = $row->nehnutelnosti()->first();
//            if ($n) {
//                $row->organization_id = $n->organization_id;
//                $row->save();
//            }
//
//        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $najom = new Najom();
        $organization_id = request()->get('organization_id');
        if (!$organization_id) {
            $n = Nehnutelnost::findOrFail(request()->get('nehnutelnosti')[0] ?? null)->first();
            $organization_id = $n->organization_id;
        }
        $organization = Organization::findOrFail($organization_id);
        $najom->organization_id = $organization->id;
        $form = [
            'name' => 'Pridať nájom',
            'url' => route('najom.store'),
            'method' => 'POST',
        ];
        return view('najom.form', ['najom' => $najom, 'form' => $form]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        } else {
            $najom = new Najom();
            $najom->fill($request->all());
            $najom->save();
            $najom->nehnutelnosti()->sync($request->get('nehnutelnosti') ?? []);
            Session::flash('message', 'Nájom bol úspešne uložný.');
            return Redirect::to($najom->redirect_url());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Najom $najom
     * @return \Illuminate\Http\Response
     */
    public function show(Najom $najom)
    {
        Session::put('NAJOM_LAST_PARENT', $najom);
        return view('najom.detail', ['najom' => $najom]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Najom $najom
     * @return \Illuminate\Http\Response
     */
    public function edit(Najom $najom)
    {
        $form = [
            'name' => 'Upraviť nájom',
            'url' => route('najom.update', $najom->id),
            'method' => 'PUT',
        ];
        return view('najom.form', ['najom' => $najom, 'form' => $form]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Najom $najom
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Najom $najom)
    {
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        } else {
            $najom->fill($request->all());
            $najom->save();
            $najom->nehnutelnosti()->sync($request->get('nehnutelnosti') ?? []);
            Session::flash('message', 'Nájom bola úspešne uložený.');
            return Redirect::to($najom->redirect_url());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Najom $najom
     * @return \Illuminate\Http\Response
     */
    public function destroy(Najom $najom)
    {
        $najom->delete();
        Session::flash('message', 'Nájom bola vymazaný.');
        return Redirect::to($najom->redirect_url());
    }

    public function find(Request $request)
    {
        $term = trim($request->q);
        $organization = Organization::find(intval($request->get('organization_id')));
        if (empty($term)) {
            return Response::json([]);
        }
        if ($organization) {
            $najmi = Najom::search($term)->whereHas('nehnutelnosti', function ($q) use ($organization) {
                $q->where('organization_id', $organization->id);
            })->get();
        } else {
            $najmi = Najom::search($term)->limit(10)->get();
        }
        $rows = [];
        foreach ($najmi as $najom) {
            $rows[] = ['id' => $najom->id, 'text' => $najom->full_najomca()];
        }
        return Response::json($rows);
    }
}
