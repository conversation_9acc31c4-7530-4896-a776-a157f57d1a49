<?php

namespace App\Http\Controllers;

use App\User;
use Eluceo\iCal\Domain\Entity\Calendar;
use Eluceo\iCal\Domain\Entity\Event;
use Eluceo\iCal\Domain\ValueObject\Date;
use Eluceo\iCal\Domain\ValueObject\SingleDay;
use Eluceo\iCal\Domain\ValueObject\UniqueIdentifier;
use Eluceo\iCal\Presentation\Factory\CalendarFactory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class UsersController extends Controller
{
    private $rules = array(
        'priezvisko' => ['required']
    );

    public function __construct()
    {
        $this->authorizeResource(User::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $users = User::order($this->getOrder('users_order'))->filter($this->getFilter('users_filter')['data'])->paginate(200);
        return view('users.list', ['users' => $users]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $user = new User();
        $user->fill(Request()->all());
        $form = [
            'name' => 'Pridať používateľa',
            'method' => 'post',
            'url' => url('users')
        ];
        return view('users.form', ['user' => $user, 'form' => $form]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $user = new User();
        if (!empty($request->get('password'))) {
            $this->rules['password'] = 'required';
            $this->rules['password_again'] = 'required|same:password';
        }
        if (!empty($request->get('username'))) {
            $this->rules['username']['unique'] = Rule::unique('users');
        }
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return Redirect::to('users/create')->withInput()->withErrors($validator);
        } else {
            $user->fill($request->all());
            $user->password = bcrypt(Request()->get('password'));
            $user->save();
            if ($request->get('roles')) {
                $user->syncRoles($request->get('roles'));
            }
            Session::flash('message', __('User successfully created.'));
            return Redirect::to('users/' . $user->id);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param \App\User $user
     * @return \Illuminate\Http\Response
     */
    public function show(User $user, $tab = 'contacts')
    {
        Session::put('OU_LAST_DETAIL', 'user');
        Session::put('TASK_LAST_PARENT', ['user', $user]);
        return view('users.detail', ['user' => $user, 'tab' => $tab]);
    }

    public function tab($id, $tab = 'contacts')
    {
        $user = User::findOrFail($id);
        if ($tab == 'duplicity' && Request()->get('duplicate_user_id')) {
            $duplicate = User::findOrFail(Request()->get('duplicate_user_id'));
            $user->absorb($duplicate);
        }
        Session::put('OU_LAST_DETAIL', 'user');
        Session::put('TASK_LAST_PARENT', ['user', $user]);
        $key = 'user-' . $user->id . '-detail-' . $tab;
        $filter = $this->getFilter($key);
        $order = $this->getOrder($key);
        return view('users.detail', compact('user', 'key', 'tab', 'order', 'filter'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\User $user
     * @return \Illuminate\Http\Response
     */
    public function edit(User $user)
    {
        $user->password = '';
        $form = [
            'name' => 'Upraviť používateľa',
            'method' => 'PUT',
            'route' => ['users.update', $user->id]
        ];
        return view('users.form', ['type' => 'update', 'user' => $user, 'form' => $form]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\User $user
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, User $user)
    {
        if (!empty(Request()->get('password'))) {
            $this->rules['password'] = 'required';
            $this->rules['password_again'] = 'required|same:password';
        }
        if (!empty($request->get('username'))) {
            $this->rules['username']['unique'] = Rule::unique('users')->ignore($user->id);
        }
        $validator = Validator::make($request->all(), $this->rules);
        if ($validator->fails()) {
            return Redirect::to('users/' . $user->id . '/edit')->withInput()->withErrors($validator);
        } else {
            $user->fill($request->except('password'));
            if (!empty(Request()->get('password'))) {
                $user->password = Hash::make(Request()->get('password'));
            }
            $user->save();
            if ($request->get('roles')) {
                $user->syncRoles($request->get('roles'));
            }
            Session::flash('message', 'Údaje používateľa boli uložené.');
            return Redirect::to('users/' . $user->id);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\User $user
     * @return \Illuminate\Http\Response
     */
    public function destroy(\App\User $user)
    {
        $user->delete();
        return Redirect::to(route('users.index'));
    }

    public function find(Request $request)
    {
        $term = trim($request->q);
        if (empty($term)) {
            return Response::json([]);
        }
        $users = User::search($term)->orderby('priezvisko')->limit(100)->get();
        $rows = [];
        foreach ($users as $user) {
            $rows[] = ['id' => $user->id, 'text' => $user->fullname()];
        }
        return Response::json($rows);
    }

    public function deputyLogin($id)
    {
        $user = User::findOrFail($id);
        $admin = Auth::user();
        if ($admin->sysadmin == 1) {
            if ($user->sysadmin == 1) {
                dd('Restricted access. Pouzivatel pre zástupné prihlásenie je sysadmin.');
            }
            Session::put('USER_LOGIN_DEPUTY', $admin->id);
            Auth::login($user);
        }
        return redirect()->to(request()->headers->get('referer') ?? '/');
    }

    public function deputyLogout()
    {
        $sess = Session::get('USER_LOGIN_DEPUTY');
        if (!empty($sess)) {
            $user = User::findOrFail($sess);
            if ($user->sysadmin == 1) {
                Session::put('USER_LOGIN_DEPUTY', null);
                Auth::login($user);
            }
        }
        return redirect()->to(request()->headers->get('referer') ?? '/');
    }

//    public function calendar()
//    {
//        $calendar = Calendar::create('Testovic online')
//            ->refreshInterval(1)
//            ->event(
//                Event::create('Testovic calender feeds')->startsAt(new \DateTime('2020-12-31 15:00:00'))->endsAt(new \DateTime('2020-12-31 16:00:00'))->uniqueIdentifier('AX1')
//            )->event(
//                Event::create('Jarne vinobranie')->startsAt(new \DateTime('2020-12-31 15:00'))->uniqueIdentifier('AX2')
//            )->get();
//        return response($calendar, 200, [
//            'Content-Type' => 'text/calendar',
//            'Content-Disposition' => 'attachment; filename="my-awesome-calendar.ics"',
//            'charset' => 'utf-8',
//        ]);
//    }

    public function calendar()
    {
        $calendar = new Calendar();

        $event = new Event(new UniqueIdentifier('D'));

        $date = new Date(\DateTimeImmutable::createFromFormat('Y-m-d', '2021-01-01'));
        $occurrence = new SingleDay($date);

        $event->setOccurrence($occurrence)
            ->setSummary('Termin do 1.1.2021');
        $calendar->addEvent($event);

        $calendarComponentFactory = new CalendarFactory();
        $calendarComponent = $calendarComponentFactory->createCalendar($calendar);

        header('Content-Type: text/calendar; charset=utf-8');
        header('Content-Disposition: attachment; filename="cal.ics"');
        echo $calendarComponent;
    }
}
