<?php

namespace App\Http\Controllers;

use App\WarehouseItem;
use Illuminate\Http\Request;

class WarehouseItemController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\WarehouseItem  $warehouseItem
     * @return \Illuminate\Http\Response
     */
    public function show(WarehouseItem $warehouseItem)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\WarehouseItem  $warehouseItem
     * @return \Illuminate\Http\Response
     */
    public function edit(WarehouseItem $warehouseItem)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\WarehouseItem  $warehouseItem
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, WarehouseItem $warehouseItem)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\WarehouseItem  $warehouseItem
     * @return \Illuminate\Http\Response
     */
    public function destroy(WarehouseItem $warehouseItem)
    {
        //
    }
}
