<?php

namespace App\Http\Controllers;

use App\Helpers\Utils;
use App\Organization;
use App\OrganizationStat;
use App\OrganizationStatMeta;
use App\OrganizationVykaz;
use App\OrganizationVykazMeta;
use Barryvdh\DomPDF\Facade;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class OrganizationVykazController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(OrganizationVykaz::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $years = OrganizationVykaz::getYears();
        return view('organization-vykaz.list', ['years' => $years]);
    }

    public function year($year)
    {
        $dekanaty = Utils::getBbdieceza()->childs('DEKANAT')->activeByYear($year)->getChilds(['by' => 'name']);
        return view('organization-vykaz.year', ['year' => $year, 'dekanaty' => $dekanaty]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $organization_id = Request()->get('organization_id');
        $organization = Organization::findOrFail($organization_id);
        if (!$organization->has_vykaz_type()) {
            report(new \Exception('Organizacia ' . $organization_id . ' nie je typ pre vykaz.'));
            abort(500);
        }
        $vykaz = new OrganizationVykaz(['organization_id' => $organization->id]);
        $form = [
            'name' => 'Výkaz ' . $organization->name,
            'method' => 'POST',
            'url' => route('organization-vykaz.store')
        ];
        return view('organization-vykaz.form-year', ['form' => $form, 'vykaz' => $vykaz]);
//        if ($organization->vykazy()->actualYear()->count()) {
//            $stat = $organization->vykazy()->actualYear()->first();
//        } else {
//            $stat = OrganizationVykaz::updateOrCreate(['organization_id' => $organization_id, 'year' => OrganizationVykaz::getActualYear()]);
//        }
//        return Redirect::to('organization-vykaz/' . $stat->id . '/edit');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $organizationVykaz = new OrganizationVykaz();
        $organizationVykaz->fill(Request()->all());

        Validator::make($request->all(), ['year' => [
            'required',
            'integer',
            'between:1900,2100',
            Rule::unique('organization_vykaz')->where(function ($query) {
                return $query->where('organization_id', request()->get('organization_id'))->whereNull('deleted_at')->where('year', request()->get('year'));
            }),
        ]], ['unique' => 'Záznam pre tento rok už existuje.'])->validate();

        $organizationVykaz->save();
        return Redirect::to(route('organization-vykaz.update', $organizationVykaz));
    }

    /**
     * Display the specified resource.
     *
     * @param \App\OrganizationVykaz $organizationVykaz
     * @return \Illuminate\Http\Response
     */
    public function show(OrganizationVykaz $organizationVykaz)
    {
        $metas = [];
        foreach ($organizationVykaz->metas as $meta) {
            $metas['meta'][$meta->meta_key] = $meta->meta_value;
        }
        $form = [
            'name' => 'Výkaz ' . $organizationVykaz->organization->name . ' - ' . $organizationVykaz->year,
            'method' => 'PUT',
            'url' => route('organization-vykaz.update', $organizationVykaz->id)
        ];
        return view('organization-vykaz.form', ['vykaz' => $organizationVykaz, 'form' => $form, 'metas' => $metas]);
    }

    public function download($id)
    {
        $debug = false;
        $vykaz = OrganizationVykaz::findOrFail($id);

        if ($debug) {
            return view('organization-vykaz.pdf', ['vykaz' => $vykaz]);
        }

        $pdf = Facade::loadView('organization-vykaz.pdf', ['vykaz' => $vykaz])->setPaper('a4');
        return $pdf->download('vykaz-farnosti-' . $vykaz->year . '-' . Str::slug($vykaz->organization->name) . '.pdf');

    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\OrganizationVykaz $organizationVykaz
     * @return \Illuminate\Http\Response
     */
    public function edit(OrganizationVykaz $organizationVykaz)
    {
        $metas = [];
        foreach ($organizationVykaz->metas as $meta) {
            $metas['meta'][$meta->meta_key] = $meta->meta_value;
        }
        $form = [
            'name' => 'Výkaz ' . $organizationVykaz->organization->name . ' - ' . $organizationVykaz->year,
            'method' => 'PUT',
            'url' => route('organization-vykaz.update', $organizationVykaz->id)
        ];
        return view('organization-vykaz.form', ['vykaz' => $organizationVykaz, 'form' => $form, 'metas' => $metas]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\OrganizationVykaz $organizationVykaz
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, OrganizationVykaz $organizationVykaz)
    {
        $organizationVykaz->fill(Request()->all());
        $organizationVykaz->save();

        $close_vykaz = !empty(Request()->get('close_vykaz'));
        $close_vykaz_as_dekanat = $request->get('close_vykaz_as_dekanat');
        $close_vykaz_as_ordinariat = $request->get('close_vykaz_as_ordinariat');
        $open_vykaz = !empty(Request()->get('open_vykaz'));
        $open_vykaz_as_dekanat = !empty(Request()->get('open_vykaz_as_dekanat'));
        $open_vykaz_as_ordinariat = !empty(Request()->get('open_vykaz_as_ordinariat'));

        foreach (Request()->get('meta') as $key => $value) {
            $meta = OrganizationVykazMeta::updateOrCreate(
                ['organization_vykaz_id' => $organizationVykaz->id, 'meta_key' => $key],
                ['meta_value' => $value]
            );
        }

        $o = $organizationVykaz->organization;
        if ($request->get('ico')) {
            $o->ico = $request->get('ico');
            $o->save();
        }
        if ($request->get('dic')) {
            $o->dic = $request->get('dic');
            $o->save();
        }
        if ($request->get('cislo_uctu')) {
            $o->cislo_uctu = $request->get('cislo_uctu');
            $o->save();
        }

        if ($close_vykaz || $close_vykaz_as_dekanat || $close_vykaz_as_ordinariat) {
            $validator = Validator::make(request()->all(), ['ico' => 'required', 'dic' => 'required', 'cislo_uctu' => 'required']);
            if ($validator->fails()) {
                return redirect()->back()->withInput()->withErrors($validator);
            }
        }

        if ($close_vykaz) {
            $organizationVykaz->closed_at = date('Y-m-d H:i:s');
            $organizationVykaz->closed_by = Auth::user()->id;
            $organizationVykaz->save();
        }
        if ($close_vykaz_as_dekanat) {
            $organizationVykaz->closed_at_dekanat = date('Y-m-d H:i:s');
            $organizationVykaz->closed_by_dekanat = Auth::user()->id;
            $organizationVykaz->save();
        }
        if ($close_vykaz_as_ordinariat) {
            $organizationVykaz->closed_at_ordinariat = date('Y-m-d H:i:s');
            $organizationVykaz->closed_by_ordinariat = Auth::user()->id;
            $organizationVykaz->save();
        }
        
        if($open_vykaz && Auth::user()->can('openAsFarnost', $organizationVykaz)) {
            if($organizationVykaz->isDekanatClosed()) {
                return redirect()->back()->with("error", "Nie je možné otvoriť, už je uzatvorený výkaz za dekanát.");
            }
            $organizationVykaz->closed_at = null;
            $organizationVykaz->closed_by = null;
            $organizationVykaz->save();
        }
        if($open_vykaz_as_dekanat && Auth::user()->can('openAsDekanat', $organizationVykaz)) {
            if($organizationVykaz->isOrdinariatClosed()) {
                return redirect()->back()->with("error", "Nie je možné otvoriť, už je uzatvorený výkaz za ordinariát.");
            }
            $organizationVykaz->closed_at_dekanat = null;
            $organizationVykaz->closed_by_dekanat = null;
            $organizationVykaz->save();
        }
        if($open_vykaz_as_ordinariat && Auth::user()->can('openAsOrdinariat', $organizationVykaz)) {
            $organizationVykaz->closed_at_ordinariat = null;
            $organizationVykaz->closed_by_ordinariat = null;
            $organizationVykaz->save();
        }
        
        return Redirect::to('organizations/' . $organizationVykaz->organization->id);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\OrganizationVykaz $organizationVykaz
     * @return \Illuminate\Http\Response
     */
    public function destroy(OrganizationVykaz $organizationVykaz)
    {
        $organizationVykaz->metas()->delete();
        $organizationVykaz->delete();
        return redirect()->back();
    }
}
