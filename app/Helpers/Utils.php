<?php

namespace App\Helpers;

use App\Organization;
use Illuminate\Support\Facades\Session;

class Utils
{
    public static function timeToHuman($seconds)
    {
        $return = [];
//        $days = floor($seconds / 86400);
        $days = 0;
//        $hours = floor(($seconds % 86400) / 3600);
        $hours = floor($seconds / 3600);
        $minutes = floor((($seconds % 86400) % 3600) / 60);
        $seconds = floor((($seconds % 86400) % 3600) % 60);
        if ($days > 0) {
            $return[] = $days;
        }
        if (!empty($return) || $hours > 0) {
            $return[] = sprintf('%02d', $hours);
        }
        $return[] = sprintf('%02d', $minutes);
        $return[] = sprintf('%02d', $seconds);
        return join(':', $return);
    }

    public static function bytesToHuman($bytes)
    {
        $units = ['B', 'KiB', 'MiB', 'GiB', 'TiB', 'PiB'];

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        return round($bytes, 2) . ' ' . $units[$i];
    }

    public static function request_snapshot()
    {
        $data = [];
        $data['ENV'] = config('app.env');
        $data['URL'] = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . '://' . $_SERVER['HTTP_HOST'] . '/' . $_SERVER['REQUEST_URI'];
        $data['_SERVER'] = $_SERVER;
        $data['_REQUEST'] = $_REQUEST;
        $data['old'] = old();
        return json_encode($data);
    }

    public static function audit_to_text($audit)
    {
        $events = ['created' => 'vytvorenie', 'deleted' => 'vymazanie', 'updated' => 'úprava'];
        $messages = [];
        $msg = $audit->auditable_type . ' has not defined audit message';
        if (class_exists($audit->auditable_type)) {
            $object = $audit->auditable_type::withTrashed()->find($audit->auditable_id);
            if (!is_null($object) && method_exists($object, 'activityMessage')) {
                $msg = $object->activityMessage($audit);
            }
        } else {
            $msg = $audit->auditable_type . ' class does not exist.';
        }
        $e = $audit->event;
        if (isset($events[$audit->event])) {
            $e = $events[$audit->event];
        }
        if ($msg != 'NO_AUDIT_MESSAGE') {
            $messages = [
                'id' => $audit->id,
                'datetime' => date('d.m.Y H:i:s', strtotime($audit->created_at)),
                'user' => $audit->user()->withTrashed()->first(),
                'event' => $e,
                'message' => $msg
            ];
        }
        return $messages;
    }

    public static function getFilter($key, $default = [])
    {
        $FF = request()->get('filter')[$key] ?? null;

        $filter = [
            'remembered' => false,
            'data' => $FF ?? $default,
            'default' => $default,
            'key' => $key
        ];

        unset($filter['data']['remembered']);
        unset($filter['data']['cancel']);

        if (!empty($FF)) {
            if (isset($FF['remembered'])) {
                $filter['remembered'] = true;
                Session::put($key, $filter['data']);
            } else {
                Session::remove($key);
            }
        }
        if (isset($FF['cancel'])) {
            Session::remove($key);
        }

        if (empty($filter['data'])) {
            $filter['data'] = Session::get($key);
            if (!empty($filter['data'])) {
                $filter['remembered'] = true;
            }
        }

        return $filter;
    }

    public static function getOrder($key, $default = [])
    {
        $o = request()->get('order')[$key] ?? [];
        $order['by'] = $o['by'] ?? ($default['by'] ?? 'id');
        $order['dir'] = $o['dir'] ?? ($default['dir'] ?? 'desc');
        $order['dir_new'] = $order['dir'] == 'desc' ? 'asc' : 'desc';
        $order['key'] = $key;
        $order['default'] = $default;
        return $order;
    }

    public static function getBbdieceza()
    {
        return Organization::findOrFail(998);
    }
}