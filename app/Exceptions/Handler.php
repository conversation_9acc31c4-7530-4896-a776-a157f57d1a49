<?php

namespace App\Exceptions;

use App\Mail\ExceptionOccured;
use Exception;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Symfony\Component\ErrorHandler\Exception\FlattenException;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function report(\Throwable $exception)
    {
        if ($this->shouldReport($exception)) {
            $this->sendEmail($exception); // sends an email
        }
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Throwable $exception
     * @return \Illuminate\Http\Response
     */
    public function render($request, \Throwable $exception)
    {
        return parent::render($request, $exception);
    }

    /**
     * Sends an email to the developer about the exception.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function sendEmail(\Throwable $exception)
    {
        try {
            if (env('APP_ENV') != 'dev') {
//                $e = FlattenException::create($exception);
//                \Illuminate\Contracts\Debug\ExceptionHandler::
//                ExceptionHandler::
//                $handler = new ExceptionHandler();
                $html = $exception->getMessage();
                $user = Auth::user();
                Mail::to(explode(';',env('ERROR_REPORTING_EMAIL')))->send(new ExceptionOccured($html, $user));
            }
        } catch (Exception $ex) {

        }
    }
}
