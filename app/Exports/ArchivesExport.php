<?php

namespace App\Exports;

use App\Archive;
use Maatwebsite\Excel\Concerns\FromArray;

class ArchivesExport implements FromArray
{
    private $rows = [];
    private $collection_id = null;

    public function __construct($rows, $collection_id)
    {
        $this->rows = $rows;
        $this->collection_id = $collection_id;
    }

    public function array(): array
    {
        $cols = Archive::collections_detail_cols($this->collection_id);
        $first_row = ['Číslo krabice',
            'Číslo dokumentu v roku pôvodu',
            'Archívne číslo',
            'Typ',
            'Fond',
            'Kategória',
            'Organizácia',
            'Názov'];
        if (!empty($cols)) {
            $first_row = [];
            foreach ($cols['cols'] as $col) {
                $first_row[] = $col;
            }
        }
        $first_row = array_merge($first_row, [
            'Od',
            'Do',
            'Jazyk prameňa',
            'Miesto ulo<PERSON> (miestnosť)',
            'Miesto ulož<PERSON> (regál)',
            '<PERSON>esto ulož<PERSON> (polica)',
            'Úplný záznam',
            'Poznámka',
        ]);

        $rows = [$first_row];

        foreach ($this->rows as $row) {
            $next_row = [$row->box_number,
                $row->book_number,
                $row->archive_number,
                $row->type->name ?? '',
                $row->collection->name ?? '',
                $row->category->name ?? '',
                implode(', ', $row->organizations()->pluck('name')->toArray()),
                $row->name];
            if (!empty($cols)) {
                $next_row = [];
                foreach ($cols['cols'] as $key => $col) {
                    $v = $row->$key;
                    if ($key == 'type_id') {
                        $v = $row->type->name ?? '';
                    }
                    if ($key == 'collection_id') {
                        $v = $row->collection->name ?? '';
                    }
                    if ($key == 'category_id') {
                        $v = $row->category->name ?? '';
                    }
                    if ($key == 'organization_id') {
                        $v = implode(', ', $row->organizations()->pluck('name')->toArray());
                    }
                    $next_row[] = $v;
                }
            }
            $next_row = array_merge($next_row, [
                $row->date_from,
                $row->date_to,
                implode(', ', $row->languages()->pluck('name')->toArray()),
                $row->storage_room->name ?? '',
                $row->storage_rack_number,
                $row->storage_shelf_number,
                $row->is_done ? 'Áno' : 'Nie',
                $row->description
            ]);

            $rows[] = [
                $next_row
            ];
        }
        return $rows;
    }
}
