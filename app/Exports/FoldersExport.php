<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;

class FoldersExport implements FromArray
{
    private $mails = [];

    public function __construct($folders)
    {
        $this->folders = $folders;
    }

    public function array(): array
    {
        $rows = [[
            '<PERSON><PERSON><PERSON>',
            '<PERSON>ok',
            'Ty<PERSON>',
            '<PERSON><PERSON>zo<PERSON>',
            'Súvisiac<PERSON>',
            '<PERSON><PERSON><PERSON>',
            'Partner',
            '<PERSON>tist<PERSON>',
            'Dátum založenia',
            'Dátum ukončenia',
            'Arch<PERSON>v<PERSON> číslo',
            '<PERSON>z<PERSON>m<PERSON>',
        ]];

        foreach ($this->folders as $folder) {
            $mails = [];
            foreach ($folder->mails as $m) {
                $mails[] = $m->number . '/' . $m->year;
            }
            $rows[] = [
                $folder->number,
                $folder->year,
                $folder->type ? $folder->type->name : '',
                $folder->name,
                $folder->folder ? $folder->folder->fullname() : '',
                join(', ', $mails),
                $folder->partner ? $folder->partner->name : '',
                $folder->protistrana,
                $folder->date_opened,
                $folder->date_closed,
                $folder->archive_number,
                $folder->notes,
            ];
        }
        return $rows;
    }
}
