<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;

class EntriesExport implements FromArray
{
    private $entries = [];

    public function __construct($entries)
    {
        $this->entries = $entries;
    }

    public function array(): array
    {
        $rows = [[
            '<PERSON><PERSON><PERSON>',
            'Pou<PERSON>ľ',
            'Od',
            'Do',
            'Popis',
            'Odpracované',
        ]];

        foreach ($this->entries as $entry) {
            $rows[] = [
                $entry->task->title,
                $entry->user->fullname(),
                $entry->date_from,
                $entry->date_to,
                $entry->description,
                $entry->trackedTime(),
            ];
        }
        return $rows;
    }
}
