version: '3.7'

services:
  app:
    # # Docker image for app service is build separately to make the
    # # docker-compose more time efficiet (see Dockerfile initial comment)
    # # To change the fajnlamp varsion it is enough to put propper version 
    # # here below (developmentrunsk/fajnlamp:5.6, developmentrunsk/fajnlamp:7.3, developmentrunsk/fajnlamp:8.0, ...)
    # build:
    #   context: ..
    #   dockerfile: .devcontainer/Dockerfile.fajnlamp80
    image: developmentrunsk/fajnlamp:8.0
    # To make it run on ARM processors too
    platform: linux/amd64
    environment:
      - TZ=${TZ:-Europe/Bratislava}
      # to make `npm start` hot reload functional (e.g. for React app development)
      # set the mapped port on host machine
      - WDS_SOCKET_PORT=${APP_PORT_3000_MAPPING:-50323}
    ports:
      - ${APP_PORT_80_MAPPING:-50023}:80
      # E.g. for `npm start`
      - ${APP_PORT_3000_MAPPING:-50323}:3000
      # E.g. for `gitbook serve`
      - ${APP_PORT_4000_MAPPING:-50423}:4000
      # E.g. for `hg serve`
      - ${APP_PORT_8000_MAPPING:-50823}:8000
    volumes:
      # Mount project folder (`:cached` improves the performance on Mac and Windows)
      - ..:/var/www/html:cached
      # Mount shared node_modules folder.
      # If local node_modules folder does not exist, then docker will create it.
      - ../../node_modules:/var/www/node_modules:cached
      # Mount local user .hgrc and .gitconfig files to encuse the same user for Mercurial and Git.
      # ATTENTION: If this does not work for WINDOWS, then an `.env` file with `HOME=C:\path\to\user\home`
      # must be created in the .devcontainer folder.
      - $HOME/.hgrc:/home/<USER>/.hgrc
      - $HOME/.gitconfig:/home/<USER>/.gitconfig
      # Store image bash history in named volume
      - app-bash-history:/home/<USER>/.bash_history
    depends_on:
      - db
      
  db:
    image: mysql:5.7
    # To make it run on ARM processors too
    platform: linux/amd64
    environment:
      - TZ=${TZ:-Europe/Bratislava}
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=project
    # Be sure to avoid following sql modes: ONLY_FULL_GROUP_BY, STRICT_TRANS_TABLES and STRICT_ALL_TABLES
    command: --sql-mode="NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION"
    volumes:
      - mysql_data:/var/lib/mysql

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    # To make it run on ARM processors too
    platform: linux/amd64
    environment:
      - TZ=${TZ:-Europe/Bratislava}
      - PMA_HOST=db
      - PMA_PORT=3306
    ports:
      - ${PHPMYADMIN_PORT_80_MAPPING:-51023}:80
    volumes:
      # Change PMA session lifetime to 24h
      - ./phpmyadmin/session.ini:/usr/local/etc/php/conf.d/session.ini
    depends_on:
      - db

volumes:
  app-bash-history:
  mysql_data:
